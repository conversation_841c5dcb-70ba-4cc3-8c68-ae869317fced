<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.pxb7.mall.workorder</groupId>
        <artifactId>work-order</artifactId>
        <version>${revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>work-order-app</artifactId>
    <packaging>jar</packaging>
    <name>work-order-app</name>

    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.pxb7.mall.workorder</groupId>
            <artifactId>work-order-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.pxb7.mall.workorder</groupId>
            <artifactId>work-order-domain</artifactId>
        </dependency>

        <dependency>
            <groupId>com.pxb7.mall.trade</groupId>
            <artifactId>intelligent-delivery-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba.cola</groupId>
            <artifactId>cola-component-catchlog-starter</artifactId>
        </dependency>

        <!-- JSR 303 Validation -->
        <dependency>
            <groupId>org.hibernate.validator</groupId>
            <artifactId>hibernate-validator</artifactId>
        </dependency>

        <dependency>
            <groupId>jakarta.el</groupId>
            <artifactId>jakarta.el-api</artifactId>
        </dependency>
        <!-- JSR 303 Validation End-->

        <!-- powerJob -->
        <dependency>
            <groupId>tech.powerjob</groupId>
            <artifactId>powerjob-worker-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pxb7.mall.components</groupId>
            <artifactId>sentinel-component-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>

    </dependencies>
</project>
