<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pxb7.mall.workorder.infra.repository.db.mapper.RemindPlanRecordMapper">
    <resultMap id="BaseResultMap" type="com.pxb7.mall.workorder.infra.repository.db.entity.RemindPlanRecord">
        <result column="id" jdbcType="BIGINT" property="id"/>
        <result column="plan_record_id" jdbcType="VARCHAR" property="planRecordId"/>
        <result column="biz_type" jdbcType="INTEGER" property="bizType"/>
        <result column="biz_id" jdbcType="VARCHAR" property="bizId"/>
        <result column="remind_id" jdbcType="VARCHAR" property="remindId"/>
        <result column="which_time" jdbcType="INTEGER" property="whichTime"/>
        <result column="is_last_before_timeout" jdbcType="BOOLEAN" property="lastBeforeTimeout"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="remind_time" jdbcType="TIMESTAMP" property="remindTime"/>
        <result column="plan_rule_id" jdbcType="BIGINT" property="planRuleId"/>
        <result column="remind_plan_id" jdbcType="BIGINT" property="remindPlanId"/>
        <result column="remind_sub_plan_id" jdbcType="BIGINT" property="remindSubPlanId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_user_id" jdbcType="VARCHAR" property="createUserId"/>
        <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId"/>
        <result column="is_deleted" jdbcType="BOOLEAN" property="deleted"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into
        remind_plan_record(plan_record_id,biz_type,biz_id,remind_id,which_time,is_last_before_timeout,status,remind_time,plan_rule_id,remind_plan_id,remind_sub_plan_id,create_user_id,update_user_id,env_profile)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.planRecordId},#{entity.bizType},#{entity.bizId},#{entity.remindId},#{entity.whichTime},#{entity.lastBeforeTimeout},#{entity.status},#{entity.remindTime},#{entity.planRuleId},#{entity.remindPlanId},#{entity.remindSubPlanId},#{entity.createUserId},#{entity.updateUserId},#{entity.envProfile})
        </foreach>
    </insert>

    <update id="batchUpdateStatusByIds">
        update remind_plan_record
        set status = #{status}
        where id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>
</mapper>

