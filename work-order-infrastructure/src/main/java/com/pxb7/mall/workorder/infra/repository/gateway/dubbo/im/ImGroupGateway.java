package com.pxb7.mall.workorder.infra.repository.gateway.dubbo.im;

import com.alibaba.cola.dto.SingleResponse;
import com.pxb7.mall.im.client.api.ImGroupServiceI;
import com.pxb7.mall.im.client.dto.response.GroupInfoRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Repository;

@Repository
@Slf4j
public class ImGroupGateway {

    @DubboReference
    private ImGroupServiceI imGroupService;


    /**
     *
     * 查询订单对应的聊天群组
     * @param orderId
     * @return
     */
    public GroupInfoRespDTO queryGroupInfoByOrderId(String orderId) {
        try {
            SingleResponse<GroupInfoRespDTO> response = imGroupService.queryGroupInfoByOrderId(orderId);
            if (response != null && response.isSuccess()) {
                return response.getData();
            }
            log.error("Fail to query group info by order id. orderId:{}", orderId);
            return null;
        } catch (Exception e) {
            log.error("Fail to query group info by order id. orderId:{}", orderId, e);
            return null;
        }
    }

}
