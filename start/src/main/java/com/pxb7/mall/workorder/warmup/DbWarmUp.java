package com.pxb7.mall.workorder.warmup;

import com.pxb7.mall.components.warmup.PxWarmUpPostProcess;
import com.pxb7.mall.workorder.domain.service.RemindPlanGameConfigDomainService;
import com.pxb7.mall.workorder.domain.service.RemindWorkOrderDomainService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/09/10 17:55
 **/
@Slf4j
@Component
public class DbWarmUp implements PxWarmUpPostProcess {

    @Resource
    private RemindWorkOrderDomainService remindWorkOrderDomainService;
    @Resource
    private RemindPlanGameConfigDomainService remindPlanGameConfigDomainService;

    @Override
    public void warmUpProcess() {

        long baseWorkOrderId = 1000644475658L;
        long baseRemindId = 127464447508480L;

        // 循环调用16次接口，每次workOrderId和remindId都累加1
        for (int i = 0; i < 16; i++) {
            long workOrderId = baseWorkOrderId + i;
            long remindId = baseRemindId + i;
            log.info("第{}次调用接口，workOrderId: {}, remindId: {}", i + 1, workOrderId, remindId);
            try {
                remindWorkOrderDomainService.findByWorkOrderId("WO" + workOrderId, String.valueOf(remindId));
                remindPlanGameConfigDomainService.findById(workOrderId);
            } catch (Exception e) {
                log.error("调用remindWorkOrderDomainService.findByWorkOrderId失败，workOrderId: {}, remindId: {}", workOrderId, remindId, e);
            }
        }
    }

    @Override
    public int loopExecNum() {
        return 1;
    }

    @Override
    public boolean loopExecute() {
        return true;
    }

}
