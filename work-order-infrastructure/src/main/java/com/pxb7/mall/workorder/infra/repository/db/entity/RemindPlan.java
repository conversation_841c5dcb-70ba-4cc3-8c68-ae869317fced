package com.pxb7.mall.workorder.infra.repository.db.entity;

import java.io.Serializable;
import java.time.*;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import com.baomidou.mybatisplus.annotation.*;

/**
 * 提醒服务预警计划(RemindPlan)实体类
 *
 * <AUTHOR>
 * @since 2025-03-24 21:09:47
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "remind_plan")
@ToString
public class RemindPlan implements Serializable {
    private static final long serialVersionUID = 222565893080655296L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 预警计划名
     */
    @TableField(value = "plan_name")
    private String planName;
    /**
     * 服务类型，1:账号交付服务，2:商品工单服务，3:售后工单，4:客诉工单
     */
    @TableField(value = "service_type")
    private Integer serviceType;
    /**
     * 勿扰时段，{"startTime":"22:00","endTime":"08:00"}
     */
    @TableField(value = "not_disturb_period")
    private String notDisturbPeriod;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;
    /**
     * 创建人
     */
    @TableField(value = "create_user_id")
    private String createUserId;
    /**
     * 更新人
     */
    @TableField(value = "update_user_id")
    private String updateUserId;
    /**
     * 是否删除 0:否, 1:是
     */
    @TableLogic
    @TableField(value = "is_deleted")
    private Boolean deleted;


}

