package com.pxb7.mall.workorder.app.model;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import javax.annotation.Nullable;

@Getter
@Setter
@Accessors(chain = true)
@ToString
public class AssPlanGameConfigDTO {

    /**
     * 预警游戏配置Id
     *
     */
    @Nullable
    private Long id;

    /**
     *  游戏ID
     */
    @NotNull(message = "游戏ID不能为空")
    private String  gameId;

    /**
     * 游戏名称
     */
    @NotNull(message = "游戏名称不能为空")
    private String gameName;

    /**
     *  游戏完成时间,{"hours":"10","minutes":"30"}
     */
    @Valid
    //@NotNull(message = "预期完成时间不能为空")
    private TimeConfigDTO expectCompleteTimeConfig;

    /**
     *  im客服端倒计时,{"hours":"10","minutes":"30"}
     */
    @Valid
    //@NotNull(message = "im客服端倒计时不能为空")
    private TimeConfigDTO imCountDownTimeConfig;

}
