package com.pxb7.mall.workorder.app.service;


import java.util.List;

import com.pxb7.mall.workorder.app.mapping.RemindSubPlanAppMapping;
import com.pxb7.mall.workorder.app.model.RemindSubPlanReqDTO;
import com.pxb7.mall.workorder.app.model.RemindSubPlanRespDTO;
import com.pxb7.mall.workorder.domain.model.RemindSubPlanBO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import com.pxb7.mall.workorder.domain.model.RemindSubPlanReqBO;
import com.pxb7.mall.workorder.domain.model.RemindSubPlanRespBO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pxb7.mall.workorder.domain.service.RemindSubPlanDomainService;


/**
 * 提醒服务预警子计划app服务
 *
 * <AUTHOR>
 * @since 2025-03-24 21:12:18
 */
@Service
public class RemindSubPlanAppService {

    @Resource
    private RemindSubPlanDomainService remindSubPlanDomainService;

    public boolean insert(RemindSubPlanReqDTO.AddDTO param) {
        RemindSubPlanBO addBO = RemindSubPlanAppMapping.INSTANCE.remindSubPlanDTO2AddBO(param);
        return remindSubPlanDomainService.insert(addBO);
    }

    public boolean update(RemindSubPlanReqDTO.UpdateDTO param) {
        RemindSubPlanBO updateBO = RemindSubPlanAppMapping.INSTANCE.remindSubPlanDTO2UpdateBO(param);
        return remindSubPlanDomainService.update(updateBO);
    }

    public boolean deleteById(RemindSubPlanReqDTO.DelDTO param) {
        RemindSubPlanReqBO.DelBO delBO = RemindSubPlanAppMapping.INSTANCE.remindSubPlanDTO2DelBO(param);
        return remindSubPlanDomainService.deleteById(delBO);
    }

    public RemindSubPlanRespDTO.DetailDTO findById(Long id) {
        RemindSubPlanRespBO.DetailBO detailBO = remindSubPlanDomainService.findById(id);
        return RemindSubPlanAppMapping.INSTANCE.remindSubPlanBO2DetailDTO(detailBO);
    }

    public List<RemindSubPlanRespDTO.DetailDTO> list(RemindSubPlanReqDTO.SearchDTO param) {
        RemindSubPlanReqBO.SearchBO searchBO = RemindSubPlanAppMapping.INSTANCE.remindSubPlanDTO2SearchBO(param);
        List<RemindSubPlanRespBO.DetailBO> list = remindSubPlanDomainService.list(searchBO);
        return RemindSubPlanAppMapping.INSTANCE.remindSubPlanBO2ListDTO(list);
    }

    public Page<RemindSubPlanRespDTO.DetailDTO> page(RemindSubPlanReqDTO.PageDTO param) {
        RemindSubPlanReqBO.PageBO pageBO = RemindSubPlanAppMapping.INSTANCE.remindSubPlanDTO2PageBO(param);
        Page<RemindSubPlanRespBO.DetailBO> page = remindSubPlanDomainService.page(pageBO);
        return RemindSubPlanAppMapping.INSTANCE.remindSubPlanBO2PageDTO(page);
    }

}

