package com.pxb7.mall.workorder.domain.service;

import com.alibaba.fastjson2.JSON;
import com.pxb7.mall.im.client.dto.request.card.CommandMsgReqDTO;
import com.pxb7.mall.im.client.dto.request.card.enums.BusinessMsgType;
import com.pxb7.mall.workorder.client.enums.BizTypeEnum;
import com.pxb7.mall.workorder.client.enums.CompleteStatusEnum;
import com.pxb7.mall.workorder.client.enums.PlanExecuteStatusEnum;
import com.pxb7.mall.workorder.client.enums.TimeoutStatusEnum;
import com.pxb7.mall.workorder.client.message.RemindPlanMessage;
import com.pxb7.mall.workorder.domain.model.BaseRemindRecordBO;
import com.pxb7.mall.workorder.domain.model.ImGroupCmdMsgBO;
import com.pxb7.mall.workorder.domain.model.TimeoutInfoRespBO;
import com.pxb7.mall.workorder.infra.constant.RedisKeyConstants;
import com.pxb7.mall.workorder.infra.repository.gateway.dubbo.im.ImMsgGateway;
import com.pxb7.mall.workorder.infra.util.RedissonUtils;
import com.pxb7.mall.workorder.infra.util.SpringUtils;
import com.pxb7.mall.workorder.infra.util.TimeUtils;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @date 2025/4/29 17:47
 */
public abstract class BaseRemindDomainService {

    @Resource
    private ImMsgGateway imMsgGateway;

    /**
     * 获取预警业务类型
     * @return
     */
    abstract BizTypeEnum getBizType();


    /**
     * 处理预警消息发送
     * @param remindPlanMessage
     */
    public abstract PlanExecuteStatusEnum handleRemindMessage(RemindPlanMessage remindPlanMessage);

    /**
     * 更新即将超时状态
     * @param remindPlanMessage
     */
    public abstract void updateWillTimeOutStatus(RemindPlanMessage remindPlanMessage);

    /**
     * 执行超时状态更新任务
     * @param databaseName
     */
    public abstract void executeUpdateTimeoutStatusTask(String databaseName);

    /**
     * 预警群组超时消息
     * @param imGroupCmdMsgList
     */
    protected void moveGroupToTimeoutList(List<ImGroupCmdMsgBO> imGroupCmdMsgList) {
        CompletableFuture.runAsync(
                () -> {
                    if (CollectionUtils.isEmpty(imGroupCmdMsgList)) {
                        return;
                    }
                    //超时状态
                    TimeoutStatusEnum status = imGroupCmdMsgList.get(0).getStatus();
                    //操作类型
                    String operation = status == TimeoutStatusEnum.TIMEOUT ?
                            BusinessMsgType.TIMEOUT_GROUP.getValue() : BusinessMsgType.WILL_TIMEOUT_GROUP.getValue();

                    List<String> groupTimoutKeys = new ArrayList<>();
                    for (ImGroupCmdMsgBO imGroupCmdMsg : imGroupCmdMsgList) {
                        if (StringUtils.isBlank(imGroupCmdMsg.getGroupId())
                                || StringUtils.isBlank(imGroupCmdMsg.getTargetUserId())) {
                            continue;
                        }
                        CommandMsgReqDTO msgReqDTO = new CommandMsgReqDTO()
                                .setTargetUserId(imGroupCmdMsg.getTargetUserId())
                                .setContent(getCmdContent(imGroupCmdMsg.getGroupId(), imGroupCmdMsg.getTargetUserId()))
                                .setOperation(operation);
                        imMsgGateway.sendCmdMsg(msgReqDTO);
                        groupTimoutKeys.add(String.format(RedisKeyConstants.TIMEOUT_GROUP_KEY,
                                imGroupCmdMsg.getGroupId(), imGroupCmdMsg.getTargetUserId()));
                    }
                    //redis批量设置群组超时状态
                    Map<String, Integer> groupTimoutMap = new HashMap<>();
                    for (String groupTimoutKey : groupTimoutKeys) {
                        groupTimoutMap.put(groupTimoutKey, status.getCode());
                    }
                    RedissonUtils.batchSet(groupTimoutMap, Duration.ofDays(180L));

                }, SpringUtils.getBean("asyncTaskExecutor")
        );
    }

    /**
     * 获取命令消息内容
     * @param groupId
     * @param customerCareId
     * @return
     */
    private String getCmdContent(String groupId, String customerCareId) {
        TimeoutInfoRespBO timeoutInfo = getTimeoutInfo(groupId, customerCareId);
        Map<String, Object> contentMap = new HashMap<>();
        contentMap.put("groupId", groupId);
        if (Objects.nonNull(timeoutInfo.getTimeoutType())) {
            contentMap.put("timeoutType", timeoutInfo.getTimeoutType());
        }
        if (Objects.nonNull(timeoutInfo.getTimeoutTime())) {
            contentMap.put("timeoutTime", timeoutInfo.getTimeoutTime());
        }
        return JSON.toJSONString(contentMap);
    }

    /**
     * 查询群组中超时信息
     * @param groupId 群组id
     * @param customerCareId 客服id
     * @return
     */
    public TimeoutInfoRespBO getTimeoutInfo(String groupId, String customerCareId) {
        LocalDateTime now = LocalDateTime.now();
        TimeoutInfoRespBO timeOutInfo = new TimeoutInfoRespBO();
        timeOutInfo.setTimeoutType(TimeoutStatusEnum.NONE.getCode());
        timeOutInfo.setBizType(getBizType().getType());
        //通过房间查询账号交付预警记录
        BaseRemindRecordBO remindRecordBO = searchByGroupId(groupId, customerCareId);
        if (Objects.isNull(remindRecordBO)) {
            return timeOutInfo;
        }
        if (!Objects.equals(CompleteStatusEnum.IN_PROCESS.getCode(), remindRecordBO.getCompleteStatus())) {
            return timeOutInfo;
        }
        if (Objects.isNull(remindRecordBO.getImCountDownTime())
                || remindRecordBO.getImCountDownTime().isAfter(now)) {
            return timeOutInfo;
        }

        LocalDateTime expectCompleteTime = remindRecordBO.getExpectCompleteTime();
        //未超时
        if (expectCompleteTime.isAfter(now)) {
            timeOutInfo.setTimeoutTime(TimeUtils.leftSeconds(expectCompleteTime));
            timeOutInfo.setTimeoutType(TimeoutStatusEnum.WILL_TIMEOUT.getCode());
        } else {
            timeOutInfo.setTimeoutTime(TimeUtils.pastSeconds(expectCompleteTime));
            timeOutInfo.setTimeoutType(TimeoutStatusEnum.TIMEOUT.getCode());
        }

        return timeOutInfo;
    }

    /**
     * 通过房间查询预警记录
     * @param groupId
     * @param customerCareId
     * @return
     */
    public abstract BaseRemindRecordBO searchByGroupId(String groupId, String customerCareId);

}
