package com.pxb7.mall.workorder.infra.repository.db;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pxb7.mall.workorder.infra.model.RemindPlanReqPO;
import com.pxb7.mall.workorder.infra.repository.db.entity.RemindPlan;

/**
 * 提醒服务预警计划(RemindPlan)表服务接口
 *
 * <AUTHOR>
 * @since 2025-03-24 21:09:54
 */
public interface RemindPlanRepository extends IService<RemindPlan> {


    boolean insert(RemindPlanReqPO.AddPO param);


    boolean update(RemindPlanReqPO.UpdatePO param);


    boolean deleteById(RemindPlanReqPO.DelPO param);

    RemindPlan findById(Long id);

    List<RemindPlan> list(RemindPlanReqPO.SearchPO param);

    Page<RemindPlan> page(RemindPlanReqPO.PagePO param);

    long countByPlanName(String palanName,Integer serviceType);

}

