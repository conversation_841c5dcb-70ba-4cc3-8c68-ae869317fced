package com.pxb7.mall.workorder.domain.service;


import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import com.pxb7.mall.workorder.domain.model.RemindSubPlanReqBO;
import com.pxb7.mall.workorder.domain.model.RemindSubPlanRespBO;
import com.pxb7.mall.workorder.infra.model.RemindSubPlanReqPO;
import com.pxb7.mall.workorder.infra.repository.db.RemindSubPlanRepository;
import com.pxb7.mall.workorder.infra.repository.db.entity.RemindSubPlan;
import com.pxb7.mall.workorder.domain.model.RemindSubPlanBO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import com.pxb7.mall.workorder.domain.mapping.RemindSubPlanDomainMapping;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;


/**
 * 提醒服务预警子计划domain服务
 *
 * <AUTHOR>
 * @since 2025-03-24 21:11:14
 */
@Service
public class RemindSubPlanDomainService {

    @Resource
    private RemindSubPlanRepository remindSubPlanRepository;

    public boolean insert(RemindSubPlanBO param) {
        RemindSubPlanReqPO.AddPO addPO = RemindSubPlanDomainMapping.INSTANCE.remindSubPlanBO2AddPO(param);
        return remindSubPlanRepository.insert(addPO);
    }


    public boolean saveBatch(List<RemindSubPlanBO> params) {
        List< RemindSubPlanReqPO.AddPO> addPOList = new ArrayList<>();
        for (RemindSubPlanBO param : params) {
            RemindSubPlanReqPO.AddPO addPO = RemindSubPlanDomainMapping.INSTANCE.remindSubPlanBO2AddPO(param);
            addPOList.add(addPO);
        }
        return remindSubPlanRepository.saveBatch(addPOList);
    }

    public boolean update(RemindSubPlanBO param) {
        RemindSubPlanReqPO.UpdatePO updatePO = RemindSubPlanDomainMapping.INSTANCE.remindSubPlanBO2UpdatePO(param);
        return remindSubPlanRepository.update(updatePO);
    }

    public boolean deleteById(RemindSubPlanReqBO.DelBO param) {
        RemindSubPlanReqPO.DelPO delPO = RemindSubPlanDomainMapping.INSTANCE.remindSubPlanBO2DelPO(param);
        return remindSubPlanRepository.deleteById(delPO);
    }

    public boolean deleteByIds(List<Long> ids) {
        return remindSubPlanRepository.removeByIds(ids);
    }

    public RemindSubPlanRespBO.DetailBO findById(Long id) {
        RemindSubPlan entity = remindSubPlanRepository.findById(id);
        return RemindSubPlanDomainMapping.INSTANCE.remindSubPlanPO2DetailBO(entity);

    }

    public List<RemindSubPlanRespBO.DetailBO> list(RemindSubPlanReqBO.SearchBO param) {
        RemindSubPlanReqPO.SearchPO searchPO = RemindSubPlanDomainMapping.INSTANCE.remindSubPlanBO2SearchPO(param);
        List<RemindSubPlan> list = remindSubPlanRepository.list(searchPO);
        return RemindSubPlanDomainMapping.INSTANCE.remindSubPlanPO2ListBO(list);
    }

    public Page<RemindSubPlanRespBO.DetailBO> page(RemindSubPlanReqBO.PageBO param) {
        RemindSubPlanReqPO.PagePO pagePO = RemindSubPlanDomainMapping.INSTANCE.remindSubPlanBO2PagePO(param);
        Page<RemindSubPlan> page = remindSubPlanRepository.page(pagePO);
        return RemindSubPlanDomainMapping.INSTANCE.remindSubPlanPO2PageBO(page);
    }

    public List<RemindSubPlanBO> getRemindSubPlanList(Long remindPlanId) {
        if (null == remindPlanId || remindPlanId < 1) {
            return Collections.emptyList();
        }
        RemindSubPlanReqPO.SearchPO searchPO = new RemindSubPlanReqPO.SearchPO();
        searchPO.setRemindPlanId(remindPlanId);
        List<RemindSubPlan> list = remindSubPlanRepository.list(searchPO);
        return RemindSubPlanDomainMapping.INSTANCE.remindSubPlan2ListBO(list);
    }

    public List<RemindSubPlanBO> getRemindSubPlanList(List<Long> remindPlanIds) {
        if (null == remindPlanIds || remindPlanIds.isEmpty()) {
            return Collections.emptyList();
        }
        RemindSubPlanReqPO.SearchPO searchPO = new RemindSubPlanReqPO.SearchPO();
        searchPO.setRemindPlanIds(remindPlanIds);
        List<RemindSubPlan> list = remindSubPlanRepository.list(searchPO);
        return RemindSubPlanDomainMapping.INSTANCE.remindSubPlan2ListBO(list);
    }

}

