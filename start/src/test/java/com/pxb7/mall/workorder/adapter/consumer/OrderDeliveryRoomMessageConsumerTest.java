package com.pxb7.mall.workorder.adapter.consumer;

import com.pxb7.mall.trade.order.client.dto.model.OrderDeliveryRoomChangeMessage;
import com.pxb7.mall.workorder.app.service.RemindDeliveryProductAppService;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

class OrderDeliveryRoomMessageConsumerTest {

    @InjectMocks
    private OrderDeliveryRoomMessageConsumer consumer;

    @Mock
    private RemindDeliveryProductAppService deliveryProductAppService;

    @Mock
    private MessageView messageView;

    @Mock
    private OrderDeliveryRoomChangeMessage messageBody;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testConsume_orderItemIdIsNull_returnSuccess() {
        // Arrange
        when(messageBody.getOrderItemId()).thenReturn(null);

        // Act
        ConsumeResult result = consumer.consume(messageView, messageBody);

        // Assert
        assertEquals(ConsumeResult.SUCCESS, result);
        verifyNoInteractions(deliveryProductAppService); // 不应调用 service
    }

    @Test
    void testConsume_orderItemIdIsEmptyString_returnSuccess() {
        // Arrange
        when(messageBody.getOrderItemId()).thenReturn("");

        // Act
        ConsumeResult result = consumer.consume(messageView, messageBody);

        // Assert
        assertEquals(ConsumeResult.SUCCESS, result);
        verifyNoInteractions(deliveryProductAppService);
    }

    @Test
    void testConsume_orderItemIdValid_callServiceAndReturnSuccess() {
        // Arrange
        String orderId = "123456";
        when(messageBody.getOrderItemId()).thenReturn(orderId);

        // Act
        ConsumeResult result = consumer.consume(messageView, messageBody);

        // Assert
        assertEquals(ConsumeResult.SUCCESS, result);
        verify(deliveryProductAppService).updateGroupAndCustomerCare(orderId);
    }

    @Test
    void testConsume_serviceThrowsException_returnFailure() {
        // Arrange
        String orderId = "123456";
        when(messageBody.getOrderItemId()).thenReturn(orderId);
        doThrow(new RuntimeException("Test Exception")).when(deliveryProductAppService).updateGroupAndCustomerCare(orderId);

        // Act
        ConsumeResult result = consumer.consume(messageView, messageBody);

        // Assert
        assertEquals(ConsumeResult.FAILURE, result);
        verify(deliveryProductAppService).updateGroupAndCustomerCare(orderId);
    }
}
