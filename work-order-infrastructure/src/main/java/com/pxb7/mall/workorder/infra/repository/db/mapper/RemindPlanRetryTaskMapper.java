package com.pxb7.mall.workorder.infra.repository.db.mapper;

import com.pxb7.mall.workorder.infra.repository.db.entity.RemindPlanRetryTask;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 生成提醒计划异常重试表(RemindPlanRetryTask)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-05-06 20:53:52
 */
@Mapper
public interface RemindPlanRetryTaskMapper extends BaseMapper<RemindPlanRetryTask> {
    /**
     * 批量新增数据
     *
     * @param entities List<RemindPlanRetryTask> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<RemindPlanRetryTask> entities);

}

