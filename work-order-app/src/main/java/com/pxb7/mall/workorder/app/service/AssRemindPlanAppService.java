package com.pxb7.mall.workorder.app.service;

import com.alibaba.cola.exception.Assert;
import com.alibaba.cola.exception.BizException;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pxb7.mall.auth.c.util.AdminUserUtil;
import com.pxb7.mall.auth.dto.AdminUserDTO;
import com.pxb7.mall.workorder.app.mapping.NotDisturbPeriodDomainMapping;
import com.pxb7.mall.workorder.app.mapping.RemindPlanAppMapping;
import com.pxb7.mall.workorder.app.mapping.RemindPlanGameConfigAppMapping;
import com.pxb7.mall.workorder.app.mapping.RemindPlanRuleAppMapping;
import com.pxb7.mall.workorder.app.model.AssOptionalGameReqDTO;
import com.pxb7.mall.workorder.app.model.AssRemindPlanReqDTO;
import com.pxb7.mall.workorder.app.model.AssRemindPlanRespDTO;
import com.pxb7.mall.workorder.app.model.OptionalGameRespDTO;
import com.pxb7.mall.workorder.app.model.PlanRuleDTO;
import com.pxb7.mall.workorder.app.model.RemindPlanPageDetailDTO;
import com.pxb7.mall.workorder.app.util.PlanRuleOrGameConfigChecker;
import com.pxb7.mall.workorder.app.util.RemindPlanBuildUtil;
import com.pxb7.mall.workorder.app.util.RemindPlanGameConfigBuildUtil;
import com.pxb7.mall.workorder.app.util.RemindPlanOperateRecordBuildUtil;
import com.pxb7.mall.workorder.app.util.RemindPlanPageBuildUtil;
import com.pxb7.mall.workorder.app.util.RemindPlanRuleBuildUtil;
import com.pxb7.mall.workorder.domain.model.OptionalGameReqBO;
import com.pxb7.mall.workorder.domain.model.OptionalGameRespBO;
import com.pxb7.mall.workorder.domain.model.QuerySelectedGameBO;
import com.pxb7.mall.workorder.domain.model.RemindPlanBO;
import com.pxb7.mall.workorder.domain.model.RemindPlanDeleteContextBO;
import com.pxb7.mall.workorder.domain.model.RemindPlanGameConfigBO;
import com.pxb7.mall.workorder.domain.model.RemindPlanGameConfigRefreshBO;
import com.pxb7.mall.workorder.domain.model.RemindPlanOperateRecordBO;
import com.pxb7.mall.workorder.domain.model.RemindPlanReqBO;
import com.pxb7.mall.workorder.domain.model.RemindPlanRuleBO;
import com.pxb7.mall.workorder.domain.model.RemindPlanRuleRefreshBO;
import com.pxb7.mall.workorder.domain.model.RemindPlanUpdateContextBO;
import com.pxb7.mall.workorder.domain.model.RemindSubPlanBO;
import com.pxb7.mall.workorder.domain.service.RemindPlanDomainService;
import com.pxb7.mall.workorder.domain.service.RemindPlanGameConfigDomainService;
import com.pxb7.mall.workorder.domain.service.RemindSubPlanDomainService;
import com.pxb7.mall.workorder.infra.aop.ClusterRedisLock;
import com.pxb7.mall.workorder.infra.enums.RemindPlanServiceTypeEnum;
import com.pxb7.mall.workorder.infra.enums.RemindPlanWorkOrderTypeEnum;
import com.pxb7.mall.workorder.infra.model.SysUserRespPO;
import com.pxb7.mall.workorder.infra.repository.gateway.dubbo.user.SysUserGateway;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.pxb7.mall.workorder.client.enums.BizErrorCodeEnum.PARAM_ERROR;
import static com.pxb7.mall.workorder.client.enums.BizErrorCodeEnum.REMIND_PLAN_NOT_FOUND;
import static com.pxb7.mall.workorder.client.enums.BizErrorCodeEnum.USER_INFO_NOT_FOUND;

/**
 * 提醒服务预警计划app服务
 *
 * <AUTHOR>
 * @since 2025-04-22 09:12:17
 */
@Service
public class AssRemindPlanAppService {

    @Resource
    private RemindPlanDomainService remindPlanDomainService;

    @Resource
    private RemindSubPlanDomainService remindSubPlanDomainService;

    @Resource
    private RemindPlanGameConfigDomainService remindPlanGameConfigDomainService;

    @Resource
    private SysUserGateway sysUserGateway;

    @ClusterRedisLock(prefix = "ass_remind_plan_insert_lock", value = "")
    public Long insert(AssRemindPlanReqDTO.AddDTO param) {
        AdminUserDTO adminUser = AdminUserUtil.getAdminUser();
        Assert.isTrue(adminUser != null && StringUtils.isNotBlank(adminUser.getUserId()),
            USER_INFO_NOT_FOUND.getErrCode(), USER_INFO_NOT_FOUND.getErrDesc());

        RemindPlanServiceTypeEnum serviceTypeEnum = RemindPlanServiceTypeEnum.getEnum(param.getServiceType());
        Assert.isTrue(serviceTypeEnum != null, PARAM_ERROR.getErrCode(), "业务类型暂不支持");

        if (RemindPlanWorkOrderTypeEnum.RETRIEVE.getValue().equals(param.getWorkOrderType())) {
            Assert.notEmpty(Collections.singleton(param.getMembership()), PARAM_ERROR.getErrCode(), "请选择订单来源");
        }
        // 判断计划名称是否重复
        Boolean existPlanName = remindPlanDomainService.existPlanName(param.getPlanName(), param.getServiceType());
        Assert.isFalse(existPlanName, PARAM_ERROR.getErrCode(), "预警计划名称已存在");

        PlanRuleOrGameConfigChecker.checkPlanRuleSorted(param.getPlanRules());
        // 纠纷工单判断 IM客服端倒计时 和 预期完结时间
        if (RemindPlanWorkOrderTypeEnum.DISPUTE.getValue().equals(param.getWorkOrderType())) {
            PlanRuleOrGameConfigChecker.assCompareGameConfigTime(param.getPlanGameConfigs());
        }
        PlanRuleOrGameConfigChecker.assComparePlanRuleAndGameConfigTime(param.getPlanRules(),
            param.getPlanGameConfigs());
        RemindPlanBO remindPlanBO = RemindPlanBuildUtil.buildAssFromAddDTO(param, adminUser);
        this.checkRemindPlanGameConfigBO(remindPlanBO, remindPlanBO.getAllRemindPlanGameConfigs());
        List<RemindPlanOperateRecordBO> remindPlanOperateRecordBOS =
            RemindPlanOperateRecordBuildUtil.buildAddOperateRecordList(remindPlanBO, adminUser);
        return remindPlanDomainService.saveAllInfo(remindPlanBO, remindPlanOperateRecordBOS);
    }

    // 游戏占用检测
    private void checkRemindPlanGameConfigBO(RemindPlanBO remindPlanBO,
        List<RemindPlanGameConfigBO> addRemindPlanGameConfigBOs) {
        if (CollectionUtils.isEmpty(addRemindPlanGameConfigBOs)) {
            return;
        }
        // 服务类型
        QuerySelectedGameBO selectedGameBO = new QuerySelectedGameBO();
        selectedGameBO.setServiceType(remindPlanBO.getServiceType());
        selectedGameBO.setWorkOrderType(remindPlanBO.getRemindSubPlans().get(0).getWorkOrderType());
        selectedGameBO.setMembership(remindPlanBO.getRemindSubPlans().get(0).getMembership());

        List<RemindPlanGameConfigBO> selectedPlanGameConfigList =
            remindPlanDomainService.getSelectedPlanGameConfigList(selectedGameBO);
        Map<String, RemindPlanGameConfigBO> gameIdToConfigMap = selectedPlanGameConfigList.stream()
            .collect(Collectors.toMap(RemindPlanGameConfigBO::getGameId, Function.identity(), (l, r) -> l));

        for (RemindPlanGameConfigBO addPlanGameConfig : addRemindPlanGameConfigBOs) {
            RemindPlanGameConfigBO remindPlanGameConfigBO = gameIdToConfigMap.get(addPlanGameConfig.getGameId());
            if (null != remindPlanGameConfigBO) {
                throw new BizException(PARAM_ERROR.getErrCode(), "新增预警设置失败:" + remindPlanGameConfigBO.getGameName()
                    + "已被其他预警计划(ID:" + remindPlanGameConfigBO.getRemindPlanId() + ")选中");
            }
        }
    }

    @ClusterRedisLock(prefix = "ass_remind_plan_update_lock", value = "")
    public boolean update(AssRemindPlanReqDTO.UpdateDTO param) {
        AdminUserDTO adminUser = AdminUserUtil.getAdminUser();
        Assert.isTrue(adminUser != null && StringUtils.isNotBlank(adminUser.getUserId()),
            USER_INFO_NOT_FOUND.getErrCode(), USER_INFO_NOT_FOUND.getErrDesc());

        RemindPlanServiceTypeEnum serviceTypeEnum = RemindPlanServiceTypeEnum.getEnum(param.getServiceType());
        Assert.isTrue(serviceTypeEnum != null, PARAM_ERROR.getErrCode(), "业务类型暂不支持");

        if (RemindPlanWorkOrderTypeEnum.RETRIEVE.getValue().equals(param.getWorkOrderType())) {
            Assert.notEmpty(Collections.singleton(param.getMembership()), PARAM_ERROR.getErrCode(), "请选择订单来源");
        }

        List<PlanRuleDTO> planRuleDTOS = param.getPlanRules();
        PlanRuleOrGameConfigChecker.checkPlanRuleSorted(planRuleDTOS);
        if (RemindPlanWorkOrderTypeEnum.DISPUTE.getValue().equals(param.getWorkOrderType())) {
            PlanRuleOrGameConfigChecker.assCompareGameConfigTime(param.getPlanGameConfigs());
        }
        PlanRuleOrGameConfigChecker.assComparePlanRuleAndGameConfigTime(param.getPlanRules(),
            param.getPlanGameConfigs());
        // 修改前预警配置
        RemindPlanBO remindPlanBO = remindPlanDomainService.getRemindPlanAllInfo(param.getId());
        Assert.notNull(remindPlanBO, REMIND_PLAN_NOT_FOUND.getErrCode(), REMIND_PLAN_NOT_FOUND.getErrDesc());
        RemindPlanBO originalRemindPlanBO = new RemindPlanBO();
        BeanUtils.copyProperties(remindPlanBO, originalRemindPlanBO);
        remindPlanBO.setPlanName(param.getPlanName());
        remindPlanBO.setNotDisturbPeriod(
            NotDisturbPeriodDomainMapping.INSTANCE.notDisturbPeriodDTO2BO(param.getNotDisturbPeriod()));
        remindPlanBO.setUpdateUserId(adminUser.getUserId());
        remindPlanBO.setUpdateTime(LocalDateTime.now());

        // 判断计划名称是否重复
        if (!param.getPlanName().equals(originalRemindPlanBO.getPlanName())) {
            Boolean existPlanName =
                remindPlanDomainService.existPlanName(param.getPlanName(), originalRemindPlanBO.getServiceType());
            Assert.isFalse(existPlanName, PARAM_ERROR.getErrCode(), "预警计划名称已存在");
        }

        // 预警-游戏配置：
        // 新增了哪些？删除了哪些？ 修改了哪些？
        RemindPlanGameConfigRefreshBO remindPlanGameConfigRefreshBO = RemindPlanGameConfigBuildUtil
            .buildAssRemindPlanGameConfigRefreshBO(param.getPlanGameConfigs(), remindPlanBO, adminUser);
        this.checkRemindPlanGameConfigBO(remindPlanBO, remindPlanGameConfigRefreshBO.getNeedAddPlanGameConfigs());

        // 新增了哪些？删除了哪些？ 修改了哪些？
        RemindPlanRuleRefreshBO remindPlanRuleRefreshBO =
            RemindPlanRuleBuildUtil.buildRemindPlanRuleRefreshBO(planRuleDTOS, remindPlanBO, adminUser);

        RemindPlanUpdateContextBO remindPlanUpdateContextBO = new RemindPlanUpdateContextBO();
        remindPlanUpdateContextBO.setOriginRemindPlan(originalRemindPlanBO);
        remindPlanUpdateContextBO.setRemindPlan(remindPlanBO);
        remindPlanUpdateContextBO.setRemindPlanRuleRefresh(remindPlanRuleRefreshBO);
        remindPlanUpdateContextBO.setRemindPlanGameConfigRefresh(remindPlanGameConfigRefreshBO);
        remindPlanUpdateContextBO.setOperateRecords(
            RemindPlanOperateRecordBuildUtil.buildUpdateOperateRecordList(remindPlanUpdateContextBO, adminUser));
        return remindPlanDomainService.updateAllInfo(remindPlanUpdateContextBO);
    }

    public AssRemindPlanRespDTO.DetailDTO findById(Long remindPlanId) {
        AdminUserDTO adminUser = AdminUserUtil.getAdminUser();
        Assert.isTrue(adminUser != null && StringUtils.isNotBlank(adminUser.getUserId()),
            USER_INFO_NOT_FOUND.getErrCode(), USER_INFO_NOT_FOUND.getErrDesc());

        RemindPlanBO remindPlanBO = remindPlanDomainService.getRemindPlanAllInfo(remindPlanId);
        Assert.notNull(remindPlanBO, REMIND_PLAN_NOT_FOUND.getErrCode(), REMIND_PLAN_NOT_FOUND.getErrDesc());

        List<RemindPlanGameConfigBO> allRemindPlanGameConfigBOS =
            Optional.of(remindPlanBO).map(RemindPlanBO::getAllRemindPlanGameConfigs).orElse(new ArrayList<>());

        Integer serviceType = remindPlanBO.getServiceType();
        List<RemindSubPlanBO> remindSubPlans = remindPlanBO.getRemindSubPlans();
        AssRemindPlanRespDTO.DetailDTO detailDTO = new AssRemindPlanRespDTO.DetailDTO();
        detailDTO.setId(remindPlanBO.getId());
        detailDTO.setPlanName(remindPlanBO.getPlanName());
        detailDTO.setServiceType(serviceType);
        detailDTO.setNotDisturbPeriod(
            NotDisturbPeriodDomainMapping.INSTANCE.notDisturbPeriodBO2DTO(remindPlanBO.getNotDisturbPeriod()));
        detailDTO.setWorkOrderType(remindSubPlans.get(0).getWorkOrderType());
        detailDTO.setMembership(remindSubPlans.get(0).getMembership());
        detailDTO.setPlanGameConfigs(
            RemindPlanGameConfigBuildUtil.AssPlanGameConfigDTOList(allRemindPlanGameConfigBOS));
        detailDTO.setPlanRules(
            RemindPlanRuleAppMapping.INSTANCE.remindPlanRuleBO2ListDTO(remindPlanBO.getRemindPlanRules()));
        return detailDTO;
    }

    public Page<RemindPlanPageDetailDTO> page(AssRemindPlanReqDTO.PageDTO param) {
        AdminUserDTO adminUser = AdminUserUtil.getAdminUser();
        Assert.isTrue(adminUser != null && StringUtils.isNotBlank(adminUser.getUserId()),
            USER_INFO_NOT_FOUND.getErrCode(), USER_INFO_NOT_FOUND.getErrDesc());

        RemindPlanReqBO.PageBO pageBO = RemindPlanAppMapping.INSTANCE.remindPlanDTO2PageBO(param);
        pageBO.setServiceType(RemindPlanServiceTypeEnum.ASS_ORDER.getValue());
        Page<RemindPlanBO> page = remindPlanDomainService.page(pageBO);
        List<RemindPlanBO> remindPlans = Optional.ofNullable(page).map(Page::getRecords).orElse(new ArrayList<>());
        List<Long> remindPlanIds = remindPlans.stream().map(RemindPlanBO::getId).toList();
        List<RemindSubPlanBO> remindSubPlanList = remindSubPlanDomainService.getRemindSubPlanList(remindPlanIds);
        List<RemindPlanGameConfigBO> remindPlanGameConfigList =
            remindPlanGameConfigDomainService.getRemindPlanGameConfigList(remindPlanIds);

        Set<String> userIds = remindPlans.stream().map(RemindPlanBO::getUpdateUserId).collect(Collectors.toSet());
        List<SysUserRespPO> sysUserInfoList = sysUserGateway.getSysUserInfoList(userIds);
        Map<String, SysUserRespPO> sysUserRespPOMap =
            sysUserInfoList.stream().collect(Collectors.toMap(SysUserRespPO::getUserId, Function.identity()));

        Page<RemindPlanPageDetailDTO> pageDTO = RemindPlanAppMapping.INSTANCE.remindPlanBO2PageDTO(page);
        // 构造返回的dto数据
        List<RemindPlanPageDetailDTO> detailDTOS = RemindPlanPageBuildUtil.buildPageDetailDTOList(remindPlans,
            remindSubPlanList, remindPlanGameConfigList, sysUserRespPOMap);
        pageDTO.setRecords(detailDTOS);
        return pageDTO;
    }

    /**
     * 根据ID删除提醒计划
     *
     * 此方法首先验证当前用户是否已登录并具有管理员权限，然后根据提供的参数获取提醒计划的详细信息 如果提醒计划存在，则构建一个包含提醒计划及其关联子计划和配置的删除上下文对象，并执行删除操作
     *
     * @param param 包含要删除提醒计划ID的请求数据传输对象
     * @return 返回删除操作的成功与否
     */
    @ClusterRedisLock(prefix = "ass_remind_plan_delete_", value = "#param.id")
    public boolean deleteById(AssRemindPlanReqDTO.DelDTO param) {
        // 获取当前登录的管理员用户信息
        AdminUserDTO adminUser = AdminUserUtil.getAdminUser();
        Assert.isTrue(adminUser != null && StringUtils.isNotBlank(adminUser.getUserId()),
            USER_INFO_NOT_FOUND.getErrCode(), USER_INFO_NOT_FOUND.getErrDesc());

        // 根据提供的ID获取提醒计划的详细信息
        RemindPlanBO remindPlanBO = remindPlanDomainService.getRemindPlanAllInfo(param.getId());
        Assert.notNull(remindPlanBO, REMIND_PLAN_NOT_FOUND.getErrCode(), REMIND_PLAN_NOT_FOUND.getErrDesc());

        // 创建提醒计划删除上下文对象
        RemindPlanDeleteContextBO remindPlanDeleteContextBO = new RemindPlanDeleteContextBO();
        // 设置提醒计划ID
        remindPlanDeleteContextBO.setRemindPlanId(remindPlanBO.getId());
        // 设置提醒子计划ID列表
        remindPlanDeleteContextBO
            .setRemindSubPlanIds(remindPlanBO.getRemindSubPlans().stream().map(RemindSubPlanBO::getId).toList());
        // 设置提醒计划游戏配置ID列表
        remindPlanDeleteContextBO.setRemindPlanGameConfigIds(
            remindPlanBO.getAllRemindPlanGameConfigs().stream().map(RemindPlanGameConfigBO::getId).toList());
        // 设置提醒计划规则ID列表
        remindPlanDeleteContextBO
            .setRemindPlanRuleIds(remindPlanBO.getRemindPlanRules().stream().map(RemindPlanRuleBO::getId).toList());
        // 构建并设置删除操作的操作记录列表
        remindPlanDeleteContextBO.setOperateRecords(
            RemindPlanOperateRecordBuildUtil.buildDeleteAllOperateRecordList(remindPlanBO, adminUser));
        // 调用领域服务执行删除操作，并返回操作结果
        return remindPlanDomainService.delete(remindPlanDeleteContextBO);
    }

    public List<OptionalGameRespDTO> getOptionalGames(AssOptionalGameReqDTO param) {

        Integer serviceType = param.getServiceType();
        RemindPlanServiceTypeEnum serviceTypeEnum = RemindPlanServiceTypeEnum.getEnum(serviceType);
        Assert.isTrue(serviceTypeEnum != null, PARAM_ERROR.getErrCode(), "服务类型暂不支持");
        Assert.isTrue(RemindPlanServiceTypeEnum.ASS_ORDER.equals(serviceTypeEnum), "服务类型错误");
        if (RemindPlanWorkOrderTypeEnum.RETRIEVE.getValue().equals(param.getWorkOrderType())) {
            Assert.notNull(param.getMembership(), PARAM_ERROR.getErrCode(), "请选择订单来源");
        }

        OptionalGameReqBO optionalGameReqBO = RemindPlanGameConfigAppMapping.INSTANCE.optionalGameReqDTO2BO(param);
        List<OptionalGameRespBO> optionalGames = remindPlanDomainService.getOptionalGames(optionalGameReqBO);
        return RemindPlanGameConfigAppMapping.INSTANCE.optionalGameRespBO2ListDTO(optionalGames);
    }

}
