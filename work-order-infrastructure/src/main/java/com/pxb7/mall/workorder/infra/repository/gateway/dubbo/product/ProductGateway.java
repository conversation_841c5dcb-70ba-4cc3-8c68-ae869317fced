package com.pxb7.mall.workorder.infra.repository.gateway.dubbo.product;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.pxb7.mall.product.client.api.ProductServiceI;
import com.pxb7.mall.product.client.dto.response.product.SellerRespDTO;
import com.pxb7.mall.product.client.dto.response.product.WorkOrderRpcResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.Objects;

/**
 * 商品服务网关
 * <AUTHOR>
 * @date 2025/4/8 10:15
 */

@Repository
@Slf4j
public class ProductGateway {

    @DubboReference
    private ProductServiceI productServiceI;

    /**
     * 根据用户ID获取客服信息
     * @param workOrderId
     * @return
     */
    public WorkOrderRpcResp getWorkOrderInfo(String workOrderId) {
        try {
            MultiResponse<WorkOrderRpcResp> response = productServiceI.listByWorkOrderIds(Collections.singletonList(workOrderId));
            if (Objects.nonNull(response)
                    && response.isSuccess()
                    && !CollectionUtils.isEmpty(response.getData())) {
                return response.getData().get(0);
            }
            log.error("Fail to send message to the productServiceI#listByWorkOrderIds,workOrderId:{}", workOrderId);
            return null;
        } catch (Exception e) {
            log.error("Fail to send message to the productServiceI#listByWorkOrderIds,workOrderId:{}", workOrderId);
            return null;
        }
    }

    /**
     * 查询商品的卖家ID
     * @return
     */
    public String getSellerId(String gameId, String productId) {
        try {
            SingleResponse<SellerRespDTO> response = productServiceI.findSeller(gameId, productId);
            if (Objects.nonNull(response)
                    && response.isSuccess()) {
                return response.getData().getSellerId();
            }
            log.error("Fail to send message to the productServiceI#findSeller,gameId:{},productId:{}", gameId, productId);
            return null;
        } catch (Exception e) {
            log.error("Fail to send message to the productServiceI#findSeller,gameId:{},productId:{}", gameId, productId);
            return null;
        }
    }

}
