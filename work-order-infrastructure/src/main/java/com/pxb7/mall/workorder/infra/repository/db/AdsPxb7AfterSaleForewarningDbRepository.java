package com.pxb7.mall.workorder.infra.repository.db;

import java.util.List;
import java.util.Objects;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.pxb7.mall.workorder.infra.model.AdsPxb7AfterSaleForewarningReqPO;
import com.pxb7.mall.workorder.infra.repository.db.mapper.AdsPxb7AfterSaleForewarningMapper;
import com.pxb7.mall.workorder.infra.repository.db.entity.AdsPxb7AfterSaleForewarning;

/**
 * 售后找回纠纷预警统计(AdsPxb7AfterSaleForewarning)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-08 18:13:53
 */
@Slf4j
@Repository
public class AdsPxb7AfterSaleForewarningDbRepository
    extends ServiceImpl<AdsPxb7AfterSaleForewarningMapper, AdsPxb7AfterSaleForewarning>
    implements AdsPxb7AfterSaleForewarningRepository {

    @Override
    public boolean insert(AdsPxb7AfterSaleForewarningReqPO.AddPO param) {
        AdsPxb7AfterSaleForewarning entity = new AdsPxb7AfterSaleForewarning();
        entity.setCreateDate(param.getCreateDate());
        entity.setProcessUserId(param.getProcessUserId());
        entity.setGameId(param.getGameId());
        entity.setWorkOrderType(param.getWorkOrderType());
        entity.setMembership(param.getMembership());
        entity.setWorkOrderCnt(param.getWorkOrderCnt());
        entity.setCompleteWorkOrderCnt(param.getCompleteWorkOrderCnt());
        entity.setProcessingWorkOrderCnt(param.getProcessingWorkOrderCnt());
        entity.setTimeoutingWorkOrderCnt(param.getTimeoutingWorkOrderCnt());
        entity.setTimeoutedWorkOrderCnt(param.getTimeoutedWorkOrderCnt());
        entity.setCreateUserId(param.getCreateUserId());
        entity.setUpdateUserId(param.getUpdateUserId());
        return this.save(entity);
    }

    @Override
    public boolean update(AdsPxb7AfterSaleForewarningReqPO.UpdatePO param) {
        LambdaUpdateWrapper<AdsPxb7AfterSaleForewarning> updateWrapper = new LambdaUpdateWrapper<>();
        // where
        updateWrapper.eq(AdsPxb7AfterSaleForewarning::getId, param.getId());
        // set
        if (StringUtils.isNotBlank(param.getCreateDate())) {
            updateWrapper.set(AdsPxb7AfterSaleForewarning::getCreateDate, param.getCreateDate());
        }
        if (StringUtils.isNotBlank(param.getProcessUserId())) {
            updateWrapper.set(AdsPxb7AfterSaleForewarning::getProcessUserId, param.getProcessUserId());
        }
        if (StringUtils.isNotBlank(param.getGameId())) {
            updateWrapper.set(AdsPxb7AfterSaleForewarning::getGameId, param.getGameId());
        }
        if (Objects.nonNull(param.getWorkOrderType())) {
            updateWrapper.set(AdsPxb7AfterSaleForewarning::getWorkOrderType, param.getWorkOrderType());
        }
        if (Objects.nonNull(param.getMembership())) {
            updateWrapper.set(AdsPxb7AfterSaleForewarning::getMembership, param.getMembership());
        }
        if (Objects.nonNull(param.getWorkOrderCnt())) {
            updateWrapper.set(AdsPxb7AfterSaleForewarning::getWorkOrderCnt, param.getWorkOrderCnt());
        }
        if (Objects.nonNull(param.getCompleteWorkOrderCnt())) {
            updateWrapper.set(AdsPxb7AfterSaleForewarning::getCompleteWorkOrderCnt, param.getCompleteWorkOrderCnt());
        }
        if (Objects.nonNull(param.getProcessingWorkOrderCnt())) {
            updateWrapper.set(AdsPxb7AfterSaleForewarning::getProcessingWorkOrderCnt,
                param.getProcessingWorkOrderCnt());
        }
        if (Objects.nonNull(param.getTimeoutingWorkOrderCnt())) {
            updateWrapper.set(AdsPxb7AfterSaleForewarning::getTimeoutingWorkOrderCnt,
                param.getTimeoutingWorkOrderCnt());
        }
        if (Objects.nonNull(param.getTimeoutedWorkOrderCnt())) {
            updateWrapper.set(AdsPxb7AfterSaleForewarning::getTimeoutedWorkOrderCnt, param.getTimeoutedWorkOrderCnt());
        }
        if (StringUtils.isNotBlank(param.getCreateUserId())) {
            updateWrapper.set(AdsPxb7AfterSaleForewarning::getCreateUserId, param.getCreateUserId());
        }
        if (StringUtils.isNotBlank(param.getUpdateUserId())) {
            updateWrapper.set(AdsPxb7AfterSaleForewarning::getUpdateUserId, param.getUpdateUserId());
        }
        return this.update(updateWrapper);
    }

    @Override
    public boolean deleteById(AdsPxb7AfterSaleForewarningReqPO.DelPO param) {
        return this.removeById(param.getId());
    }

    @Override
    public AdsPxb7AfterSaleForewarning findById(Long id) {
        return this.getById(id);
    }

    @Override
    public List<AdsPxb7AfterSaleForewarning> list(AdsPxb7AfterSaleForewarningReqPO.SearchPO param) {
        LambdaQueryWrapper<AdsPxb7AfterSaleForewarning> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(param.getCreateDate())) {
            queryWrapper.eq(AdsPxb7AfterSaleForewarning::getCreateDate, param.getCreateDate());
        }
        if (StringUtils.isNotBlank(param.getProcessUserId())) {
            queryWrapper.eq(AdsPxb7AfterSaleForewarning::getProcessUserId, param.getProcessUserId());
        }
        if (StringUtils.isNotBlank(param.getGameId())) {
            queryWrapper.eq(AdsPxb7AfterSaleForewarning::getGameId, param.getGameId());
        }
        if (Objects.nonNull(param.getWorkOrderType())) {
            queryWrapper.eq(AdsPxb7AfterSaleForewarning::getWorkOrderType, param.getWorkOrderType());
        }
        if (Objects.nonNull(param.getMembership())) {
            queryWrapper.eq(AdsPxb7AfterSaleForewarning::getMembership, param.getMembership());
        }
        if (Objects.nonNull(param.getWorkOrderCnt())) {
            queryWrapper.eq(AdsPxb7AfterSaleForewarning::getWorkOrderCnt, param.getWorkOrderCnt());
        }
        if (Objects.nonNull(param.getCompleteWorkOrderCnt())) {
            queryWrapper.eq(AdsPxb7AfterSaleForewarning::getCompleteWorkOrderCnt, param.getCompleteWorkOrderCnt());
        }
        if (Objects.nonNull(param.getProcessingWorkOrderCnt())) {
            queryWrapper.eq(AdsPxb7AfterSaleForewarning::getProcessingWorkOrderCnt, param.getProcessingWorkOrderCnt());
        }
        if (Objects.nonNull(param.getTimeoutingWorkOrderCnt())) {
            queryWrapper.eq(AdsPxb7AfterSaleForewarning::getTimeoutingWorkOrderCnt, param.getTimeoutingWorkOrderCnt());
        }
        if (Objects.nonNull(param.getTimeoutedWorkOrderCnt())) {
            queryWrapper.eq(AdsPxb7AfterSaleForewarning::getTimeoutedWorkOrderCnt, param.getTimeoutedWorkOrderCnt());
        }
        if (StringUtils.isNotBlank(param.getCreateUserId())) {
            queryWrapper.eq(AdsPxb7AfterSaleForewarning::getCreateUserId, param.getCreateUserId());
        }
        if (StringUtils.isNotBlank(param.getUpdateUserId())) {
            queryWrapper.eq(AdsPxb7AfterSaleForewarning::getUpdateUserId, param.getUpdateUserId());
        }
        return this.list(queryWrapper);
    }

    @Override
    public Page<AdsPxb7AfterSaleForewarning> page(AdsPxb7AfterSaleForewarningReqPO.PagePO param) {
        LambdaQueryWrapper<AdsPxb7AfterSaleForewarning> queryWrapper = new LambdaQueryWrapper<>();
        // 返回分页查询结果
        if (StringUtils.isNotBlank(param.getCreateDate())) {
            queryWrapper.eq(AdsPxb7AfterSaleForewarning::getCreateDate, param.getCreateDate());
        }
        if (StringUtils.isNotBlank(param.getProcessUserId())) {
            queryWrapper.eq(AdsPxb7AfterSaleForewarning::getProcessUserId, param.getProcessUserId());
        }
        if (StringUtils.isNotBlank(param.getGameId())) {
            queryWrapper.eq(AdsPxb7AfterSaleForewarning::getGameId, param.getGameId());
        }
        if (Objects.nonNull(param.getWorkOrderType())) {
            queryWrapper.eq(AdsPxb7AfterSaleForewarning::getWorkOrderType, param.getWorkOrderType());
        }
        if (Objects.nonNull(param.getMembership())) {
            queryWrapper.eq(AdsPxb7AfterSaleForewarning::getMembership, param.getMembership());
        }
        if (Objects.nonNull(param.getWorkOrderCnt())) {
            queryWrapper.eq(AdsPxb7AfterSaleForewarning::getWorkOrderCnt, param.getWorkOrderCnt());
        }
        if (Objects.nonNull(param.getCompleteWorkOrderCnt())) {
            queryWrapper.eq(AdsPxb7AfterSaleForewarning::getCompleteWorkOrderCnt, param.getCompleteWorkOrderCnt());
        }
        if (Objects.nonNull(param.getProcessingWorkOrderCnt())) {
            queryWrapper.eq(AdsPxb7AfterSaleForewarning::getProcessingWorkOrderCnt, param.getProcessingWorkOrderCnt());
        }
        if (Objects.nonNull(param.getTimeoutingWorkOrderCnt())) {
            queryWrapper.eq(AdsPxb7AfterSaleForewarning::getTimeoutingWorkOrderCnt, param.getTimeoutingWorkOrderCnt());
        }
        if (Objects.nonNull(param.getTimeoutedWorkOrderCnt())) {
            queryWrapper.eq(AdsPxb7AfterSaleForewarning::getTimeoutedWorkOrderCnt, param.getTimeoutedWorkOrderCnt());
        }
        if (StringUtils.isNotBlank(param.getCreateUserId())) {
            queryWrapper.eq(AdsPxb7AfterSaleForewarning::getCreateUserId, param.getCreateUserId());
        }
        if (StringUtils.isNotBlank(param.getUpdateUserId())) {
            queryWrapper.eq(AdsPxb7AfterSaleForewarning::getUpdateUserId, param.getUpdateUserId());
        }
        return this.page(new Page<>(param.getPageIndex(), param.getPageSize()), queryWrapper);
    }
}
