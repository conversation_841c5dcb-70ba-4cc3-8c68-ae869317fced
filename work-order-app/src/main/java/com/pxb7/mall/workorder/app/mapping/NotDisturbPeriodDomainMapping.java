package com.pxb7.mall.workorder.app.mapping;


import com.pxb7.mall.workorder.app.model.NotDisturbPeriodDTO;
import com.pxb7.mall.workorder.domain.model.NotDisturbPeriodBO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface NotDisturbPeriodDomainMapping {

    NotDisturbPeriodDomainMapping INSTANCE = Mappers.getMapper(NotDisturbPeriodDomainMapping.class);


    NotDisturbPeriodBO notDisturbPeriodDTO2BO(NotDisturbPeriodDTO source);


    List<NotDisturbPeriodBO> notDisturbPeriodDTO2ListBO(List<NotDisturbPeriodDTO> source);


    NotDisturbPeriodDTO notDisturbPeriodBO2DTO(NotDisturbPeriodBO source);


    List<NotDisturbPeriodBO> notDisturbPeriodBO2ListDTO(List<NotDisturbPeriodDTO> source);
}


