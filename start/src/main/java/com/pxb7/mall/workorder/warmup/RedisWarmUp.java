package com.pxb7.mall.workorder.warmup;

import com.pxb7.mall.components.warmup.PxWarmUpPostProcess;
import com.pxb7.mall.workorder.infra.util.RedissonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/09/10 17:58
 **/
@Slf4j
@Component
public class RedisWarmUp implements PxWarmUpPostProcess {
    @Override
    public void warmUpProcess() {
        for (int i = 0; i < 100; i++) {
            RedissonUtils.getCacheObject("warmup_key_" + i);
        }
    }

    @Override
    public int loopExecNum() {
        return 1;
    }

    @Override
    public boolean loopExecute() {
        return true;
    }
}
