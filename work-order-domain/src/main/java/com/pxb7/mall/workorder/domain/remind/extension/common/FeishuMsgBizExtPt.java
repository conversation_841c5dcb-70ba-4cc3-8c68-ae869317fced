package com.pxb7.mall.workorder.domain.remind.extension.common;

import com.alibaba.cola.extension.Extensions;
import com.pxb7.mall.workorder.client.constant.MessageTemplateConstant;
import com.pxb7.mall.workorder.client.enums.BizTypeEnum;
import com.pxb7.mall.workorder.client.enums.RemindMethodEnum;
import com.pxb7.mall.workorder.client.enums.RemindObjectEnum;
import com.pxb7.mall.workorder.client.message.RemindPlanMessage;
import com.pxb7.mall.workorder.domain.model.*;
import com.pxb7.mall.workorder.domain.remind.extension.BaseMessageService;
import com.pxb7.mall.workorder.domain.remind.extension.RemindBizExtPt;
import com.pxb7.mall.workorder.domain.remind.extension.context.RemindBizContext;
import com.pxb7.mall.workorder.infra.enums.RemindPlanWorkOrderTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

import static com.pxb7.mall.workorder.infra.constant.CommonConstants.SYS_DEFAULT_USER_0;

/**
 * 飞书消息提醒，私聊
 * <AUTHOR>
 * @date 2025/4/2 14:56
 */

@Slf4j
@Extensions(
        bizId = {BizTypeEnum.Constants.DELIVERY_PRODUCT,
                BizTypeEnum.Constants.AFTER_SALE,
                BizTypeEnum.Constants.COMPLAINT},
        useCase = {RemindMethodEnum.Constants.FEISHU},
        scenario = {RemindObjectEnum.Constants.CUSTOMER_CARE})
public class FeishuMsgBizExtPt extends BaseMessageService implements RemindBizExtPt {

    @Override
    public void executeRemindTask(RemindBizContext context) {
        BaseRemindRecordBO remindRecord = context.getRemindRecord();
        RemindPlanMessage remindPlanMessage = context.getRemindPlanMessage();
        RemindSubPlanRespBO.DetailBO remindSubPlan =
                subPlanDomainService.findById(remindPlanMessage.getRemindSubPlanId());
        //获取提醒对象
        String systemUserId;
        String title;
        String content;
        if (remindRecord instanceof RemindDeliveryProductBO remindDeliveryProduct) {
            systemUserId = remindDeliveryProduct.getDeliveryCustomerCare();
            title = MessageTemplateConstant.DELIVERY_PRODUCT_REMIND_TITLE;
            content = getDeliveryProductMessageContent(context);
        } else if (remindRecord instanceof RemindAfterSaleBO remindAfterSale) {
            systemUserId = RemindPlanWorkOrderTypeEnum.RETRIEVE.eq(remindSubPlan.getWorkOrderType()) ?
                    remindAfterSale.getRetrieveUserId() : remindAfterSale.getDisputeUserId();
            title = RemindPlanWorkOrderTypeEnum.RETRIEVE.eq(remindSubPlan.getWorkOrderType()) ?
                    MessageTemplateConstant.AFTER_SALE_RETRIEVE_REMIND_TITLE : MessageTemplateConstant.AFTER_SALE_DISPUTE_REMIND_TITLE;
            content = getAfterSaleMessageContent(context, remindSubPlan, RemindMethodEnum.FEISHU);
        } else if (remindRecord instanceof RemindComplaintBO remindComplaint) {
            systemUserId = remindComplaint.getHandleUserId();
            title = MessageTemplateConstant.COMPLAINT_REMIND_TITLE;
            content = getComplaintMessageContent(context, remindSubPlan, RemindMethodEnum.FEISHU);
        } else {
            // 添加默认处理分支，防止未来类型扩展导致逻辑静默失败
            throw new IllegalArgumentException("Unsupported remindRecord type: " + remindRecord.getClass().getName());
        }

        if (StringUtils.isBlank(systemUserId)
                || Objects.equals(systemUserId, SYS_DEFAULT_USER_0)) {
            log.error("Fail to get systemUserId. planRecordId:{}", remindPlanMessage.getPlanRecordId());
            return;
        }

        if (StringUtils.isBlank(content)) {
            log.error("Fail to get message content. planRecordId:{}", remindPlanMessage.getPlanRecordId());
            return;
        }
        //向客服发送飞书私聊消息
        sendFeishuMessage(title, content, systemUserId, remindPlanMessage.getPlanRecordId());
    }

}
