package com.pxb7.mall.workorder.infra.repository.db;


import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import com.pxb7.mall.workorder.infra.model.RemindPlanRuleReqPO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.pxb7.mall.workorder.infra.repository.db.mapper.RemindPlanRuleMapper;
import com.pxb7.mall.workorder.infra.repository.db.entity.RemindPlanRule;

/**
 * 预警计划提醒规则配置(RemindPlanRule)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-24 21:09:56
 */
@Slf4j
@Repository
public class RemindPlanRuleDbRepository extends ServiceImpl<RemindPlanRuleMapper, RemindPlanRule> implements RemindPlanRuleRepository {

    @Override
    public boolean insert(RemindPlanRuleReqPO.AddPO param) {
        RemindPlanRule entity = new RemindPlanRule();
        entity.setId(param.getId());
        entity.setNodeNumber(param.getNodeNumber());
        entity.setRemindTimeConfig(param.getRemindTimeConfig());
        entity.setRemindMethodConfig(param.getRemindMethodConfig());
        entity.setRemindPlanId(param.getRemindPlanId());
        entity.setCreateUserId(param.getCreateUserId());
        entity.setUpdateUserId(param.getUpdateUserId());
        return this.save(entity);
    }

    @Override
    public boolean saveBatch(List<RemindPlanRuleReqPO.AddPO> params){
        List<RemindPlanRule> entities = new ArrayList<>();
        for (RemindPlanRuleReqPO.AddPO param : params) {
            RemindPlanRule entity = new RemindPlanRule();
            entity.setId(param.getId());
            entity.setNodeNumber(param.getNodeNumber());
            entity.setRemindTimeConfig(param.getRemindTimeConfig());
            entity.setRemindMethodConfig(param.getRemindMethodConfig());
            entity.setRemindPlanId(param.getRemindPlanId());
            entity.setCreateUserId(param.getCreateUserId());
            entity.setUpdateUserId(param.getUpdateUserId());
            entities.add(entity);
        }
        return this.saveBatch(entities);
    }

    @Override
    public boolean update(RemindPlanRuleReqPO.UpdatePO param) {
        LambdaUpdateWrapper<RemindPlanRule> updateWrapper = new LambdaUpdateWrapper<>();
        //where
        updateWrapper.eq(RemindPlanRule::getId, param.getId());
        //set
        if (Objects.nonNull(param.getNodeNumber())) {
            updateWrapper.set(RemindPlanRule::getNodeNumber, param.getNodeNumber());
        }
        if (StringUtils.isNotBlank(param.getRemindTimeConfig())) {
            updateWrapper.set(RemindPlanRule::getRemindTimeConfig, param.getRemindTimeConfig());
        }
        if (StringUtils.isNotBlank(param.getRemindMethodConfig())) {
            updateWrapper.set(RemindPlanRule::getRemindMethodConfig, param.getRemindMethodConfig());
        }
        if (Objects.nonNull(param.getRemindPlanId())) {
            updateWrapper.set(RemindPlanRule::getRemindPlanId, param.getRemindPlanId());
        }
        if (StringUtils.isNotBlank(param.getCreateUserId())) {
            updateWrapper.set(RemindPlanRule::getCreateUserId, param.getCreateUserId());
        }
        if (StringUtils.isNotBlank(param.getUpdateUserId())) {
            updateWrapper.set(RemindPlanRule::getUpdateUserId, param.getUpdateUserId());
        }
        return this.update(updateWrapper);
    }

    @Override
    public boolean deleteById(RemindPlanRuleReqPO.DelPO param) {
        return this.removeById(param.getId());
    }


    @Override
    public boolean deleteBatchByIds(List<Long> params) {
        if (Objects.isNull(params) || params.isEmpty()){
            return false;
        }
        LambdaUpdateWrapper<RemindPlanRule> updateWrapper = new LambdaUpdateWrapper<>();
        //where
        updateWrapper.in(RemindPlanRule::getId, params);
        updateWrapper.set(RemindPlanRule::getDeleted, true);
        return this.update(updateWrapper);
    }

    @Override
    public RemindPlanRule findById(Long id) {
        return this.getById(id);
    }

    @Override
    public List<RemindPlanRule> list(RemindPlanRuleReqPO.SearchPO param) {
        LambdaQueryWrapper<RemindPlanRule> queryWrapper = new LambdaQueryWrapper<>();
        if (Objects.nonNull(param.getNodeNumber())) {
            queryWrapper.eq(RemindPlanRule::getNodeNumber, param.getNodeNumber());
        }
        if (Objects.nonNull(param.getRemindPlanId())) {
            queryWrapper.eq(RemindPlanRule::getRemindPlanId, param.getRemindPlanId());
        }
        return this.list(queryWrapper);
    }

    @Override
    public Page<RemindPlanRule> page(RemindPlanRuleReqPO.PagePO param) {
        LambdaQueryWrapper<RemindPlanRule> queryWrapper = new LambdaQueryWrapper<>();
        // 返回分页查询结果
        if (Objects.nonNull(param.getNodeNumber())) {
            queryWrapper.eq(RemindPlanRule::getNodeNumber, param.getNodeNumber());
        }
        if (StringUtils.isNotBlank(param.getRemindTimeConfig())) {
            queryWrapper.eq(RemindPlanRule::getRemindTimeConfig, param.getRemindTimeConfig());
        }
        if (StringUtils.isNotBlank(param.getRemindMethodConfig())) {
            queryWrapper.eq(RemindPlanRule::getRemindMethodConfig, param.getRemindMethodConfig());
        }
        if (Objects.nonNull(param.getRemindPlanId())) {
            queryWrapper.eq(RemindPlanRule::getRemindPlanId, param.getRemindPlanId());
        }
        if (StringUtils.isNotBlank(param.getCreateUserId())) {
            queryWrapper.eq(RemindPlanRule::getCreateUserId, param.getCreateUserId());
        }
        if (StringUtils.isNotBlank(param.getUpdateUserId())) {
            queryWrapper.eq(RemindPlanRule::getUpdateUserId, param.getUpdateUserId());
        }
        return this.page(new Page<>(param.getPageIndex(), param.getPageSize()), queryWrapper);
    }
}
