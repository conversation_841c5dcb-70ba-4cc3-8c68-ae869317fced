package com.pxb7.mall.workorder.domain.service;


import com.alibaba.cola.exception.Assert;
import com.alibaba.excel.util.BooleanUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pxb7.mall.workorder.client.enums.*;
import com.pxb7.mall.workorder.client.message.RemindPlanMessage;
import com.pxb7.mall.workorder.domain.mapping.RemindAfterSaleDomainMapping;
import com.pxb7.mall.workorder.domain.model.*;
import com.pxb7.mall.workorder.domain.remind.task.RemindMessageTask;
import com.pxb7.mall.workorder.infra.aop.ClusterRedisLock;
import com.pxb7.mall.workorder.infra.constant.CommonConstants;
import com.pxb7.mall.workorder.infra.model.AfterSaleStatisticDataPO;
import com.pxb7.mall.workorder.infra.model.AfterSaleStatisticSearchPO;
import com.pxb7.mall.workorder.infra.model.RemindAfterSaleReqPO;
import com.pxb7.mall.workorder.infra.repository.db.RemindAfterSaleRepository;
import com.pxb7.mall.workorder.infra.repository.db.entity.RemindAfterSale;
import com.pxb7.mall.workorder.infra.repository.db.mapper.AdsPxb7AfterSaleForewarningMapper;
import com.pxb7.mall.workorder.infra.repository.es.entity.RemindAfterSaleDoc;
import com.pxb7.mall.workorder.infra.repository.es.mapper.RemindAfterSaleDocEsRepository;
import com.pxb7.mall.workorder.infra.repository.es.mapper.RemindAfterSaleDocRepository;
import com.pxb7.mall.workorder.infra.util.IdGenUtil;
import com.pxb7.mall.workorder.infra.util.TimeUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchPage;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;


/**
 * 售后工单预警记录domain服务
 *
 * <AUTHOR>
 * @since 2025-04-24 23:32:52
 */
@Slf4j
@Service
public class RemindAfterSaleDomainService extends BaseRemindDomainService {

    @Resource
    private RemindAfterSaleRepository remindAfterSaleRepository;

    @Resource
    private RemindMessageTask remindMessageTask;

    @Resource
    private RemindAfterSaleDocRepository remindAfterSaleDocRepository;
    @Resource
    private RemindAfterSaleDocEsRepository remindAfterSaleDocEsRepository;
    @Resource
    private AdsPxb7AfterSaleForewarningMapper adsPxb7AfterSaleForewarningMapper;

    @Override
    public BizTypeEnum getBizType() {
        return BizTypeEnum.AFTER_SALE;
    }

    public boolean updateByWorkOrderId(RemindAfterSaleReqBO.UpdateBO param) {
        RemindAfterSaleReqPO.UpdatePO updatePO = RemindAfterSaleDomainMapping.INSTANCE.remindAfterSaleBO2UpdatePO(param);
        return remindAfterSaleRepository.updateByWorkOrderId(updatePO);
    }

    /**
     * 生成售后工单预警记录
     * @param remindAfterSaleBO
     * @param remindPlanGameConfig
     * @return
     */
    public RemindAfterSaleReqBO.AddBO generateRemindAfterSale(
            RemindAfterSaleBO remindAfterSaleBO, RemindPlanGameConfigBO remindPlanGameConfig) {
        //查询是否已存在
        RemindAfterSaleRespBO.DetailBO detailBO = findByWorkOrderId(remindAfterSaleBO.getWorkOrderId(), null);
        if (Objects.nonNull(detailBO)) {
            return null;
        }
        //预期完结时间
        TimeConfigBO expectCompleteTimeConfig = remindPlanGameConfig.getExpectCompleteTimeConfig();
        LocalDateTime expectCompleteTime = TimeUtils.addSpecificTime(
                expectCompleteTimeConfig.getHours(), expectCompleteTimeConfig.getMinutes(), 0);
        //im客服端倒计时开始时间
        TimeConfigBO imCountDownTimeConfig = remindPlanGameConfig.getImCountDownTimeConfig();
        LocalDateTime imCountDownTime = Objects.nonNull(imCountDownTimeConfig) ?
                TimeUtils.subtractSpecificTime(expectCompleteTime, imCountDownTimeConfig.getHours(), imCountDownTimeConfig.getMinutes(), 0)
                : null;
        //插入售后工单预警记录
        RemindAfterSaleReqBO.AddBO addBO = new RemindAfterSaleReqBO.AddBO()
                .setRemindId("AS" + IdGenUtil.getShardingColumnId(remindAfterSaleBO.getWorkOrderId()))
                .setWorkOrderId(remindAfterSaleBO.getWorkOrderId())
                .setWorkOrderNo(remindAfterSaleBO.getWorkOrderNo())
                .setProductId(remindAfterSaleBO.getProductId())
                .setProductCode(remindAfterSaleBO.getProductCode())
                .setGameId(remindAfterSaleBO.getGameId())
                .setGroupId(remindAfterSaleBO.getGroupId())
                .setOrderId("")
                .setOrderItemId(remindAfterSaleBO.getOrderItemId())
                .setCompleteStatus(CompleteStatusEnum.IN_PROCESS.getCode())
                .setTimeOutStatus(TimeoutStatusEnum.NONE.getCode())
                .setWorkOrderType(remindAfterSaleBO.getWorkOrderType())
                .setRetrieveUserId(remindAfterSaleBO.getRetrieveUserId())
                .setDisputeUserId(remindAfterSaleBO.getDisputeUserId())
                .setExpectCompleteTime(expectCompleteTime)
                .setImCountDownTime(imCountDownTime)
                .setGameConfigId(remindPlanGameConfig.getId())
                .setRemindPlanId(remindPlanGameConfig.getRemindPlanId())
                .setRemindSubPlanId(remindPlanGameConfig.getRemindSubPlanId())
                .setCreateUserId(CommonConstants.SYS_DEFAULT_USER)
                .setUpdateUserId(CommonConstants.SYS_DEFAULT_USER);

        RemindAfterSaleReqPO.AddPO addPO = RemindAfterSaleDomainMapping.INSTANCE.remindAfterSaleBO2AddPO(addBO);
        boolean result = remindAfterSaleRepository.insert(addPO);
        Assert.isTrue(result,
                BizErrorCodeEnum.REMIND_RECORD_SAVE_ERROR.getErrCode(), BizErrorCodeEnum.REMIND_RECORD_SAVE_ERROR.getErrDesc());
        return addBO;
    }

    /**
     * 处理提醒消息发送
     * @param remindPlanMessage
     */
    @Override
    public PlanExecuteStatusEnum handleRemindMessage(RemindPlanMessage remindPlanMessage) {
        //根据workOrderId查询商品工单预警记录
        RemindAfterSaleRespBO.DetailBO remindWorkOrder =
                findByWorkOrderId(remindPlanMessage.getBizId(), remindPlanMessage.getRemindId());
        if (Objects.isNull(remindWorkOrder)) {
            log.error("根据workOrderId查询售后工单预警记录为空，workOrderId:{}", remindPlanMessage.getBizId());
            return PlanExecuteStatusEnum.FAILED;
        }
        //完结状态,1:未完结,2:已完结,3:终止
        Integer completeStatus = remindWorkOrder.getCompleteStatus();
        if (!Objects.equals(CompleteStatusEnum.IN_PROCESS.getCode(), completeStatus)) {
            return PlanExecuteStatusEnum.EXPIRED;
        }
        //处理提醒消息发送
        RemindAfterSaleBO remindRecord = new RemindAfterSaleBO();
        BeanUtils.copyProperties(remindWorkOrder, remindRecord);
        Boolean result = remindMessageTask.handleRemindMessage(remindPlanMessage, remindRecord);
        return Objects.equals(result, Boolean.TRUE)
                ? PlanExecuteStatusEnum.DONE : PlanExecuteStatusEnum.FAILED;
    }

    /**
     * 根据workOrderId查询售后工单预警记录
     * @param workOrderId
     * @param remindId
     * @return
     */
    public RemindAfterSaleRespBO.DetailBO findByWorkOrderId(String workOrderId, String remindId) {
        RemindAfterSale entity = remindAfterSaleRepository.findByWorkOrderId(workOrderId, remindId);
        return RemindAfterSaleDomainMapping.INSTANCE.remindAfterSalePO2DetailBO(entity);
    }

    /**
     * 更新售后预警记录超时状态
     * @param remindPlanMessage
     */
    @Override
    public void updateWillTimeOutStatus(RemindPlanMessage remindPlanMessage) {
        RemindAfterSaleRespBO.DetailBO remindAfterSale =
                findByWorkOrderId(remindPlanMessage.getBizId(), remindPlanMessage.getRemindId());
        if (!Objects.equals(CompleteStatusEnum.IN_PROCESS.getCode(), remindAfterSale.getCompleteStatus())) {
            return;
        }
        //更新预警记录状态
        RemindAfterSaleReqBO.UpdateBO updateBO = new RemindAfterSaleReqBO.UpdateBO();
        updateBO.setWorkOrderId(remindPlanMessage.getBizId());
        updateBO.setRemindId(remindPlanMessage.getRemindId());
        updateBO.setTimeOutStatus(TimeoutStatusEnum.WILL_TIMEOUT.getCode());
        this.updateByWorkOrderId(updateBO);

        //通知客服端，把超时房间移到超时tab
        moveGroupToTimeoutList(
                Collections.singletonList(remindAfterSale), TimeoutStatusEnum.WILL_TIMEOUT);
    }

    /**
     * 执行售后工单超时状态更新任务
     */
    @Override
    public void executeUpdateTimeoutStatusTask(String databaseName) {
        log.info("执行售后工单超时状态更新任务，任务开始，databaseName:{}", databaseName);
        //提前时间
        LocalDateTime nowDateTime = LocalDateTime.now();

        //分页查询符合条件的提醒计划记录
        //游标id
        long minId = 0;
        long pageSize = CommonConstants.QUERY_PAGE_SIZE;
        while (true) {
            RemindAfterSaleReqBO.PageBO param = new RemindAfterSaleReqBO.PageBO();
            param.setId(minId);
            param.setCompleteStatus(CompleteStatusEnum.IN_PROCESS.getCode());
            param.setTimeOutStatus(TimeoutStatusEnum.TIMEOUT.getCode());
            param.setExpectCompleteTime(nowDateTime);
            param.setPageIndex(1);
            param.setPageSize(pageSize);

            Page<RemindAfterSaleRespBO.DetailBO> page = pageDb(param);
            List<RemindAfterSaleRespBO.DetailBO> pageRecords = page.getRecords();
            if (CollectionUtils.isEmpty(pageRecords)) {
                break;
            }
            minId = pageRecords.get(pageRecords.size() - 1).getId();
            //更新状态为过期
            List<Long> ids = pageRecords.stream().map(RemindAfterSaleRespBO.DetailBO::getId).toList();
            batchUpdateTimeoutStatusByIds(
                    ids, TimeoutStatusEnum.TIMEOUT);

            //通知客服端，把超时房间移到超时tab
            moveGroupToTimeoutList(
                    pageRecords, TimeoutStatusEnum.TIMEOUT);

            log.info("执行售后工单超时状态更新任务，databaseName:{}，page:{}，page size:{}", databaseName, page.getCurrent(), page.getSize());
        }
        log.info("执行售后工单超时状态更新任务，任务结束，databaseName:{}", databaseName);
    }


    /**
     * 分页查询
     * @param param
     * @return
     */
    private Page<RemindAfterSaleRespBO.DetailBO> pageDb(RemindAfterSaleReqBO.PageBO param) {
        RemindAfterSaleReqPO.PagePO pagePO =
                RemindAfterSaleDomainMapping.INSTANCE.remindAfterSaleBO2PagePO(param);
        Page<RemindAfterSale> page = remindAfterSaleRepository.page(pagePO);
        return RemindAfterSaleDomainMapping.INSTANCE.remindAfterSalePO2PageBO(page);
    }

    /**
     * 批量更新账号交付超时状态
     * @param ids
     * @param status
     * @return
     */
    private Boolean batchUpdateTimeoutStatusByIds(List<Long> ids, TimeoutStatusEnum status) {
        if (CollectionUtils.isEmpty(ids)) {
            return Boolean.FALSE;
        }
        return remindAfterSaleRepository.batchUpdateTimeoutStatusByIds(ids, status.getCode());
    }

    /**
     * 通知客服端，把超时房间移到超时tab
     * @param remindAfterSales
     * @param status
     */
    private void moveGroupToTimeoutList(
            List<RemindAfterSaleRespBO.DetailBO> remindAfterSales, TimeoutStatusEnum status) {
        List<ImGroupCmdMsgBO> imGroupCmdMsgList = new ArrayList<>();
        for (RemindAfterSaleRespBO.DetailBO remindAfterSale : remindAfterSales) {
            if (StringUtils.isBlank(remindAfterSale.getGroupId())
                    || StringUtils.isBlank(remindAfterSale.getDisputeUserId())) {
                continue;
            }
            ImGroupCmdMsgBO imGroupCmdMsgBO = new ImGroupCmdMsgBO()
                    .setGroupId(remindAfterSale.getGroupId())
                    .setTargetUserId(remindAfterSale.getDisputeUserId())
                    .setStatus(status);
            imGroupCmdMsgList.add(imGroupCmdMsgBO);
        }
        moveGroupToTimeoutList(imGroupCmdMsgList);
    }


    /**
     * 通过房间查询售后预警记录
     * @param groupId
     * @return
     */
    @Override
    public RemindAfterSaleBO searchByGroupId(String groupId, String customerCareId) {
        RemindAfterSaleDoc afterSaleDoc = remindAfterSaleDocEsRepository.searchByGroupId(groupId, customerCareId);
        return RemindAfterSaleDomainMapping.INSTANCE.remindAfterSaleDoc2BO(afterSaleDoc);
    }


    public Page<RemindAfterSaleBO> pageEs(RemindAfterSaleReqBO.PageBO param) {
        RemindAfterSaleReqPO.PagePO pagePO = RemindAfterSaleDomainMapping.INSTANCE.remindAfterSaleBO2PagePO(param);
        SearchPage<RemindAfterSaleDoc> searchHits = remindAfterSaleDocEsRepository.pageQueryAfterSale(pagePO);
        if (searchHits == null){
            return new Page<>(param.getPageIndex(), param.getPageSize());
        }
        if (CollectionUtils.isEmpty(searchHits.getContent())) {
            return new Page<>(searchHits.getNumber(), searchHits.getSize(), searchHits.getTotalElements());
        }
        List<SearchHit<RemindAfterSaleDoc>> content = searchHits.getContent();
        List<RemindAfterSaleDoc> list = content.stream().map(SearchHit::getContent).toList();
        List<RemindAfterSaleBO> remindAfterSaleBOS = RemindAfterSaleDomainMapping.INSTANCE.remindAfterSaleDoc2ListBO(list);
        Page<RemindAfterSaleBO> remindAfterSaleBOPage = new Page<>(searchHits.getNumber(), searchHits.getSize(), searchHits.getTotalElements());
        remindAfterSaleBOPage.setRecords(remindAfterSaleBOS);
        return remindAfterSaleBOPage;
    }

    public void deleteRemindAfterSaleEs(List<String> remindIds) {
        if (CollectionUtils.isEmpty(remindIds)) {
            return;
        }
        remindAfterSaleDocRepository.deleteAllById(remindIds);
    }

    @ClusterRedisLock(prefix = "after_sale_data_sync", value = "#remindId")
    public void syncRemindAfterSaleToEs(String remindId) {
        RemindAfterSale remindAfterSale = remindAfterSaleRepository.getByRemindId(remindId);
        if (Objects.isNull(remindAfterSale)
                || BooleanUtils.isTrue(remindAfterSale.getDeleted())) {
            remindAfterSaleDocRepository.deleteById(remindId);
        } else {
            RemindAfterSaleDoc remindAfterSaleDoc = RemindAfterSaleDomainMapping.INSTANCE.remindAfterSalePO2Doc(remindAfterSale);
            if (Objects.isNull(remindAfterSaleDoc)) {
                return;
            }
            remindAfterSaleDocRepository.save(remindAfterSaleDoc);
        }
    }

    /**
     * 售后统计查询
     *
     * @param searchBO 参数
     * @return List<AfterSaleStatisticDataBO>
     */
    public List<AfterSaleStatisticDataBO> getStatisticList(AfterSaleStatisticDataSearchBO searchBO) {
        AfterSaleStatisticSearchPO param =
            RemindAfterSaleDomainMapping.INSTANCE.remindAfterSaleStatisticBO2SearchPO(searchBO);
        List<AfterSaleStatisticDataPO> afterSaleStatisticDataPOS =
            adsPxb7AfterSaleForewarningMapper.countBySearchParam(param);
        return RemindAfterSaleDomainMapping.INSTANCE.afterSaleStatisticDataPO2BO(afterSaleStatisticDataPOS);
    }

    public LocalDateTime getLastDataInsertTime() {
        return adsPxb7AfterSaleForewarningMapper.getLastDataInsertTime();
    }
}

