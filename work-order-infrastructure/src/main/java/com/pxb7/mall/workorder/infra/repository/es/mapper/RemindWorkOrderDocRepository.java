package com.pxb7.mall.workorder.infra.repository.es.mapper;

import com.pxb7.mall.workorder.infra.repository.es.entity.RemindWorkOrderDoc;
import org.springframework.data.elasticsearch.repository.ElasticsearchRepository;

import java.io.Serializable;

public interface RemindWorkOrderDocRepository extends ElasticsearchRepository<RemindWorkOrderDoc, Serializable> {

    //SearchPage<RemindWorkOrderDoc> pageQueryWorkOrder(RemindWorkOrderReqPO.PagePO workOrderPagePO);
}
