package com.pxb7.mall.workorder.app;

import com.pxb7.mall.trade.ass.client.dto.response.afc.RetrieveWORespDTO;
import com.pxb7.mall.workorder.app.service.RemindAfterSaleAppService;
import com.pxb7.mall.workorder.client.enums.BizTypeEnum;
import com.pxb7.mall.workorder.domain.model.RemindAfterSaleBO;
import com.pxb7.mall.workorder.domain.model.RemindAfterSaleRespBO;
import com.pxb7.mall.workorder.domain.service.RemindAfterSaleDomainService;
import com.pxb7.mall.workorder.domain.service.RemindPlanGameConfigDomainService;
import com.pxb7.mall.workorder.domain.service.RemindPlanRecordDomainService;
import com.pxb7.mall.workorder.infra.enums.RemindPlanWorkOrderTypeEnum;
import com.pxb7.mall.workorder.infra.repository.gateway.dubbo.ass.AfcWorkOrderGateway;
import com.pxb7.mall.workorder.infra.repository.gateway.dubbo.order.OrderInfoGateway;
import com.pxb7.mall.workorder.infra.repository.gateway.dubbo.product.GameGateway;
import com.pxb7.mall.workorder.infra.repository.gateway.dubbo.product.ProductGateway;
import com.pxb7.mall.workorder.infra.repository.gateway.dubbo.user.SysUserGateway;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.mockito.Mockito.*;

public class RemindAfterSaleAppServiceTest {

    @InjectMocks
    private RemindAfterSaleAppService service;

    @Mock
    private AfcWorkOrderGateway afcWorkOrderGateway;

    @Mock
    private OrderInfoGateway orderInfoGateway;

    @Mock
    private ProductGateway productGateway;

    @Mock
    private SysUserGateway sysUserGateway;

    @Mock
    private GameGateway gameGateway;

    @Mock
    private RemindPlanGameConfigDomainService gameConfigDomainService;

    @Mock
    private RemindAfterSaleDomainService afterSaleDomainService;

    @Mock
    private RemindPlanRecordDomainService planRecordDomainService;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testDealAfterSaleWorkOrder_Retrieve_Success() {
        String workOrderId = "123";
        Integer workOrderType = RemindPlanWorkOrderTypeEnum.RETRIEVE.getValue();

        RetrieveWORespDTO dto = new RetrieveWORespDTO();
        dto.setWorkOrderId(workOrderId);
        dto.setStatus(1);

        when(afcWorkOrderGateway.getRetrieveWorkOrderInfo(workOrderId)).thenReturn(dto);

        service.dealAfterSaleWorkOrder(workOrderId, workOrderType);

        verify(afterSaleDomainService).generateRemindAfterSale(any(), any());
    }

    @Test
    public void testGenerateRemindPlan_Invalid_OrderItemId() {
        RemindAfterSaleBO bo = new RemindAfterSaleBO();
        bo.setWorkOrderId("123");
        bo.setOrderItemId(null); // orderItemId 为空
        bo.setWorkOrderType(RemindPlanWorkOrderTypeEnum.RETRIEVE.getValue());

        service.generateRemindPlan(bo);

        verifyNoInteractions(gameConfigDomainService);
    }

    @Test
    public void testChangeRemindPlanStatus_Normal() {
        RemindAfterSaleBO bo = new RemindAfterSaleBO();
        bo.setWorkOrderId("123");
        bo.setCompleteStatus(2); // COMPLETE

        RemindAfterSaleRespBO.DetailBO detailBO = new RemindAfterSaleRespBO.DetailBO();
        detailBO.setGroupId("group1");
        detailBO.setDisputeUserId("user1");

        when(afterSaleDomainService.findByWorkOrderId("123", null)).thenReturn(detailBO);

        service.changeRemindPlanStatus(bo);

        verify(planRecordDomainService).invalidPlanRecord(BizTypeEnum.AFTER_SALE.getType(), "123");
    }
}

