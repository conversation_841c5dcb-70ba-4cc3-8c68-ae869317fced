package com.pxb7.mall.workorder.app.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version : BargainTicketPageDetailDTO.java, v 0.1 2025年04月16日 15:54 yang.xuexi Exp $
 */
@Getter
@Setter
@Accessors(chain = true)
@ToString
public class BargainTicketDetailDTO {

    /**
     * 工单id
     */
    private String receiveId;

    /**
     * 买家id
     */
    private String buyerId;


    /**
     * 买家手机号
     */
    private String buyerPhone;


    /**
     * 接单客服id
     */
    private String customerId;

    /**
     * 接单客服名称
     */
    private String customerName;


    /**
     * 还价数量
     */
    private Integer bargainNum;

    /**
     * 还价游戏列表信息
     */
    private List<GameInfo> gameInfoList;

    /**
     * 游戏名称拼接字符串、
     */
    private String gameNameStr;


    /**
     * 最低号价
     */
    private Long numberPriceMin;

    /**
     * 最高号价
     */
    private Long numberPriceMax;
    /**
     * 议价比例开始价（单位：分）
     */
    private Double bargainingRatioStart;


    /**
     * 议价比例结束价（单位：分）
     */
    private Double bargainingRatioEnd;


    /**
     * 工单状态( 0：不可接单 1：待接单 2：已接单 3：已完结')
     */
    private Integer receiveStatus;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 接单时间(yyyy-MM-dd)
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime receiveTime;

    /**
     * 接单日期
     */
    private String receiveDate;

    /**
     * 10:未成交 20:有成交
     */
    private Integer dealFlag;

    /**
     * 10:未读 20:已读
     */
    private Integer readFlag;


    /**
     * 游戏列表信息
     */
    @Setter
    @Getter
    @Accessors(chain = true)
    public static class GameInfo {
        private String gameId;
        private String gameName;
    }


}
