package com.pxb7.mall.workorder.app.handler;

import com.pxb7.mall.workorder.client.dto.DbRecordSyncDTO;
import com.pxb7.mall.workorder.domain.service.RemindComplaintDomainService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Slf4j
@Component
public class RemindComplaintHandler extends AbstractDbRecordSyncHandler {

    @Resource
    private RemindComplaintDomainService remindComplaintDomainService;

    @Override
    public String getTableName() {
        return "remind_complaint";
    }

    private static final String DELETE = "DELETE";

    @Override
    public void handle(DbRecordSyncDTO dbRecordSyncDTO) {

        if (null == dbRecordSyncDTO || CollectionUtils.isEmpty(dbRecordSyncDTO.getOperateKeys())) {
            return;
        }
        List<String> remindIds = dbRecordSyncDTO.getOperateKeys();
        String operateType = dbRecordSyncDTO.getOperateType();
        if (StringUtils.isBlank(operateType)) {
            return;
        }
        if (DELETE.equals(operateType)) {
            remindComplaintDomainService.deleteRemindComplaintEs(remindIds);
        } else {
            for (String remindId : remindIds) {
                remindComplaintDomainService.syncRemindComplaintToEs(remindId);
            }
        }
    }
}
