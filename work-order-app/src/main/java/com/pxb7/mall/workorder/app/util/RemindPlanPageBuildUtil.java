package com.pxb7.mall.workorder.app.util;

import com.pxb7.mall.workorder.app.model.AfterSalePageDetailDTO;
import com.pxb7.mall.workorder.app.model.ComplaintPageDetailDTO;
import com.pxb7.mall.workorder.app.model.DeliveryProductPageDetailDTO;
import com.pxb7.mall.workorder.app.model.WorkOrderPageDetailDTO;
import com.pxb7.mall.workorder.domain.model.*;
import com.pxb7.mall.workorder.infra.enums.RemindPlanServiceTypeEnum;
import com.pxb7.mall.workorder.app.mapping.RemindPlanAppMapping;
import com.pxb7.mall.workorder.app.model.RemindPlanPageDetailDTO;
import com.pxb7.mall.workorder.infra.model.SysUserRespPO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

public class RemindPlanPageBuildUtil {

    public static List<RemindPlanPageDetailDTO> buildPageDetailDTOList(List<RemindPlanBO> remindPlanBOS, List<RemindSubPlanBO> remindSubPlanList,
        List<RemindPlanGameConfigBO> remindPlanGameConfigList, Map<String, SysUserRespPO> sysUserRespPOMap ){
        Map<Long, List<RemindSubPlanBO>> planIdToSubPlansMap = remindSubPlanList.stream()
            .collect(Collectors.groupingBy(RemindSubPlanBO::getRemindPlanId));

        Map<Long, List<RemindPlanGameConfigBO>> planIdToGameConfigsMap = remindPlanGameConfigList.stream()
            .collect(Collectors.groupingBy(RemindPlanGameConfigBO::getRemindPlanId));

        List<RemindPlanPageDetailDTO> remindPlanPageDetailDTOS = new ArrayList<>();

        for (RemindPlanBO remindPlanBO : remindPlanBOS) {
            RemindPlanPageDetailDTO remindPlanPageDetailDTO = RemindPlanAppMapping.INSTANCE.remindPlanBO2DTO(remindPlanBO);

            List<RemindSubPlanBO> subPlans = planIdToSubPlansMap.getOrDefault(remindPlanBO.getId(), Collections.emptyList());
            if (RemindPlanServiceTypeEnum.DELIVERY_PRODUCT.getValue().equals(remindPlanBO.getServiceType())){

                List<Integer> businessTypes =
                    subPlans.stream().map(RemindSubPlanBO::getBusinessType).filter(Objects::nonNull).distinct().collect(Collectors.toList());
                remindPlanPageDetailDTO.setBusinessTypes(businessTypes);

            }else if (RemindPlanServiceTypeEnum.WORK_ORDER.getValue().equals(remindPlanBO.getServiceType())){
                List<Integer> workOrderStatuses =
                    subPlans.stream().map(RemindSubPlanBO::getWorkOrderStatus).filter(Objects::nonNull).distinct().collect(Collectors.toList());
                remindPlanPageDetailDTO.setWorkOrderStatuses(workOrderStatuses);
                List<Integer> onShelfTypes =
                    subPlans.stream().map(RemindSubPlanBO::getOnShelfType).filter(Objects::nonNull).distinct().collect(Collectors.toList());
                remindPlanPageDetailDTO.setOnShelfTypes(onShelfTypes);
            } else if (RemindPlanServiceTypeEnum.ASS_ORDER.getValue().equals(remindPlanBO.getServiceType())) {
                remindPlanPageDetailDTO.setWorkOrderType(subPlans.get(0).getWorkOrderType());
            } else if (RemindPlanServiceTypeEnum.COMPLAIN_ORDER.getValue().equals(remindPlanBO.getServiceType())) {
                remindPlanPageDetailDTO.setComplaintLevel(subPlans.get(0).getComplaintLevel());
            }
            List<RemindPlanGameConfigBO> planGameConfigBOList =
                planIdToGameConfigsMap.getOrDefault(remindPlanBO.getId(), Collections.emptyList());
            List<String> gameNames = planGameConfigBOList.stream().map(RemindPlanGameConfigBO::getGameName).distinct()
                .collect(Collectors.toList());
            remindPlanPageDetailDTO.setGameNames(gameNames);
            List<Integer> makerNames = planGameConfigBOList.stream().map(RemindPlanGameConfigBO::getMaker).distinct()
                .collect(Collectors.toList());
            remindPlanPageDetailDTO.setMakerNames(makerNames);
            SysUserRespPO sysUserRespPO = sysUserRespPOMap.get(remindPlanBO.getUpdateUserId());
            remindPlanPageDetailDTO.setUpdateUserName(sysUserRespPO == null ? null
                : sysUserRespPO.getUserName() + "(" + sysUserRespPO.getExternalName() + ")");
            remindPlanPageDetailDTOS.add(remindPlanPageDetailDTO);
        }
        return remindPlanPageDetailDTOS;

    }


    public static Set<String> extractUserIdsFromWorkOrder( List<WorkOrderPageDetailDTO> records){
        if (CollectionUtils.isEmpty(records)){
            return new HashSet<>();
        }
        return records.stream().map(a -> {
            List<String> tempUserIds = new ArrayList<>();
            if (StringUtils.isNotBlank(a.getArtDesignerId())) {
                tempUserIds.add(a.getArtDesignerId());
            }
            if (StringUtils.isNotBlank(a.getFollowerId())) {
                tempUserIds.add(a.getFollowerId());
            }
            if (StringUtils.isNotBlank(a.getAuditUserId())) {
                tempUserIds.add(a.getAuditUserId());
            }
            return tempUserIds;
        }).flatMap(Collection::stream).collect(Collectors.toSet());
    }

    public static Set<String> extractUserIdsFromWorkOrderStatisticList(List<WorkOrderStatisticDataBO> records){
        if (CollectionUtils.isEmpty(records)){
            return new HashSet<>();
        }
        return records.stream().map(a -> {
            List<String> tempUserIds = new ArrayList<>();
            if (StringUtils.isNotBlank(a.getArtDesignerId())) {
                tempUserIds.add(a.getArtDesignerId());
            }
            if (StringUtils.isNotBlank(a.getFollowerId())) {
                tempUserIds.add(a.getFollowerId());
            }
            if (StringUtils.isNotBlank(a.getCustomerCareId())) {
                tempUserIds.add(a.getCustomerCareId());
            }
            return tempUserIds;
        }).flatMap(Collection::stream).collect(Collectors.toSet());
    }

    public static Set<String> extractUserIdsFromDeliveryStatisticList(List<DeliveryProductStatisticDataBO> records){
        if (CollectionUtils.isEmpty(records)){
            return new HashSet<>();
        }
        return records.stream().map(a -> {
            List<String> tempUserIds = new ArrayList<>();
            if (StringUtils.isNotBlank(a.getDeliveryCustomerCareId())) {
                tempUserIds.add(a.getDeliveryCustomerCareId());
            }

            return tempUserIds;
        }).flatMap(Collection::stream).collect(Collectors.toSet());
    }




    public static Set<String> extractUserIdsFromDeliveryProduct(List<DeliveryProductPageDetailDTO> records) {
        if (CollectionUtils.isEmpty(records)){
            return new HashSet<>();
        }
        return records.stream().map(a -> {
            List<String> tempUserIds = new ArrayList<>();
            if (StringUtils.isNotBlank(a.getDeliveryCustomerCare())) {
                tempUserIds.add(a.getDeliveryCustomerCare());
            }
            return tempUserIds;
        }).flatMap(Collection::stream).collect(Collectors.toSet());
    }

    public static Set<String> extractUserIdsFromAfterSale(List<AfterSalePageDetailDTO> records) {
        if (CollectionUtils.isEmpty(records)){
            return new HashSet<>();
        }
        return records.stream().map(a -> {
            List<String> tempUserIds = new ArrayList<>();
            if (StringUtils.isNotBlank(a.getRetrieveUserId())) {
                tempUserIds.add(a.getRetrieveUserId());
            }
            if (StringUtils.isNotBlank(a.getDisputeUserId())) {
                tempUserIds.add(a.getDisputeUserId());
            }
            return tempUserIds;
        }).flatMap(Collection::stream).collect(Collectors.toSet());
    }

    public static Set<String> extractUserIdsFromComplaint(List<ComplaintPageDetailDTO> records) {
        if (CollectionUtils.isEmpty(records)){
            return new HashSet<>();
        }
        return records.stream().map(a -> {
            List<String> tempUserIds = new ArrayList<>();
            if (StringUtils.isNotBlank(a.getHandleUserId())) {
                tempUserIds.add(a.getHandleUserId());
            }
            return tempUserIds;
        }).flatMap(Collection::stream).collect(Collectors.toSet());
    }
}
