package com.pxb7.mall.workorder.domain.mapping;

import com.pxb7.mall.workorder.domain.model.ComplaintStatisticDataBO;
import com.pxb7.mall.workorder.domain.model.ComplaintStatisticDataSearchBO;
import com.pxb7.mall.workorder.domain.model.RemindComplaintBO;
import com.pxb7.mall.workorder.domain.model.RemindComplaintReqBO;
import com.pxb7.mall.workorder.domain.model.RemindComplaintRespBO;
import com.pxb7.mall.workorder.infra.model.ComplaintStatisticDataPO;
import com.pxb7.mall.workorder.infra.model.ComplaintStatisticSearchPO;
import com.pxb7.mall.workorder.infra.model.RemindComplaintReqPO;
import com.pxb7.mall.workorder.infra.repository.db.entity.RemindComplaint;

import java.util.List;

import com.pxb7.mall.workorder.infra.repository.es.entity.RemindComplaintDoc;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;


@Mapper
public interface RemindComplaintDomainMapping {

    RemindComplaintDomainMapping INSTANCE = Mappers.getMapper(RemindComplaintDomainMapping.class);


    RemindComplaintReqPO.AddPO remindComplaintBO2AddPO(RemindComplaintReqBO.AddBO source);

    RemindComplaintReqPO.UpdatePO remindComplaintBO2UpdatePO(RemindComplaintReqBO.UpdateBO source);

    RemindComplaintReqPO.DelPO remindComplaintBO2DelPO(RemindComplaintReqBO.DelBO source);

    RemindComplaintReqPO.SearchPO remindComplaintBO2SearchPO(RemindComplaintReqBO.SearchBO source);

    RemindComplaintReqPO.PagePO remindComplaintBO2PagePO(RemindComplaintReqBO.PageBO source);

    RemindComplaintRespBO.DetailBO remindComplaintPO2DetailBO(RemindComplaint source);

    List<RemindComplaintRespBO.DetailBO> remindComplaintPO2ListBO(List<RemindComplaint> source);

    Page<RemindComplaintRespBO.DetailBO> remindComplaintPO2PageBO(Page<RemindComplaint> source);

    List<RemindComplaintBO> remindComplaintDoc2ListBO(List<RemindComplaintDoc> list);

    RemindComplaintDoc remindComplaintPO2Doc(RemindComplaint remindComplaint);

    ComplaintStatisticSearchPO remindAfterSaleStatisticBO2SearchPO(ComplaintStatisticDataSearchBO searchBO);

    List<ComplaintStatisticDataBO> complaintStatisticDataPO2BO(List<ComplaintStatisticDataPO> complaintStatisticDataPOS);

    RemindComplaintBO remindComplaintDoc2BO(RemindComplaintDoc source);

}


