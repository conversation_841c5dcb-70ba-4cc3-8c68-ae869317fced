package com.pxb7.mall.workorder.domain.remind.extension.context;

import com.pxb7.mall.workorder.client.message.RemindPlanMessage;
import com.pxb7.mall.workorder.domain.model.BaseRemindRecordBO;
import com.pxb7.mall.workorder.domain.model.RemindMethodConfigBO;
import com.pxb7.mall.workorder.domain.model.RemindPlanGameConfigBO;
import com.pxb7.mall.workorder.domain.model.TimeConfigBO;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/4/2 15:56
 */

@Data
public class RemindBizContext {

    /**
     * 交付预警记录
     */
    private BaseRemindRecordBO remindRecord;

    /**
     * 预警计划消息
     */
    private RemindPlanMessage remindPlanMessage;

    /**
     * 预警计划游戏配置
     */
    private RemindPlanGameConfigBO remindPlanGameConfig;

    /**
     * 预警对象
     */
    private RemindMethodConfigBO.RemindObjectBO remindObjectBO;

    /**
     * 预警时间配置
     */
    private TimeConfigBO timeConfigBO;

}
