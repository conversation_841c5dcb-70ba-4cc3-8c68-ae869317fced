<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pxb7.mall.workorder.infra.repository.db.mapper.AdsPxb7ProductForewarningMapper">

    <resultMap id="BaseResultMap" type="com.pxb7.mall.workorder.infra.repository.db.entity.AdsPxb7ProductForewarning">
        <!--@Table ads_pxb7_product_forewarning-->
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="acceptedCompletedWorkOrderCnt" column="accepted_completed_work_order_cnt" jdbcType="INTEGER"/>
        <result property="acceptedTerminatedWorkOrderCnt" column="accepted_terminated_work_order_cnt" jdbcType="INTEGER"/>
        <result property="acceptedTimeoutedWorkOrderCnt" column="accepted_timeouted_work_order_cnt" jdbcType="INTEGER"/>
        <result property="acceptedTimeoutingWorkOrderCnt" column="accepted_timeouting_work_order_cnt" jdbcType="INTEGER"/>
        <result property="acceptedWorkOrderCnt" column="accepted_work_order_cnt" jdbcType="INTEGER"/>
        <result property="artDesignerId" column="art_designer_id" jdbcType="VARCHAR"/>
        <result property="auditUserId" column="audit_user_id" jdbcType="VARCHAR"/>
        <result property="createDate" column="create_date" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="createUserId" column="create_user_id" jdbcType="VARCHAR"/>
        <result property="followedCompletedWorkOrderCnt" column="followed_completed_work_order_cnt" jdbcType="INTEGER"/>
        <result property="followedTerminatedWorkOrderCnt" column="followed_terminated_work_order_cnt" jdbcType="INTEGER"/>
        <result property="followedTimeoutedWorkOrderCnt" column="followed_timeouted_work_order_cnt" jdbcType="INTEGER"/>
        <result property="followedTimeoutingWorkOrderCnt" column="followed_timeouting_work_order_cnt" jdbcType="INTEGER"/>
        <result property="followedWorkOrderCnt" column="followed_work_order_cnt" jdbcType="INTEGER"/>
        <result property="followerId" column="follower_id" jdbcType="VARCHAR"/>
        <result property="gameId" column="game_id" jdbcType="VARCHAR"/>
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
        <result property="onShelfType" column="on_shelf_type" jdbcType="INTEGER"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="updateUserId" column="update_user_id" jdbcType="VARCHAR"/>
        <result property="waitCompletedWorkOrderCnt" column="wait_completed_work_order_cnt" jdbcType="INTEGER"/>
        <result property="waitTimeoutedWorkOrderCnt" column="wait_timeouted_work_order_cnt" jdbcType="INTEGER"/>
        <result property="waitTimeoutingWorkOrderCnt" column="wait_timeouting_work_order_cnt" jdbcType="INTEGER"/>
        <result property="waitWorkOrderCnt" column="wait_work_order_cnt" jdbcType="INTEGER"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="BaseResultMap">
        select
id, accepted_completed_work_order_cnt, accepted_terminated_work_order_cnt, accepted_timeouted_work_order_cnt, accepted_timeouting_work_order_cnt, accepted_work_order_cnt, art_designer_id, audit_user_id, create_date, create_time, create_user_id, followed_completed_work_order_cnt, followed_terminated_work_order_cnt, followed_timeouted_work_order_cnt, followed_timeouting_work_order_cnt, followed_work_order_cnt, follower_id, game_id, is_deleted, on_shelf_type, update_time, update_user_id, wait_completed_work_order_cnt, wait_timeouted_work_order_cnt, wait_timeouting_work_order_cnt, wait_work_order_cnt
        from ads_pxb7_product_forewarning
        where id = #{id}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="BaseResultMap">
        select
id, accepted_completed_work_order_cnt, accepted_terminated_work_order_cnt, accepted_timeouted_work_order_cnt, accepted_timeouting_work_order_cnt, accepted_work_order_cnt, art_designer_id, audit_user_id, create_date, create_time, create_user_id, followed_completed_work_order_cnt, followed_terminated_work_order_cnt, followed_timeouted_work_order_cnt, followed_timeouting_work_order_cnt, followed_work_order_cnt, follower_id, game_id, is_deleted, on_shelf_type, update_time, update_user_id, wait_completed_work_order_cnt, wait_timeouted_work_order_cnt, wait_timeouting_work_order_cnt, wait_work_order_cnt
        from ads_pxb7_product_forewarning
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="acceptedCompletedWorkOrderCnt != null">
                and accepted_completed_work_order_cnt = #{acceptedCompletedWorkOrderCnt}
            </if>
            <if test="acceptedTerminatedWorkOrderCnt != null">
                and accepted_terminated_work_order_cnt = #{acceptedTerminatedWorkOrderCnt}
            </if>
            <if test="acceptedTimeoutedWorkOrderCnt != null">
                and accepted_timeouted_work_order_cnt = #{acceptedTimeoutedWorkOrderCnt}
            </if>
            <if test="acceptedTimeoutingWorkOrderCnt != null">
                and accepted_timeouting_work_order_cnt = #{acceptedTimeoutingWorkOrderCnt}
            </if>
            <if test="acceptedWorkOrderCnt != null">
                and accepted_work_order_cnt = #{acceptedWorkOrderCnt}
            </if>
            <if test="artDesignerId != null and artDesignerId != ''">
                and art_designer_id = #{artDesignerId}
            </if>
            <if test="auditUserId != null and auditUserId != ''">
                and audit_user_id = #{auditUserId}
            </if>
            <if test="createDate != null and createDate != ''">
                and create_date = #{createDate}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="createUserId != null and createUserId != ''">
                and create_user_id = #{createUserId}
            </if>
            <if test="followedCompletedWorkOrderCnt != null">
                and followed_completed_work_order_cnt = #{followedCompletedWorkOrderCnt}
            </if>
            <if test="followedTerminatedWorkOrderCnt != null">
                and followed_terminated_work_order_cnt = #{followedTerminatedWorkOrderCnt}
            </if>
            <if test="followedTimeoutedWorkOrderCnt != null">
                and followed_timeouted_work_order_cnt = #{followedTimeoutedWorkOrderCnt}
            </if>
            <if test="followedTimeoutingWorkOrderCnt != null">
                and followed_timeouting_work_order_cnt = #{followedTimeoutingWorkOrderCnt}
            </if>
            <if test="followedWorkOrderCnt != null">
                and followed_work_order_cnt = #{followedWorkOrderCnt}
            </if>
            <if test="followerId != null and followerId != ''">
                and follower_id = #{followerId}
            </if>
            <if test="gameId != null and gameId != ''">
                and game_id = #{gameId}
            </if>
            <if test="isDeleted != null">
                and is_deleted = #{isDeleted}
            </if>
            <if test="onShelfType != null">
                and on_shelf_type = #{onShelfType}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="updateUserId != null and updateUserId != ''">
                and update_user_id = #{updateUserId}
            </if>
            <if test="waitCompletedWorkOrderCnt != null">
                and wait_completed_work_order_cnt = #{waitCompletedWorkOrderCnt}
            </if>
            <if test="waitTimeoutedWorkOrderCnt != null">
                and wait_timeouted_work_order_cnt = #{waitTimeoutedWorkOrderCnt}
            </if>
            <if test="waitTimeoutingWorkOrderCnt != null">
                and wait_timeouting_work_order_cnt = #{waitTimeoutingWorkOrderCnt}
            </if>
            <if test="waitWorkOrderCnt != null">
                and wait_work_order_cnt = #{waitWorkOrderCnt}
            </if>
        </where>
        limit #{pageable.offset}, #{pageable.pageSize}
    </select>

    <!--通过实体作为筛选条件查询-->

    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from ads_pxb7_product_forewarning
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="acceptedCompletedWorkOrderCnt != null">
                and accepted_completed_work_order_cnt = #{acceptedCompletedWorkOrderCnt}
            </if>
            <if test="acceptedTerminatedWorkOrderCnt != null">
                and accepted_terminated_work_order_cnt = #{acceptedTerminatedWorkOrderCnt}
            </if>
            <if test="acceptedTimeoutedWorkOrderCnt != null">
                and accepted_timeouted_work_order_cnt = #{acceptedTimeoutedWorkOrderCnt}
            </if>
            <if test="acceptedTimeoutingWorkOrderCnt != null">
                and accepted_timeouting_work_order_cnt = #{acceptedTimeoutingWorkOrderCnt}
            </if>
            <if test="acceptedWorkOrderCnt != null">
                and accepted_work_order_cnt = #{acceptedWorkOrderCnt}
            </if>
            <if test="artDesignerId != null and artDesignerId != ''">
                and art_designer_id = #{artDesignerId}
            </if>
            <if test="auditUserId != null and auditUserId != ''">
                and audit_user_id = #{auditUserId}
            </if>
            <if test="createDate != null and createDate != ''">
                and create_date = #{createDate}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="createUserId != null and createUserId != ''">
                and create_user_id = #{createUserId}
            </if>
            <if test="followedCompletedWorkOrderCnt != null">
                and followed_completed_work_order_cnt = #{followedCompletedWorkOrderCnt}
            </if>
            <if test="followedTerminatedWorkOrderCnt != null">
                and followed_terminated_work_order_cnt = #{followedTerminatedWorkOrderCnt}
            </if>
            <if test="followedTimeoutedWorkOrderCnt != null">
                and followed_timeouted_work_order_cnt = #{followedTimeoutedWorkOrderCnt}
            </if>
            <if test="followedTimeoutingWorkOrderCnt != null">
                and followed_timeouting_work_order_cnt = #{followedTimeoutingWorkOrderCnt}
            </if>
            <if test="followedWorkOrderCnt != null">
                and followed_work_order_cnt = #{followedWorkOrderCnt}
            </if>
            <if test="followerId != null and followerId != ''">
                and follower_id = #{followerId}
            </if>
            <if test="gameId != null and gameId != ''">
                and game_id = #{gameId}
            </if>
            <if test="isDeleted != null">
                and is_deleted = #{isDeleted}
            </if>
            <if test="onShelfType != null">
                and on_shelf_type = #{onShelfType}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="updateUserId != null and updateUserId != ''">
                and update_user_id = #{updateUserId}
            </if>
            <if test="waitCompletedWorkOrderCnt != null">
                and wait_completed_work_order_cnt = #{waitCompletedWorkOrderCnt}
            </if>
            <if test="waitTimeoutedWorkOrderCnt != null">
                and wait_timeouted_work_order_cnt = #{waitTimeoutedWorkOrderCnt}
            </if>
            <if test="waitTimeoutingWorkOrderCnt != null">
                and wait_timeouting_work_order_cnt = #{waitTimeoutingWorkOrderCnt}
            </if>
            <if test="waitWorkOrderCnt != null">
                and wait_work_order_cnt = #{waitWorkOrderCnt}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into ads_pxb7_product_forewarning(accepted_completed_work_order_cnt, accepted_terminated_work_order_cnt, accepted_timeouted_work_order_cnt, accepted_timeouting_work_order_cnt, accepted_work_order_cnt, art_designer_id, audit_user_id, create_date, create_time, create_user_id, followed_completed_work_order_cnt, followed_terminated_work_order_cnt, followed_timeouted_work_order_cnt, followed_timeouting_work_order_cnt, followed_work_order_cnt, follower_id, game_id, is_deleted, on_shelf_type, update_time, update_user_id, wait_completed_work_order_cnt, wait_timeouted_work_order_cnt, wait_timeouting_work_order_cnt, wait_work_order_cnt)
        values (#{acceptedCompletedWorkOrderCnt}, #{acceptedTerminatedWorkOrderCnt}, #{acceptedTimeoutedWorkOrderCnt}, #{acceptedTimeoutingWorkOrderCnt}, #{acceptedWorkOrderCnt}, #{artDesignerId}, #{auditUserId}, #{createDate}, #{createTime}, #{createUserId}, #{followedCompletedWorkOrderCnt}, #{followedTerminatedWorkOrderCnt}, #{followedTimeoutedWorkOrderCnt}, #{followedTimeoutingWorkOrderCnt}, #{followedWorkOrderCnt}, #{followerId}, #{gameId}, #{isDeleted}, #{onShelfType}, #{updateTime}, #{updateUserId}, #{waitCompletedWorkOrderCnt}, #{waitTimeoutedWorkOrderCnt}, #{waitTimeoutingWorkOrderCnt}, #{waitWorkOrderCnt})
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into ads_pxb7_product_forewarning(accepted_completed_work_order_cnt, accepted_terminated_work_order_cnt, accepted_timeouted_work_order_cnt, accepted_timeouting_work_order_cnt, accepted_work_order_cnt, art_designer_id, audit_user_id, create_date, create_time, create_user_id, followed_completed_work_order_cnt, followed_terminated_work_order_cnt, followed_timeouted_work_order_cnt, followed_timeouting_work_order_cnt, followed_work_order_cnt, follower_id, game_id, is_deleted, on_shelf_type, update_time, update_user_id, wait_completed_work_order_cnt, wait_timeouted_work_order_cnt, wait_timeouting_work_order_cnt, wait_work_order_cnt)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.acceptedCompletedWorkOrderCnt}, #{entity.acceptedTerminatedWorkOrderCnt}, #{entity.acceptedTimeoutedWorkOrderCnt}, #{entity.acceptedTimeoutingWorkOrderCnt}, #{entity.acceptedWorkOrderCnt}, #{entity.artDesignerId}, #{entity.auditUserId}, #{entity.createDate}, #{entity.createTime}, #{entity.createUserId}, #{entity.followedCompletedWorkOrderCnt}, #{entity.followedTerminatedWorkOrderCnt}, #{entity.followedTimeoutedWorkOrderCnt}, #{entity.followedTimeoutingWorkOrderCnt}, #{entity.followedWorkOrderCnt}, #{entity.followerId}, #{entity.gameId}, #{entity.isDeleted}, #{entity.onShelfType}, #{entity.updateTime}, #{entity.updateUserId}, #{entity.waitCompletedWorkOrderCnt}, #{entity.waitTimeoutedWorkOrderCnt}, #{entity.waitTimeoutingWorkOrderCnt}, #{entity.waitWorkOrderCnt})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into ads_pxb7_product_forewarning(accepted_completed_work_order_cnt, accepted_terminated_work_order_cnt, accepted_timeouted_work_order_cnt, accepted_timeouting_work_order_cnt, accepted_work_order_cnt, art_designer_id, audit_user_id, create_date, create_time, create_user_id, followed_completed_work_order_cnt, followed_terminated_work_order_cnt, followed_timeouted_work_order_cnt, followed_timeouting_work_order_cnt, followed_work_order_cnt, follower_id, game_id, is_deleted, on_shelf_type, update_time, update_user_id, wait_completed_work_order_cnt, wait_timeouted_work_order_cnt, wait_timeouting_work_order_cnt, wait_work_order_cnt)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.acceptedCompletedWorkOrderCnt}, #{entity.acceptedTerminatedWorkOrderCnt}, #{entity.acceptedTimeoutedWorkOrderCnt}, #{entity.acceptedTimeoutingWorkOrderCnt}, #{entity.acceptedWorkOrderCnt}, #{entity.artDesignerId}, #{entity.auditUserId}, #{entity.createDate}, #{entity.createTime}, #{entity.createUserId}, #{entity.followedCompletedWorkOrderCnt}, #{entity.followedTerminatedWorkOrderCnt}, #{entity.followedTimeoutedWorkOrderCnt}, #{entity.followedTimeoutingWorkOrderCnt}, #{entity.followedWorkOrderCnt}, #{entity.followerId}, #{entity.gameId}, #{entity.isDeleted}, #{entity.onShelfType}, #{entity.updateTime}, #{entity.updateUserId}, #{entity.waitCompletedWorkOrderCnt}, #{entity.waitTimeoutedWorkOrderCnt}, #{entity.waitTimeoutingWorkOrderCnt}, #{entity.waitWorkOrderCnt})
        </foreach>
        on duplicate key update
accepted_completed_work_order_cnt = values(accepted_completed_work_order_cnt),
accepted_terminated_work_order_cnt = values(accepted_terminated_work_order_cnt),
accepted_timeouted_work_order_cnt = values(accepted_timeouted_work_order_cnt),
accepted_timeouting_work_order_cnt = values(accepted_timeouting_work_order_cnt),
accepted_work_order_cnt = values(accepted_work_order_cnt),
art_designer_id = values(art_designer_id),
audit_user_id = values(audit_user_id),
create_date = values(create_date),
create_time = values(create_time),
create_user_id = values(create_user_id),
followed_completed_work_order_cnt = values(followed_completed_work_order_cnt),
followed_terminated_work_order_cnt = values(followed_terminated_work_order_cnt),
followed_timeouted_work_order_cnt = values(followed_timeouted_work_order_cnt),
followed_timeouting_work_order_cnt = values(followed_timeouting_work_order_cnt),
followed_work_order_cnt = values(followed_work_order_cnt),
follower_id = values(follower_id),
game_id = values(game_id),
is_deleted = values(is_deleted),
on_shelf_type = values(on_shelf_type),
update_time = values(update_time),
update_user_id = values(update_user_id),
wait_completed_work_order_cnt = values(wait_completed_work_order_cnt),
wait_timeouted_work_order_cnt = values(wait_timeouted_work_order_cnt),
wait_timeouting_work_order_cnt = values(wait_timeouting_work_order_cnt),
wait_work_order_cnt = values(wait_work_order_cnt)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update ads_pxb7_product_forewarning
        <set>
            <if test="acceptedCompletedWorkOrderCnt != null">
                accepted_completed_work_order_cnt = #{acceptedCompletedWorkOrderCnt},
            </if>
            <if test="acceptedTerminatedWorkOrderCnt != null">
                accepted_terminated_work_order_cnt = #{acceptedTerminatedWorkOrderCnt},
            </if>
            <if test="acceptedTimeoutedWorkOrderCnt != null">
                accepted_timeouted_work_order_cnt = #{acceptedTimeoutedWorkOrderCnt},
            </if>
            <if test="acceptedTimeoutingWorkOrderCnt != null">
                accepted_timeouting_work_order_cnt = #{acceptedTimeoutingWorkOrderCnt},
            </if>
            <if test="acceptedWorkOrderCnt != null">
                accepted_work_order_cnt = #{acceptedWorkOrderCnt},
            </if>
            <if test="artDesignerId != null and artDesignerId != ''">
                art_designer_id = #{artDesignerId},
            </if>
            <if test="auditUserId != null and auditUserId != ''">
                audit_user_id = #{auditUserId},
            </if>
            <if test="createDate != null and createDate != ''">
                create_date = #{createDate},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="createUserId != null and createUserId != ''">
                create_user_id = #{createUserId},
            </if>
            <if test="followedCompletedWorkOrderCnt != null">
                followed_completed_work_order_cnt = #{followedCompletedWorkOrderCnt},
            </if>
            <if test="followedTerminatedWorkOrderCnt != null">
                followed_terminated_work_order_cnt = #{followedTerminatedWorkOrderCnt},
            </if>
            <if test="followedTimeoutedWorkOrderCnt != null">
                followed_timeouted_work_order_cnt = #{followedTimeoutedWorkOrderCnt},
            </if>
            <if test="followedTimeoutingWorkOrderCnt != null">
                followed_timeouting_work_order_cnt = #{followedTimeoutingWorkOrderCnt},
            </if>
            <if test="followedWorkOrderCnt != null">
                followed_work_order_cnt = #{followedWorkOrderCnt},
            </if>
            <if test="followerId != null and followerId != ''">
                follower_id = #{followerId},
            </if>
            <if test="gameId != null and gameId != ''">
                game_id = #{gameId},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted},
            </if>
            <if test="onShelfType != null">
                on_shelf_type = #{onShelfType},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="updateUserId != null and updateUserId != ''">
                update_user_id = #{updateUserId},
            </if>
            <if test="waitCompletedWorkOrderCnt != null">
                wait_completed_work_order_cnt = #{waitCompletedWorkOrderCnt},
            </if>
            <if test="waitTimeoutedWorkOrderCnt != null">
                wait_timeouted_work_order_cnt = #{waitTimeoutedWorkOrderCnt},
            </if>
            <if test="waitTimeoutingWorkOrderCnt != null">
                wait_timeouting_work_order_cnt = #{waitTimeoutingWorkOrderCnt},
            </if>
            <if test="waitWorkOrderCnt != null">
                wait_work_order_cnt = #{waitWorkOrderCnt},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from ads_pxb7_product_forewarning where id = #{id}
    </delete>


    <select id="countBySearchParam" resultType="com.pxb7.mall.workorder.infra.model.ProductForewarningStatictisDataPO"
            parameterType="com.pxb7.mall.workorder.infra.model.ProductForewarningStatictisSearchPO">
        SELECT
        create_date
        <if test="param.gameIds !=null and !param.gameIds.isEmpty()">
            ,game_id
        </if>
        <if test="param.onShelfType != null and param.onShelfType != ''">
            ,on_shelf_type
        </if>
        <if test="param.customerCareIds !=null and !param.customerCareIds.isEmpty()">
            ,audit_user_id
        </if>
        <if test="param.artDesignerIds !=null and !param.artDesignerIds.isEmpty()">
            ,art_designer_id
        </if>
        <if test="param.followerIds !=null and !param.followerIds.isEmpty()">
            ,follower_id
        </if>
        ,sum(wait_work_order_cnt ) AS 'wait_work_order_cnt',
        sum(wait_completed_work_order_cnt) AS 'wait_completed_work_order_cnt',
        sum(wait_timeouting_work_order_cnt) AS 'wait_timeouting_work_order_cnt',
        sum(wait_timeouted_work_order_cnt) AS 'wait_timeouted_work_order_cnt',

        sum(accepted_work_order_cnt) AS 'accepted_work_order_cnt',
        sum(accepted_completed_work_order_cnt) AS 'accepted_completed_work_order_cnt',
        sum(accepted_terminated_work_order_cnt) AS 'accepted_terminated_work_order_cnt',
        sum(accepted_timeouting_work_order_cnt) AS 'accepted_timeouting_work_order_cnt',
        sum(accepted_timeouted_work_order_cnt) AS 'accepted_timeouted_work_order_cnt',

        sum(followed_work_order_cnt) AS 'followed_work_order_cnt',
        sum(followed_completed_work_order_cnt) AS 'followed_completed_work_order_cnt',
        sum(followed_terminated_work_order_cnt) AS 'followed_terminated_work_order_cnt',
        sum(followed_timeouting_work_order_cnt) AS 'followed_timeouting_work_order_cnt',
        sum(followed_timeouted_work_order_cnt) AS 'followed_timeouted_work_order_cnt'

        FROM
        ads_pxb7_product_forewarning
        where
        is_deleted = 0
        and create_date BETWEEN #{param.startDate} AND #{param.endDate}
        <if test="param.gameIds !=null and !param.gameIds.isEmpty()">
            and game_id IN
            <foreach collection="param.gameIds" item="gameId" separator="," open="(" close=")">
                <!-- 使用 #{gameId} 防止 SQL 注入 -->
                #{gameId}
            </foreach>
        </if>
        <if test="param.onShelfType != null">
            and on_shelf_type = #{param.onShelfType}
        </if>
        <if test="param.customerCareIds !=null and !param.customerCareIds.isEmpty()">
            and audit_user_id IN
            <foreach collection="param.customerCareIds" item="customerCareId" separator="," open="(" close=")">
                #{customerCareId}
            </foreach>
        </if>
        <if test="param.artDesignerIds !=null and !param.artDesignerIds.isEmpty()">
            and audit_user_id IN
            <foreach collection="param.artDesignerIds" item="artDesignerId" separator="," open="(" close=")">
                #{artDesignerId}
            </foreach>
        </if>
        <if test="param.followerIds !=null and !param.followerIds.isEmpty()">
            and follower_id IN
            <foreach collection="param.followerIds" item="followerId" separator="," open="(" close=")">
                #{followerId}
            </foreach>
        </if>
        group by create_date
        <if test="param.gameIds !=null and !param.gameIds.isEmpty()">
            ,game_id
        </if>
        <if test="param.onShelfType != null and param.onShelfType != ''">
            ,on_shelf_type
        </if>
        <if test="param.customerCareIds !=null and !param.customerCareIds.isEmpty()">
            ,audit_user_id
        </if>
        <if test="param.artDesignerIds !=null and !param.artDesignerIds.isEmpty()">
            ,art_designer_id
        </if>
        <if test="param.followerIds !=null and !param.followerIds.isEmpty()">
            ,follower_id
        </if>
    </select>

</mapper>

