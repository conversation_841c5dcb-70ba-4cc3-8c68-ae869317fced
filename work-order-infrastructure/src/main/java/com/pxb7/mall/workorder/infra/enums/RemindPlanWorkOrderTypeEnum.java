package com.pxb7.mall.workorder.infra.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum RemindPlanWorkOrderTypeEnum {

    RETRIEVE(1, "找回"),
    DISPUTE(2, "纠纷"),
    COMPLAINT(3, "投诉");

    private final Integer value;

    private final String label;

    public static String getLabel(Integer value) {
        return Arrays.stream(RemindPlanWorkOrderTypeEnum.values()).filter(e -> e.getValue().equals(value))
            .map(RemindPlanWorkOrderTypeEnum::getLabel).findAny().orElse("");
    }

    public static RemindPlanWorkOrderTypeEnum getEnum(Integer value) {
        if (null == value) {
            return null;
        }
        return Arrays.stream(RemindPlanWorkOrderTypeEnum.values()).filter(e -> value.equals(e.getValue())).findFirst()
            .orElse(null);
    }

    public boolean eq(Integer value) {
        return this.getValue().equals(value);
    }

}
