package com.pxb7.mall.workorder.adapter.consumer;

import com.alibaba.fastjson.JSON;
import com.pxb7.mall.components.rocketmq.RocketMQListenerExt;
import com.pxb7.mall.trade.ofs.delivery.client.dto.model.DeliveryStatusChangeDTO;
import com.pxb7.mall.trade.order.client.message.OrderItemStatusChangeMQDTO;
import com.pxb7.mall.workorder.app.service.RemindDeliveryProductAppService;
import com.pxb7.mall.workorder.infra.constant.RMQConstant;
import com.pxb7.mall.workorder.infra.enums.OrderItemStatusEnum;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.annotation.RocketMQMessageListener;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.springframework.stereotype.Service;

/**
 * 监听订单交付状态变更消息
 * <AUTHOR>
 */
@Slf4j
@Service
@RocketMQMessageListener(
        consumerGroup = RMQConstant.DELIVER_STATUS_CHANGE_GROUP,
        topic = RMQConstant.DELIVER_STATUS_CHANGE_TOPIC,
        tag = RMQConstant.ALL_TAG)
public class DeliverStatusMessageConsumer implements RocketMQListenerExt<DeliveryStatusChangeDTO> {

    @Resource
    private RemindDeliveryProductAppService remindDeliveryProductAppService;

    @Override
    public ConsumeResult consume(MessageView messageView, DeliveryStatusChangeDTO dto) {
        log.info("监听到交付状态变更消息: messageId = {},message = {}", messageView.getMessageId(), JSON.toJSONString(dto));
        Integer status = dto.getStatus();
        String orderItemId = dto.getOrderItemId();
        try {
            if (StringUtils.isBlank(orderItemId) || status == null) {
                return ConsumeResult.SUCCESS;
            }
            //交付状态变更，修改预警执行计划状态
            remindDeliveryProductAppService.changeRemindPlanStatusByDeliverStatusChange(dto);
            return ConsumeResult.SUCCESS;
        } catch (Exception e) {
            log.error("error in consumed from order_status_change_topic: orderId = {}", status, e);
            return ConsumeResult.FAILURE;
        }
    }
}

