package com.pxb7.mall.workorder.client.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
@Accessors(chain = true)
@ToString
public class DbRecordSyncDTO {

    private String tableName;

    private List<Object> syncKeys;

    private List<String> operateKeys;

    private LocalDateTime sendTime;

    private String operateType;
}
