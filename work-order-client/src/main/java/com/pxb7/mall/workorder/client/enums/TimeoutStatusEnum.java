package com.pxb7.mall.workorder.client.enums;

import lombok.Getter;
import lombok.ToString;

/**
 * 超时状态
 * <AUTHOR>
 */
@Getter
@ToString
public enum TimeoutStatusEnum {

    /**
     * 超时状态，0: 无 1:即将超时 2:已超时
     */
    NONE(0, "无"),
    WILL_TIMEOUT(1, "即将超时"),
    TIMEOUT(2, "已超时");

    private final Integer code;
    private final String desc;

    TimeoutStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}
