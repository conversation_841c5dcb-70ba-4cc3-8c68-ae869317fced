package com.pxb7.mall.workorder.domain.model;

import java.util.Date;
import java.time.*;
import java.math.BigDecimal;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.ToString;


/**
 * 售后工单预警记录(RemindAfterSale)实体类
 *
 * <AUTHOR>
 * @since 2025-04-24 23:32:52
 */
public class RemindAfterSaleRespBO {


    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class DetailBO {
        private Long id;
        private String remindId;
        private String workOrderId;
        private String workOrderNo;
        private String productId;
        private String productCode;
        private String gameId;
        private String groupId;
        private String orderId;
        private String orderItemId;
        private Integer completeStatus;
        private Integer timeOutStatus;
        private Integer workOrderType;
        private String retrieveUserId;
        private String disputeUserId;
        private LocalDateTime expectCompleteTime;
        private LocalDateTime completeTime;
        private Long gameConfigId;
        private Long remindPlanId;
        private Long remindSubPlanId;
        private LocalDateTime createTime;
        private LocalDateTime updateTime;
        private String createUserId;
        private String updateUserId;
        private Boolean deleted;
    }
}

