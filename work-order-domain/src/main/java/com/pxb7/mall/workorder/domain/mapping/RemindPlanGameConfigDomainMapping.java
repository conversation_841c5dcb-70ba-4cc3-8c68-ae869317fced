package com.pxb7.mall.workorder.domain.mapping;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pxb7.mall.workorder.domain.model.RemindMethodConfigBO;
import com.pxb7.mall.workorder.domain.model.RemindPlanGameConfigBO;
import com.pxb7.mall.workorder.domain.model.RemindPlanGameConfigReqBO;
import com.pxb7.mall.workorder.domain.model.TimeConfigBO;
import com.pxb7.mall.workorder.infra.model.RemindPlanGameConfigReqPO;
import com.pxb7.mall.workorder.infra.repository.db.entity.RemindPlanGameConfig;
import com.pxb7.mall.workorder.domain.model.RemindPlanGameConfigRespBO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(imports = {com.alibaba.fastjson2.JSON.class, TimeConfigBO.class,
    RemindMethodConfigBO.class})
public interface RemindPlanGameConfigDomainMapping {

    RemindPlanGameConfigDomainMapping INSTANCE = Mappers.getMapper(RemindPlanGameConfigDomainMapping.class);

    @Mapping(target = "expectCompleteTimeConfig", expression = "java(JSON.toJSONString(source.getExpectCompleteTimeConfig()))")
    @Mapping(target = "imCountDownTimeConfig", expression = "java(JSON.toJSONString(source.getImCountDownTimeConfig()))")
    RemindPlanGameConfigReqPO.AddPO remindPlanGameConfigBO2AddPO(RemindPlanGameConfigBO source);

    @Mapping(target = "expectCompleteTimeConfig", expression = "java(JSON.toJSONString(source.getExpectCompleteTimeConfig()))")
    @Mapping(target = "imCountDownTimeConfig", expression = "java(JSON.toJSONString(source.getImCountDownTimeConfig()))")
    RemindPlanGameConfigReqPO.UpdatePO remindPlanGameConfigBO2UpdatePO(RemindPlanGameConfigBO source);

    RemindPlanGameConfigReqPO.DelPO remindPlanGameConfigBO2DelPO(RemindPlanGameConfigReqBO.DelBO source);

    @Mapping(target = "expectCompleteTimeConfig", expression = "java(JSON.toJSONString(source.getExpectCompleteTimeConfig()))")
    @Mapping(target = "imCountDownTimeConfig", expression = "java(JSON.toJSONString(source.getImCountDownTimeConfig()))")
    RemindPlanGameConfigReqPO.SearchPO remindPlanGameConfigBO2SearchPO(RemindPlanGameConfigReqBO.SearchBO source);

    @Mapping(target = "expectCompleteTimeConfig", expression = "java(JSON.toJSONString(source.getExpectCompleteTimeConfig()))")
    @Mapping(target = "imCountDownTimeConfig", expression = "java(JSON.toJSONString(source.getImCountDownTimeConfig()))")
    RemindPlanGameConfigReqPO.PagePO remindPlanGameConfigBO2PagePO(RemindPlanGameConfigReqBO.PageBO source);

    RemindPlanGameConfigRespBO.DetailBO remindPlanGameConfigPO2DetailBO(RemindPlanGameConfig source);

    @Mapping(target = "expectCompleteTimeConfig", expression = "java(JSON.parseObject(source.getExpectCompleteTimeConfig(), TimeConfigBO.class))")
    @Mapping(target = "imCountDownTimeConfig", expression = "java(JSON.parseObject(source.getImCountDownTimeConfig(), TimeConfigBO.class))")
    RemindPlanGameConfigBO remindPlanGameConfigPO2BO(RemindPlanGameConfig source);

    @Mapping(target = "expectCompleteTimeConfig", expression = "java(JSON.parseObject(source.getExpectCompleteTimeConfig(), TimeConfigBO.class))")
    @Mapping(target = "imCountDownTimeConfig", expression = "java(JSON.parseObject(source.getImCountDownTimeConfig(), TimeConfigBO.class))")
    RemindPlanGameConfigBO remindPlanGameConfigPO2BO(RemindPlanGameConfigRespBO.DetailBO source);

    List<RemindPlanGameConfigBO> remindPlanGameConfigPO2ListBO(List<RemindPlanGameConfig> source);

    Page<RemindPlanGameConfigRespBO.DetailBO> remindPlanGameConfigPO2PageBO(Page<RemindPlanGameConfig> source);
}


