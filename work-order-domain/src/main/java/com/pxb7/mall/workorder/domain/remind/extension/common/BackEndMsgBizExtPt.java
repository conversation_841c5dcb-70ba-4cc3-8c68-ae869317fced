package com.pxb7.mall.workorder.domain.remind.extension.common;

import com.alibaba.cola.extension.Extensions;
import com.alibaba.fastjson2.JSON;
import com.pxb7.mall.common.client.enums.BusinessTypeEnum;
import com.pxb7.mall.common.client.enums.SystemUserInternalMsgTypeEnum;
import com.pxb7.mall.common.client.request.message.SysUserInternalMsgReqDTO;
import com.pxb7.mall.common.client.request.message.SysUserToastMsgReqDTO;
import com.pxb7.mall.workorder.client.constant.MessageTemplateConstant;
import com.pxb7.mall.workorder.client.enums.BizTypeEnum;
import com.pxb7.mall.workorder.client.enums.RemindMethodEnum;
import com.pxb7.mall.workorder.client.enums.RemindObjectEnum;
import com.pxb7.mall.workorder.client.message.RemindPlanMessage;
import com.pxb7.mall.workorder.domain.model.*;
import com.pxb7.mall.workorder.domain.remind.extension.BaseMessageService;
import com.pxb7.mall.workorder.domain.remind.extension.RemindBizExtPt;
import com.pxb7.mall.workorder.domain.remind.extension.context.RemindBizContext;
import com.pxb7.mall.workorder.infra.enums.RemindPlanWorkOrderTypeEnum;
import com.pxb7.mall.workorder.infra.repository.gateway.dubbo.common.MessageGateway;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

import static com.pxb7.mall.workorder.infra.constant.CommonConstants.SYS_DEFAULT_USER_0;

/**
 * 管理后台消息提醒
 * <AUTHOR>
 * @date 2025/4/2 14:56
 */

@Slf4j
@Extensions(
        bizId = {BizTypeEnum.Constants.AFTER_SALE,
                BizTypeEnum.Constants.COMPLAINT},
        useCase = {RemindMethodEnum.Constants.BACKEND},
        scenario = {RemindObjectEnum.Constants.CUSTOMER_CARE})
public class BackEndMsgBizExtPt extends BaseMessageService implements RemindBizExtPt {


    @Resource
    private MessageGateway messageGateway;

    @Override
    public void executeRemindTask(RemindBizContext context) {
        BaseRemindRecordBO remindRecord = context.getRemindRecord();
        RemindPlanMessage remindPlanMessage = context.getRemindPlanMessage();
        RemindSubPlanRespBO.DetailBO remindSubPlan =
                subPlanDomainService.findById(remindPlanMessage.getRemindSubPlanId());

        //获取提醒对象
        String userId;
        if (remindRecord instanceof RemindAfterSaleBO remindAfterSale) {
            userId = RemindPlanWorkOrderTypeEnum.RETRIEVE.eq(remindSubPlan.getWorkOrderType()) ?
                    remindAfterSale.getRetrieveUserId() : remindAfterSale.getDisputeUserId();
        } else if (remindRecord instanceof RemindComplaintBO remindComplaint) {
            userId = remindComplaint.getHandleUserId();
        } else {
            // 添加默认处理分支，防止未来类型扩展导致逻辑静默失败
            throw new IllegalArgumentException("Unsupported remindRecord type: " + remindRecord.getClass().getName());
        }

        if (StringUtils.isBlank(userId)
                || Objects.equals(userId, SYS_DEFAULT_USER_0)) {
            log.error("Fail to get systemUserId. planRecordId:{}", remindPlanMessage.getPlanRecordId());
            return;
        }

        //发送站内信
        setInternalMessage(context, remindSubPlan, userId);
        //发送管理后台弹窗消息
        sendToastMessage(context, userId);
    }

    /**
     * 发送站内信
     * @param context
     * @param remindSubPlan
     * @param userId
     */
    private void setInternalMessage(RemindBizContext context, RemindSubPlanRespBO.DetailBO remindSubPlan, String userId) {
        String businessId = context.getRemindPlanMessage().getPlanRecordId();
        Long remindPlanId = context.getRemindPlanMessage().getRemindPlanId();
        BaseRemindRecordBO remindRecord = context.getRemindRecord();

        //获取消息标题
        RemindPlanRespBO.DetailBO remindPlan = remindPlanDomainService.findById(remindPlanId);

        String title;
        String content;
        String jumpUrl;
        if (remindRecord instanceof RemindAfterSaleBO remindAfterSale) {
            title = RemindPlanWorkOrderTypeEnum.RETRIEVE.eq(remindSubPlan.getWorkOrderType()) ?
                    MessageTemplateConstant.AFTER_SALE_RETRIEVE_REMIND_TITLE_BACK_END : MessageTemplateConstant.AFTER_SALE_DISPUTE_REMIND_TITLE_BACK_END;
            title = String.format(title, remindPlan.getPlanName());
            content = getAfterSaleMessageContent(context, remindSubPlan, RemindMethodEnum.BACKEND_MANAGE);
            jumpUrl = (RemindPlanWorkOrderTypeEnum.RETRIEVE.eq(remindSubPlan.getWorkOrderType()) ?
                    "/afterSales/find" : "/afterSales/dispute") + "?orderInfo=" + remindAfterSale.getWorkOrderNo();
        } else if (remindRecord instanceof RemindComplaintBO remindComplaint) {
            title = String.format(MessageTemplateConstant.COMPLAINT_REMIND_TITLE_BACK_END, remindPlan.getPlanName());
            content = getComplaintMessageContent(context, remindSubPlan, RemindMethodEnum.BACKEND_MANAGE);
            jumpUrl = "/complaintOrder/complaintsList?complaintWorkId=" + remindComplaint.getWorkOrderId();
        } else {
            // 添加默认处理分支，防止未来类型扩展导致逻辑静默失败
            throw new IllegalArgumentException("Unsupported remindRecord type: " + remindRecord.getClass().getName());
        }

        if (StringUtils.isBlank(content)) {
            log.error("Fail to get work order message content. planRecordId:{}", businessId);
            return;
        }
        //向后台系统用户发送站内信
        SysUserInternalMsgReqDTO msgReqDTO = new SysUserInternalMsgReqDTO();
        msgReqDTO.setTitle(title);
        msgReqDTO.setContent(content);
        msgReqDTO.setMsgType(SystemUserInternalMsgTypeEnum.SERVICE_TIME_WARNING.getCode());
        msgReqDTO.setBusinessType(BusinessTypeEnum.SERVICE_TIME_WARNING.getCode());
        msgReqDTO.setJumpUrl(jumpUrl);
        msgReqDTO.setSystemUserId(userId);
        msgReqDTO.setBusinessId(businessId + userId);
        String messageId = messageGateway.sendInternalMessage(msgReqDTO);
        log.info("Send message to the systemUserMessageService#sendInternalMessage,msgReqDTO:{},messageId:{}",
                    JSON.toJSONString(msgReqDTO), messageId);
    }

    /**
     * 发送管理后台弹窗消息
     * @param context
     * @param userId
     */
    private void sendToastMessage(RemindBizContext context, String userId) {
        String businessId = context.getRemindPlanMessage().getPlanRecordId();
        BaseRemindRecordBO remindRecord = context.getRemindRecord();
        String title;
        if (remindRecord instanceof RemindAfterSaleBO remindAfterSale) {
            title = RemindPlanWorkOrderTypeEnum.RETRIEVE.eq(remindAfterSale.getWorkOrderType()) ?
                    MessageTemplateConstant.AFTER_SALE_RETRIEVE_REMIND_TITLE : MessageTemplateConstant.AFTER_SALE_DISPUTE_REMIND_TITLE;
        } else if (remindRecord instanceof RemindComplaintBO) {
            title = MessageTemplateConstant.COMPLAINT_REMIND_TITLE;
        } else {
            // 添加默认处理分支，防止未来类型扩展导致逻辑静默失败
            throw new IllegalArgumentException("Unsupported remindRecord type: " + remindRecord.getClass().getName());
        }
        //获取工单后台弹窗消息
        String toastContent = getToastMessageContent(context);
        if (StringUtils.isBlank(toastContent)) {
            log.error("Fail to get work order toast message content. planRecordId:{}", businessId);
            return;
        }
        //向后台系统用户发送弹窗消息
        SysUserToastMsgReqDTO toastMsgReqDTO = new SysUserToastMsgReqDTO();
        toastMsgReqDTO.setTitle(title);
        toastMsgReqDTO.setContent(toastContent);
        toastMsgReqDTO.setBusinessType(BusinessTypeEnum.SERVICE_TIME_WARNING.getCode());
        toastMsgReqDTO.setSystemUserId(userId);
        toastMsgReqDTO.setBusinessId(businessId + userId);
        String messageId = messageGateway.sendToastMessage(toastMsgReqDTO);
        log.info("Send message to the systemUserMessageService#sendToastMessage,toastMsgReqDTO:{},messageId:{}",
                JSON.toJSONString(toastMsgReqDTO), messageId);
    }

}
