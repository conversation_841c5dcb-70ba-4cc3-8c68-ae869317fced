package com.pxb7.mall.workorder.infra.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

@Getter
@AllArgsConstructor
public enum RemindPlanOnShelfTypeEnum {

    /**
     * 1:官方截图，2:自主截图
     */
    OFFICIAL_SCREENSHOT(1, "官方截图", "officialScreenshot"),
    SELF_SERVICE_SCREENSHOT(2, "自主截图", "selfServiceScreenshot"),
    ;

    private final Integer value;
    private final String label;
    private final String bizCode;

    public static RemindPlanOnShelfTypeEnum getEnum(Integer value) {
        if (null == value) {
            return null;
        }
        return Arrays.stream(RemindPlanOnShelfTypeEnum.values()).filter(e -> value.equals(e.getValue())).findFirst()
            .orElse(null);
    }

    public static List<Integer> getAllTypes() {
        return Arrays.stream(RemindPlanOnShelfTypeEnum.values()).map(RemindPlanOnShelfTypeEnum::getValue).toList();
    }


}
