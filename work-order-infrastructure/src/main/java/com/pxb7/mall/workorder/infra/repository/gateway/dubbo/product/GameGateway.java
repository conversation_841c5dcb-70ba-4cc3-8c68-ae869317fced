package com.pxb7.mall.workorder.infra.repository.gateway.dubbo.product;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.cola.dto.MultiResponse;
import com.pxb7.mall.workorder.infra.mapping.GameBaseDomainMapping;
import com.pxb7.mall.workorder.infra.model.GameBasePO;
import com.pxb7.mall.product.client.api.admin.GameServiceI;
import com.pxb7.mall.product.client.dto.response.game.GameBaseDTO;
import com.pxb7.mall.product.client.dto.response.game.GameIdDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Repository
@Slf4j
public class GameGateway {

    @DubboReference(check = false)
    private GameServiceI gameAdminServiceI;

    public List<GameBasePO> getAllGameList(String gameName) {

        MultiResponse<GameBaseDTO> response = gameAdminServiceI.selectAll();
        if (null == response || CollectionUtil.isEmpty(response.getData())) {
            return Collections.emptyList();
        }
        List<GameBaseDTO> data = response.getData();
        if (StringUtils.isNotBlank(gameName)) {
            List<GameBaseDTO> list = data.stream().filter(a -> a.getGameName().contains(gameName)).toList();
            return GameBaseDomainMapping.INSTANCE.dto2ListPO(list);
        }
        return GameBaseDomainMapping.INSTANCE.dto2ListPO(data);
    }

    public List<GameBasePO> getGameInfoByIds(List<String> gameIds) {
        if (CollectionUtil.isEmpty(gameIds)) {
            return Collections.emptyList();
        }
        MultiResponse<GameBaseDTO> response = gameAdminServiceI.selectGameBaseInfo(gameIds);
        if (null == response || CollectionUtil.isEmpty(response.getData())) {
            return Collections.emptyList();
        }
        List<GameBaseDTO> data = response.getData();
        return GameBaseDomainMapping.INSTANCE.dto2ListPO(data);
    }

}
