package com.pxb7.mall.workorder.app.model;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import javax.annotation.Nullable;

@Getter
@Setter
@Accessors(chain = true)
@ToString
public class ComplaintPlanGameConfigDTO {

    /**
     * 预警游戏配置Id
     *
     */
    @Nullable
    private Long id;

    /**
     * 投诉渠道 1:IM 2支付宝 3闲鱼 4:12315 5消费宝 6连连支付 7电话 8反诈邮箱 9外部门升级 10黑猫投诉 11工商局 12工信部
     */
    @NotNull(message = "请选择投诉渠道")
    @Min(value = 1, message = "投诉渠道参数错误")
    private Integer channel;

    /**
     *  游戏完成时间,{"hours":"10","minutes":"30"}
     */
    @Valid
    //@NotNull(message = "预期完成时间不能为空")
    private TimeConfigDTO expectCompleteTimeConfig;

    /**
     *  im客服端倒计时,{"hours":"10","minutes":"30"}
     */
    @Valid
    //@NotNull(message = "im客服端倒计时不能为空")
    private TimeConfigDTO imCountDownTimeConfig;


}
