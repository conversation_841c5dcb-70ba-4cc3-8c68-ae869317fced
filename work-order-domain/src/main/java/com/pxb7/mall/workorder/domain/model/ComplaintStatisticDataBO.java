package com.pxb7.mall.workorder.domain.model;

import java.math.BigDecimal;
import java.math.RoundingMode;

import lombok.Data;

@Data
public class ComplaintStatisticDataBO {

    /**
     * 日期
     */
    private String createDate;

    /**
     * 客诉工单数量
     */
    private Long workOrderCnt;
    /**
     * 已完结工单数量
     */
    private Long completeWorkOrderCnt;
    /**
     * 处理中工单数量
     */
    private Long processingWorkOrderCnt;
    /**
     * 即将超时工单数量
     */
    private Long timeoutingWorkOrderCnt;
    /**
     * 已超时工单数量
     */
    private Long timeoutedWorkOrderCnt;

    /**
     * 超时率= 已超时数/处理总数量
     *
     * timeoutCount/processTotalCount
     */
    public BigDecimal getTimeoutRate() {
        if (timeoutedWorkOrderCnt == null) {
            return BigDecimal.ZERO;
        }
        if (workOrderCnt == null || workOrderCnt <= 0) {
            return null; // 处理分母为零的情况
        }
        // 计算超时率
        try {
            BigDecimal timeout = BigDecimal.valueOf(timeoutedWorkOrderCnt);
            BigDecimal processTotalCount = BigDecimal.valueOf(this.workOrderCnt);

            String timeoutRateStr = timeout.divide(processTotalCount, 4, RoundingMode.HALF_UP) // 设置精度和舍入模式
                .multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP).stripTrailingZeros()
                .toPlainString();

            return new BigDecimal(timeoutRateStr); // 确保最终结果的精度
        } catch (ArithmeticException | NumberFormatException e) {
            // 捕获可能的异常并返回 null
            return null;
        }
    }

}
