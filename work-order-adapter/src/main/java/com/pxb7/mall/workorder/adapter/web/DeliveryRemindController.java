package com.pxb7.mall.workorder.adapter.web;


import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pxb7.mall.auth.c.util.ImUserUtil;
import com.pxb7.mall.response.PxPageResponse;
import com.pxb7.mall.workorder.app.model.*;
import com.pxb7.mall.workorder.app.service.RemindDeliveryProductAppService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 交付预警信息
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/web/delivery-remind/")
public class DeliveryRemindController {

    @Resource
    private RemindDeliveryProductAppService deliveryProductAppService;


    /**
     * 客服端-查询群组中交付超时信息
     * @param groupId 群组id
     * @return 群组交付超时信息
     */
    @GetMapping("timeout-info")
    public SingleResponse<TimeoutInfoRespDTO> getTimeoutInfo(@RequestParam("groupId") String groupId) {
        String userId = ImUserUtil.getUserId();
        return SingleResponse.of(deliveryProductAppService.getTimeoutInfo(groupId, userId));
    }

    /**
     * 管理后台-订单交付预警明细分页
     */
    @PostMapping("page")
    public PxPageResponse<DeliveryProductPageDetailDTO> page(@RequestBody @Validated RemindDeliveryProductReqDTO.PageDTO param) {
        Page<DeliveryProductPageDetailDTO> page = deliveryProductAppService.page(param);
        return PxPageResponse.of(page.getRecords(), (int) page.getTotal(), (int) page.getSize(), (int) page.getCurrent());
    }



    /**
     * 管理后台-订单交付预警统计列表
     */
    @PostMapping("statistic-list")
    public MultiResponse<DeliveryProductStatisticDataDTO> getStatisticList(@RequestBody @Validated DeliveryProductStatisticDataSearchDTO param) {
        List<DeliveryProductStatisticDataDTO> list = deliveryProductAppService.getStatisticList(param);
        return MultiResponse.of(list);
    }

}

