package com.pxb7.mall.workorder.adapter.schedule;

import com.pxb7.mall.workorder.app.service.RemindPlanRetryTaskAppService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import tech.powerjob.worker.core.processor.ProcessResult;
import tech.powerjob.worker.core.processor.TaskContext;
import tech.powerjob.worker.core.processor.sdk.BasicProcessor;

/**
 *  生成提醒计划异常重试任务
 * <AUTHOR>
 * @date 2025/3/28 20:05
 */

@Component
@Slf4j
public class RemindPlanRetryTask implements BasicProcessor {

    @Resource
    private RemindPlanRetryTaskAppService retryTaskAppService;

    @Override
    public ProcessResult process(TaskContext taskContext) throws Exception {
        retryTaskAppService.executeRetryTask();
        return new ProcessResult(true, "执行生成提醒计划异常重试任务成功！");
    }

}
