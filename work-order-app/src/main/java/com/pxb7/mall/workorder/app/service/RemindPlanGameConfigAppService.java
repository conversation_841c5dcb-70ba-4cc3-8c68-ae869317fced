package com.pxb7.mall.workorder.app.service;


import java.util.List;

import com.pxb7.mall.workorder.app.model.PlanGameConfigDTO;
import com.pxb7.mall.workorder.app.model.RemindPlanGameConfigReqDTO;
import com.pxb7.mall.workorder.app.model.RemindPlanGameConfigRespDTO;
import com.pxb7.mall.workorder.domain.model.RemindPlanGameConfigBO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import com.pxb7.mall.workorder.app.mapping.RemindPlanGameConfigAppMapping;
import com.pxb7.mall.workorder.domain.model.RemindPlanGameConfigReqBO;
import com.pxb7.mall.workorder.domain.model.RemindPlanGameConfigRespBO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pxb7.mall.workorder.domain.service.RemindPlanGameConfigDomainService;


/**
 * 预警计划游戏配置app服务
 *
 * <AUTHOR>
 * @since 2025-03-24 21:12:18
 */
@Service
public class RemindPlanGameConfigAppService {

    @Resource
    private RemindPlanGameConfigDomainService remindPlanGameConfigDomainService;

    public boolean insert(RemindPlanGameConfigReqDTO.AddDTO param) {
        RemindPlanGameConfigBO addBO= RemindPlanGameConfigAppMapping.INSTANCE.remindPlanGameConfigDTO2BO(param);
        return remindPlanGameConfigDomainService.insert(addBO);
    }

    public boolean update(RemindPlanGameConfigReqDTO.UpdateDTO param) {
        RemindPlanGameConfigBO updateBO = RemindPlanGameConfigAppMapping.INSTANCE.remindPlanGameConfigDTO2UpdateBO(param);
        return remindPlanGameConfigDomainService.update(updateBO);
    }

    public boolean deleteById(RemindPlanGameConfigReqDTO.DelDTO param) {
        RemindPlanGameConfigReqBO.DelBO delBO = RemindPlanGameConfigAppMapping.INSTANCE.remindPlanGameConfigDTO2DelBO(param);
        return remindPlanGameConfigDomainService.deleteById(delBO);
    }

    public RemindPlanGameConfigRespDTO.DetailDTO findById(Long id) {
        RemindPlanGameConfigRespBO.DetailBO detailBO = remindPlanGameConfigDomainService.findById(id);
        return RemindPlanGameConfigAppMapping.INSTANCE.remindPlanGameConfigBO2DetailDTO(detailBO);
    }

    public List<PlanGameConfigDTO> list(RemindPlanGameConfigReqDTO.SearchDTO param) {
        RemindPlanGameConfigReqBO.SearchBO searchBO = RemindPlanGameConfigAppMapping.INSTANCE.remindPlanGameConfigDTO2SearchBO(param);
        List<RemindPlanGameConfigBO> list = remindPlanGameConfigDomainService.list(searchBO);
        return RemindPlanGameConfigAppMapping.INSTANCE.remindPlanGameConfigBO2ListDTO(list);
    }

    public Page<RemindPlanGameConfigRespDTO.DetailDTO> page(RemindPlanGameConfigReqDTO.PageDTO param) {
        RemindPlanGameConfigReqBO.PageBO pageBO = RemindPlanGameConfigAppMapping.INSTANCE.remindPlanGameConfigDTO2PageBO(param);
        Page<RemindPlanGameConfigRespBO.DetailBO> page = remindPlanGameConfigDomainService.page(pageBO);
        return RemindPlanGameConfigAppMapping.INSTANCE.remindPlanGameConfigBO2PageDTO(page);
    }

}

