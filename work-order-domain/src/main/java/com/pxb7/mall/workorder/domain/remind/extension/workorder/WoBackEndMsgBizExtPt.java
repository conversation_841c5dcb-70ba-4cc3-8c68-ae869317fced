package com.pxb7.mall.workorder.domain.remind.extension.workorder;

import com.alibaba.cola.extension.Extensions;
import com.alibaba.fastjson2.JSON;
import com.pxb7.mall.common.client.enums.BusinessTypeEnum;
import com.pxb7.mall.common.client.enums.SystemUserInternalMsgTypeEnum;
import com.pxb7.mall.common.client.request.message.SysUserInternalMsgReqDTO;
import com.pxb7.mall.common.client.request.message.SysUserToastMsgReqDTO;
import com.pxb7.mall.workorder.client.constant.MessageTemplateConstant;
import com.pxb7.mall.workorder.client.enums.BizTypeEnum;
import com.pxb7.mall.workorder.client.enums.RemindMethodEnum;
import com.pxb7.mall.workorder.client.enums.RemindObjectEnum;
import com.pxb7.mall.workorder.client.message.RemindPlanMessage;
import com.pxb7.mall.workorder.domain.model.RemindMethodConfigBO;
import com.pxb7.mall.workorder.domain.model.RemindPlanRespBO;
import com.pxb7.mall.workorder.domain.model.RemindWorkOrderBO;
import com.pxb7.mall.workorder.domain.remind.extension.BaseMessageService;
import com.pxb7.mall.workorder.domain.remind.extension.RemindBizExtPt;
import com.pxb7.mall.workorder.domain.remind.extension.context.RemindBizContext;
import com.pxb7.mall.workorder.infra.repository.gateway.dubbo.common.MessageGateway;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

import static com.pxb7.mall.workorder.infra.constant.CommonConstants.SYS_DEFAULT_USER_0;

/**
 * 商品工单管理后台消息提醒
 * <AUTHOR>
 * @date 2025/4/2 14:56
 */

@Slf4j
@Extensions(
        bizId = {BizTypeEnum.Constants.WORK_ORDER},
        useCase = {RemindMethodEnum.Constants.BACKEND},
        scenario = {RemindObjectEnum.Constants.ART_DESIGNER,
                RemindObjectEnum.Constants.AUDIT_CUSTOMER_CARE,
                RemindObjectEnum.Constants.FOLLOWER,
                RemindObjectEnum.Constants.CUSTOM})
public class WoBackEndMsgBizExtPt extends BaseMessageService implements RemindBizExtPt {


    @Resource
    private MessageGateway messageGateway;

    @Override
    public void executeRemindTask(RemindBizContext context) {
        RemindWorkOrderBO remindWorkOrder = (RemindWorkOrderBO) context.getRemindRecord();
        RemindPlanMessage remindPlanMessage = context.getRemindPlanMessage();
        //获取提醒对象
        RemindMethodConfigBO.RemindObjectBO remindObjectBO = context.getRemindObjectBO();
        RemindObjectEnum remindObject = RemindObjectEnum.getByCode(remindObjectBO.getObject());
        String userIdStr = "";
        switch (Objects.requireNonNull(remindObject)) {
            case ART_DESIGNER -> userIdStr = remindWorkOrder.getArtDesignerId();
            case AUDIT_CUSTOMER_CARE -> userIdStr = remindWorkOrder.getAuditUserId();
            case FOLLOWER -> userIdStr = remindWorkOrder.getFollowerId();
            case CUSTOM -> userIdStr = getUserIdsByUserNames(remindObjectBO.getCustomUserIds());
            default -> log.error("Fail to get remind object by code. code:{}", remindObjectBO.getObject());
        }
        if (StringUtils.isBlank(userIdStr)) {
            log.warn("Fail to get remind user id. planRecordId:{}", remindPlanMessage.getPlanRecordId());
            return;
        }
        //发送站内信
        setInternalMessage(context, remindPlanMessage.getPlanRecordId(), userIdStr);
        //发送管理后台弹窗消息
        sendToastMessage(context, remindPlanMessage.getPlanRecordId(), userIdStr);
    }

    /**
     * 发送站内信
     * @param context
     * @param businessId
     * @param userIdStr
     */
    private void setInternalMessage(RemindBizContext context, String businessId, String userIdStr) {
        //获取商品工单消息内容
        String content = getWorkOrderMessageContent(context, RemindMethodEnum.BACKEND_MANAGE);
        if (StringUtils.isBlank(content)) {
            log.error("Fail to get work order message content. planRecordId:{}", businessId);
            return;
        }
        RemindWorkOrderBO remindWorkOrder = (RemindWorkOrderBO) context.getRemindRecord();
        //获取消息标题
        RemindPlanRespBO.DetailBO remindPlan = remindPlanDomainService.findById(remindWorkOrder.getRemindPlanId());
        String title = String.format(MessageTemplateConstant.WORK_ORDER_REMIND_TITLE_BACK_END, remindPlan.getPlanName());

        //向后台系统用户发送站内信
        SysUserInternalMsgReqDTO msgReqDTO = new SysUserInternalMsgReqDTO();
        msgReqDTO.setTitle(title);
        msgReqDTO.setContent(content);
        msgReqDTO.setMsgType(SystemUserInternalMsgTypeEnum.SERVICE_TIME_WARNING.getCode());
        msgReqDTO.setBusinessType(BusinessTypeEnum.SERVICE_TIME_WARNING.getCode());
        msgReqDTO.setJumpUrl("/workOrderManage/manageList?workOrderId="
                + remindWorkOrder.getWorkOrderId() + "&productCode=" + remindWorkOrder.getProductCode());
        for (String userId : userIdStr.split(",")) {
            if (StringUtils.isBlank(userId)
                    || Objects.equals(userId, SYS_DEFAULT_USER_0)) {
                continue;
            }
            msgReqDTO.setSystemUserId(userId);
            msgReqDTO.setBusinessId(businessId + userId);
            String messageId = messageGateway.sendInternalMessage(msgReqDTO);
            log.info("Send message to the systemUserMessageService#sendInternalMessage,msgReqDTO:{},messageId:{}",
                    JSON.toJSONString(msgReqDTO), messageId);
        }
    }

    /**
     * 发送管理后台弹窗消息
     * @param context
     * @param businessId
     * @param userIdStr
     */
    private void sendToastMessage(RemindBizContext context, String businessId, String userIdStr) {
        //获取商品工单后台弹窗消息
        String toastContent = getToastMessageContent(context);
        if (StringUtils.isBlank(toastContent)) {
            log.error("Fail to get work order toast message content. planRecordId:{}", businessId);
            return;
        }
        //向后台系统用户发送弹窗消息
        SysUserToastMsgReqDTO toastMsgReqDTO = new SysUserToastMsgReqDTO();
        toastMsgReqDTO.setTitle(MessageTemplateConstant.WORK_ORDER_REMIND_TITLE);
        toastMsgReqDTO.setContent(toastContent);
        toastMsgReqDTO.setBusinessType(BusinessTypeEnum.SERVICE_TIME_WARNING.getCode());
        for (String userId : userIdStr.split(",")) {
            if (StringUtils.isBlank(userId)
                    || Objects.equals(userId, SYS_DEFAULT_USER_0)) {
                continue;
            }
            toastMsgReqDTO.setSystemUserId(userId);
            toastMsgReqDTO.setBusinessId(businessId + userId);
            String messageId = messageGateway.sendToastMessage(toastMsgReqDTO);
            log.info("Send message to the systemUserMessageService#sendToastMessage,toastMsgReqDTO:{},messageId:{}",
                    JSON.toJSONString(toastMsgReqDTO), messageId);
        }
    }

}
