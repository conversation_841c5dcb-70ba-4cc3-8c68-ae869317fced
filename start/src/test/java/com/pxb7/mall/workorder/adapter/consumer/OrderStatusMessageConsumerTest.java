package com.pxb7.mall.workorder.adapter.consumer;

import com.pxb7.mall.trade.order.client.message.OrderItemStatusChangeMQDTO;
import com.pxb7.mall.workorder.app.service.RemindDeliveryProductAppService;
import com.pxb7.mall.workorder.infra.enums.OrderItemStatusEnum;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.apache.rocketmq.client.java.message.MessageIdImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

class OrderStatusMessageConsumerTest {

    @InjectMocks
    private OrderStatusMessageConsumer consumer;

    @Mock
    private RemindDeliveryProductAppService remindDeliveryProductAppService;

    @Mock
    private MessageView messageView;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        when(messageView.getMessageId()).thenReturn(new MessageIdImpl("version", "mocked-message-id"));
    }

    @Test
    void testConsume_whenOrderIdOrOrderItemIdBlank_thenSuccess() {
        // Arrange
        OrderItemStatusChangeMQDTO dto = new OrderItemStatusChangeMQDTO();
        dto.setOrderId(null);
        dto.setOrderItemId("1");

        // Act
        ConsumeResult result = consumer.consume(messageView, dto);

        // Assert
        assertEquals(ConsumeResult.SUCCESS, result);
        verifyNoInteractions(remindDeliveryProductAppService);
    }

    @Test
    void testConsume_whenStatusIsInitOrWaitPayOrPerSettlement_thenSuccess() {
        OrderItemStatusChangeMQDTO dto = new OrderItemStatusChangeMQDTO();
        dto.setOrderId("1");
        dto.setOrderItemId("1");

        for (OrderItemStatusEnum status : new OrderItemStatusEnum[]{
                OrderItemStatusEnum.INIT,
                OrderItemStatusEnum.WAIT_PAY,
                OrderItemStatusEnum.PER_SETTLEMENT}) {
            dto.setOrderItemStatus(status.getValue());
            ConsumeResult result = consumer.consume(messageView, dto);
            assertEquals(ConsumeResult.SUCCESS, result);
        }
        verifyNoInteractions(remindDeliveryProductAppService);
    }

    @Test
    void testConsume_whenStatusIsDealing_thenGenerateRemindPlanCalled() {
        // Arrange
        OrderItemStatusChangeMQDTO dto = mock(OrderItemStatusChangeMQDTO.class);
        when(dto.getOrderId()).thenReturn("1");
        when(dto.getOrderItemId()).thenReturn("1");
        when(dto.getOrderItemStatus()).thenReturn(OrderItemStatusEnum.DEALING.getValue());

        // Act
        ConsumeResult result = consumer.consume(messageView, dto);

        // Assert
        assertEquals(ConsumeResult.SUCCESS, result);
        verify(remindDeliveryProductAppService).generateRemindPlan(dto);
        verifyNoMoreInteractions(remindDeliveryProductAppService);
    }

    @Test
    void testConsume_whenStatusIsFinish_thenChangeRemindPlanStatusCalled() {
        // Arrange
        OrderItemStatusChangeMQDTO dto = mock(OrderItemStatusChangeMQDTO.class);
        when(dto.getOrderId()).thenReturn("1");
        when(dto.getOrderItemId()).thenReturn("1");
        when(dto.getOrderItemStatus()).thenReturn(OrderItemStatusEnum.DEAL_SUCCESS.getValue());

        // Act
        ConsumeResult result = consumer.consume(messageView, dto);

        // Assert
        assertEquals(ConsumeResult.SUCCESS, result);
        verify(remindDeliveryProductAppService).changeRemindPlanStatus(dto);
        verifyNoMoreInteractions(remindDeliveryProductAppService);
    }

    @Test
    void testConsume_whenExceptionOccurs_thenFailureReturned() {
        // Arrange
        OrderItemStatusChangeMQDTO dto = mock(OrderItemStatusChangeMQDTO.class);
        when(dto.getOrderId()).thenReturn("1");
        when(dto.getOrderItemId()).thenReturn("1");
        when(dto.getOrderItemStatus()).thenReturn(OrderItemStatusEnum.DEALING.getValue());
        doThrow(new RuntimeException("Simulated error")).when(remindDeliveryProductAppService).generateRemindPlan(dto);

        // Act
        ConsumeResult result = consumer.consume(messageView, dto);

        // Assert
        assertEquals(ConsumeResult.FAILURE, result);
    }
}
