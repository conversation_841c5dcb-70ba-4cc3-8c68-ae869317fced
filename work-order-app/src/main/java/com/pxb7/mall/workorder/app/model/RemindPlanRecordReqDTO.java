package com.pxb7.mall.workorder.app.model;

import java.util.Date;
import java.time.*;
import java.math.BigDecimal;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Min;
import lombok.ToString;


/**
 * 预警执行计划记录(RemindPlanRecord)实体类
 *
 * <AUTHOR>
 * @since 2025-04-12 14:14:29
 */
public class RemindPlanRecordReqDTO {

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class AddDTO {


        @NotBlank(message = "planRecordId不能为空")
        private String planRecordId;


        @NotNull(message = "bizType不能为空")
        private Integer bizType;


        @NotBlank(message = "bizId不能为空")
        private String bizId;


        @NotBlank(message = "remindId不能为空")
        private String remindId;


        @NotNull(message = "whichTime不能为空")
        private Integer whichTime;


        @NotNull(message = "lastBeforeTimeout不能为空")
        private Boolean lastBeforeTimeout;


        @NotNull(message = "status不能为空")
        private Integer status;


        @NotNull(message = "remindTime不能为空")
        private LocalDateTime remindTime;


        @NotNull(message = "planRuleId不能为空")
        private Long planRuleId;


        @NotNull(message = "remindPlanId不能为空")
        private Long remindPlanId;


        @NotNull(message = "remindSubPlanId不能为空")
        private Long remindSubPlanId;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class UpdateDTO {


        @NotNull(message = "id不能为空")
        private Long id;


        @NotBlank(message = "planRecordId不能为空")
        private String planRecordId;


        @NotNull(message = "bizType不能为空")
        private Integer bizType;


        @NotBlank(message = "bizId不能为空")
        private String bizId;


        @NotBlank(message = "remindId不能为空")
        private String remindId;


        @NotNull(message = "whichTime不能为空")
        private Integer whichTime;


        @NotNull(message = "lastBeforeTimeout不能为空")
        private Boolean lastBeforeTimeout;


        @NotNull(message = "status不能为空")
        private Integer status;


        @NotNull(message = "remindTime不能为空")
        private LocalDateTime remindTime;


        @NotNull(message = "planRuleId不能为空")
        private Long planRuleId;


        @NotNull(message = "remindPlanId不能为空")
        private Long remindPlanId;


        @NotNull(message = "remindSubPlanId不能为空")
        private Long remindSubPlanId;


    }


    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class DelDTO {
        @NotNull(message = "id不能为空")
        private Long id;
    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class SearchDTO {

        @NotBlank(message = "planRecordId不能为空")
        private String planRecordId;


        @NotNull(message = "bizType不能为空")
        private Integer bizType;


        @NotBlank(message = "bizId不能为空")
        private String bizId;


        @NotBlank(message = "remindId不能为空")
        private String remindId;


        @NotNull(message = "whichTime不能为空")
        private Integer whichTime;


        @NotNull(message = "lastBeforeTimeout不能为空")
        private Boolean lastBeforeTimeout;


        @NotNull(message = "status不能为空")
        private Integer status;


        @NotNull(message = "remindTime不能为空")
        private LocalDateTime remindTime;


        @NotNull(message = "planRuleId不能为空")
        private Long planRuleId;


        @NotNull(message = "remindPlanId不能为空")
        private Long remindPlanId;


        @NotNull(message = "remindSubPlanId不能为空")
        private Long remindSubPlanId;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class PageDTO {


        @NotBlank(message = "planRecordId不能为空")
        private String planRecordId;


        @NotNull(message = "bizType不能为空")
        private Integer bizType;


        @NotBlank(message = "bizId不能为空")
        private String bizId;


        @NotBlank(message = "remindId不能为空")
        private String remindId;


        @NotNull(message = "whichTime不能为空")
        private Integer whichTime;


        @NotNull(message = "lastBeforeTimeout不能为空")
        private Boolean lastBeforeTimeout;


        @NotNull(message = "status不能为空")
        private Integer status;


        @NotNull(message = "remindTime不能为空")
        private LocalDateTime remindTime;


        @NotNull(message = "planRuleId不能为空")
        private Long planRuleId;


        @NotNull(message = "remindPlanId不能为空")
        private Long remindPlanId;


        @NotNull(message = "remindSubPlanId不能为空")
        private Long remindSubPlanId;


        /**
         * 页码，从1开始
         */
        @NotNull(message = "pageIndex不能为空")
        @Min(value = 1, message = "pageIndex不能小于1")
        private Integer pageIndex;
        /**
         * 每页数量
         */
        @NotNull(message = "pageSize不能为空")
        private Integer pageSize;

    }

}

