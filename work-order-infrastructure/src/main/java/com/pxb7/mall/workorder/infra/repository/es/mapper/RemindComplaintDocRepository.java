package com.pxb7.mall.workorder.infra.repository.es.mapper;

import com.pxb7.mall.workorder.infra.repository.es.entity.RemindComplaintDoc;
import org.springframework.data.elasticsearch.repository.ElasticsearchRepository;

import java.io.Serializable;

public interface RemindComplaintDocRepository extends ElasticsearchRepository<RemindComplaintDoc, Serializable> {

    //SearchPage<RemindWorkOrderDoc> pageQueryWorkOrder(RemindWorkOrderReqPO.PagePO workOrderPagePO);
}
