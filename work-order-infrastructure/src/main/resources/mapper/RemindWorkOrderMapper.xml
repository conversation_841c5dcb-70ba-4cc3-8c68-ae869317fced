<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pxb7.mall.workorder.infra.repository.db.mapper.RemindWorkOrderMapper">
    <resultMap id="BaseResultMap" type="com.pxb7.mall.workorder.infra.repository.db.entity.RemindWorkOrder">
        <result column="id" jdbcType="BIGINT" property="id"/>
        <result column="remind_id" jdbcType="VARCHAR" property="remindId"/>
        <result column="work_order_id" jdbcType="VARCHAR" property="workOrderId"/>
        <result column="work_order_status" jdbcType="INTEGER" property="workOrderStatus"/>
        <result column="product_id" jdbcType="VARCHAR" property="productId"/>
        <result column="complete_status" jdbcType="INTEGER" property="completeStatus"/>
        <result column="time_out_status" jdbcType="INTEGER" property="timeOutStatus"/>
        <result column="art_designer_id" jdbcType="VARCHAR" property="artDesignerId"/>
        <result column="follower_id" jdbcType="VARCHAR" property="followerId"/>
        <result column="audit_user_id" jdbcType="VARCHAR" property="auditUserId"/>
        <result column="expect_complete_time" jdbcType="TIMESTAMP" property="expectCompleteTime"/>
        <result column="game_config_id" jdbcType="BIGINT" property="gameConfigId"/>
        <result column="remind_plan_id" jdbcType="BIGINT" property="remindPlanId"/>
        <result column="remind_sub_plan_id" jdbcType="BIGINT" property="remindSubPlanId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_user_id" jdbcType="VARCHAR" property="createUserId"/>
        <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId"/>
        <result column="is_deleted" jdbcType="BOOLEAN" property="deleted"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into
        remind_work_order(remind_id,work_order_id,work_order_status,product_id,complete_status,time_out_status,art_designer_id,follower_id,audit_user_id,expect_complete_time,game_config_id,remind_plan_id,remind_sub_plan_id,create_user_id,update_user_id)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.remindId},#{entity.workOrderId},#{entity.workOrderStatus},#{entity.productId},#{entity.completeStatus},#{entity.timeOutStatus},#{entity.artDesignerId},#{entity.followerId},#{entity.auditUserId},#{entity.expectCompleteTime},#{entity.gameConfigId},#{entity.remindPlanId},#{entity.remindSubPlanId},#{entity.createUserId},#{entity.updateUserId})
        </foreach>
    </insert>

    <update id="batchUpdateTimeoutStatusByIds">
        update remind_work_order
        set time_out_status = #{status}
        where id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>


    <select id="checkFollowUpStatusByWorkOrderId" resultType="int">
        select count(*)
        from remind_work_order
        where work_order_id = #{workOrderId} and work_order_status = 3 and complete_status = 2 limit 1
    </select>

</mapper>

