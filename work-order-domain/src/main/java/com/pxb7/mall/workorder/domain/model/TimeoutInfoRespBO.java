package com.pxb7.mall.workorder.domain.model;

import lombok.Data;

/**
 *
 * 账号交付超时预警信息
 *
 * <AUTHOR>
 * @date 2025/3/26 15:58
 */

@Data
public class TimeoutInfoRespBO {

    /**
     * 超时类型，0:无，1:即将超时，2:已超时
     * @see com.pxb7.mall.workorder.client.enums.TimeoutStatusEnum
     */
    private Integer timeoutType;

    /**
     * 超时时间，单位：秒
     */
    private Long timeoutTime;

    /**
     * 服务类型，1:账号交付，2:商品工单，3:售后工单，4:客诉工单
     * @see com.pxb7.mall.workorder.client.enums.BizTypeEnum
     */
    private Integer bizType;

}
