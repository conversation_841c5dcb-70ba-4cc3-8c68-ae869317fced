package com.pxb7.mall.workorder.infra.repository.db;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pxb7.mall.components.env.profile.PxProfileHelper;
import com.pxb7.mall.workorder.infra.mapping.RetryTaskMapping;
import com.pxb7.mall.workorder.infra.model.RemindPlanRetryTaskReqPO;
import com.pxb7.mall.workorder.infra.repository.db.entity.RemindPlanRetryTask;
import com.pxb7.mall.workorder.infra.repository.db.mapper.RemindPlanRetryTaskMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 生成提醒计划异常重试表(RemindPlanRetryTask)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-06 20:53:53
 */
@Slf4j
@Repository
public class RemindPlanRetryTaskDbRepository extends ServiceImpl<RemindPlanRetryTaskMapper, RemindPlanRetryTask> implements RemindPlanRetryTaskRepository {

    @Override
    public boolean insert(RemindPlanRetryTaskReqPO.AddPO param) {
        RemindPlanRetryTask entity = RetryTaskMapping.INSTANCE.addDto2PO(param);
        return this.save(entity);
    }

    @Override
    public boolean batchUpdate(List<RemindPlanRetryTaskReqPO.UpdatePO> param) {
        List<RemindPlanRetryTask> list = RetryTaskMapping.INSTANCE.updateDto2POList(param);
        return this.updateBatchById(list);
    }

    @Override
    public Page<RemindPlanRetryTask> page(RemindPlanRetryTaskReqPO.PagePO param) {
        LambdaQueryWrapper<RemindPlanRetryTask> queryWrapper = new LambdaQueryWrapper<>();
        // 返回分页查询结果
        queryWrapper.eq(RemindPlanRetryTask::getEnvProfile, PxProfileHelper.getProfile().getProfileValue());
        queryWrapper.gt(RemindPlanRetryTask::getId, param.getId());
        queryWrapper.eq(RemindPlanRetryTask::getStatus, param.getStatus());
        queryWrapper.orderByAsc(RemindPlanRetryTask::getId);
        return this.page(new Page<>(param.getPageIndex(), param.getPageSize()), queryWrapper);
    }

}
