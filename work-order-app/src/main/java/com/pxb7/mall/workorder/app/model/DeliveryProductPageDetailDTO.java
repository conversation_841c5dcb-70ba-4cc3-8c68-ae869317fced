package com.pxb7.mall.workorder.app.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Getter
@Setter
@Accessors(chain = true)
@ToString
public class DeliveryProductPageDetailDTO {


    /**
     * 预警Id
     */
    private String remindId;

    /**
     * 游戏Id
     */
    private String gameId;

    /**
     * 游戏名称
     */
    private String gameName;





    /**
     * 商品id
     */
    private String productId;

    /**
     * 商品编码
     */
    private String productCode;

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 订单明细号
     */
    private String orderItemId;

    /**
     * 群组ID （房间ID）
     */
    private String groupId;

    /**
     * 交付客服
     */
    private String deliveryCustomerCare;

    /**
     * 交付客服名称
     */
    private String deliveryCustomerCareName;

    /**
     * 交付时长？？？
     *  todo
     *
     *  要从支付成功时间到 财务放款时间 ？？
     *
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime payTime;

    /**
     * 预期完结时间：通过当前时间减去expectCompleteTime可以算出
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expectCompleteTime;

    /**
     *  完结时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime completeTime;

    /**
     * 交付时长提示："xx天xx小时xx分"
     */
    private String durationTip;

    /**
     * 倒计时提示："xx天xx小时xx分钟后超时" OR "已超时xx天xx小时xx分钟"
     */
    private String countDownTip;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
}
