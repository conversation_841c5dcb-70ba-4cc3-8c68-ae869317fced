package com.pxb7.mall.workorder.app;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pxb7.mall.trade.order.client.message.OrderItemStatusChangeMQDTO;
import com.pxb7.mall.workorder.app.service.RemindDeliveryProductAppService;
import com.pxb7.mall.workorder.app.service.RemindPlanRetryTaskAppService;
import com.pxb7.mall.workorder.client.enums.BizTypeEnum;
import com.pxb7.mall.workorder.domain.model.RemindPlanRetryTaskReqBO;
import com.pxb7.mall.workorder.domain.model.RemindPlanRetryTaskRespBO;
import com.pxb7.mall.workorder.domain.service.RemindPlanRetryTaskDomainService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.*;

@SpringBootTest
public class RemindPlanRetryTaskAppServiceTest {

    @InjectMocks
    private RemindPlanRetryTaskAppService appService;

    @Mock
    private RemindPlanRetryTaskDomainService retryTaskDomainService;

    @Mock
    private RemindDeliveryProductAppService deliveryProductAppService;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    // ------------------ saveRetryTask ------------------

    @Test
    public void testSaveRetryTask_ShouldCallInsert() {
        OrderItemStatusChangeMQDTO dto = new OrderItemStatusChangeMQDTO();
        dto.setOrderItemId("123");

        appService.saveRetryTask(dto);

        verify(retryTaskDomainService, times(1)).insert(any(RemindPlanRetryTaskReqBO.AddBO.class));
    }

    // ------------------ executeRetryTask ------------------

    @Test
    public void testExecuteRetryTask_WithNoData_ShouldExitEarly() {
        when(retryTaskDomainService.page(any(RemindPlanRetryTaskReqBO.PageBO.class)))
                .thenReturn(new Page<>());

        appService.executeRetryTask();

        verify(retryTaskDomainService, times(1)).page(any());
    }

    @Test
    public void testExecuteRetryTask_WithData_ShouldHandlePage() {
        List<RemindPlanRetryTaskRespBO.DetailBO> records = new ArrayList<>();
        records.add(mock(RemindPlanRetryTaskRespBO.DetailBO.class));

        Page<RemindPlanRetryTaskRespBO.DetailBO> page = new Page<>();
        page.setRecords(records);
        page.setCurrent(1L);
        page.setSize(10L);

        when(retryTaskDomainService.page(any()))
                .thenReturn(page)
                .thenReturn(new Page<>()); // 第二次返回空

        appService.executeRetryTask();

        verify(appService, times(1)).handleRetryTask(records);
    }

    // ------------------ handleRetryTask ------------------

    @Test
    public void testHandleRetryTask_ShouldOnlyProcessDeliveryProductType() {
        List<RemindPlanRetryTaskRespBO.DetailBO> records = new ArrayList<>();

        RemindPlanRetryTaskRespBO.DetailBO detailBO1 = mock(RemindPlanRetryTaskRespBO.DetailBO.class);
        when(detailBO1.getServiceType()).thenReturn(BizTypeEnum.DELIVERY_PRODUCT.getType());

        RemindPlanRetryTaskRespBO.DetailBO detailBO2 = mock(RemindPlanRetryTaskRespBO.DetailBO.class);
        when(detailBO2.getServiceType()).thenReturn(0);

        records.add(detailBO1);
        records.add(detailBO2);

        doNothing().when(appService).retryGenerateRemindPlan(detailBO1);

        appService.handleRetryTask(records);

        verify(appService, times(1)).retryGenerateRemindPlan(detailBO1);
        verify(retryTaskDomainService, times(1)).batchUpdate(anyList());
    }

    // ------------------ retryGenerateRemindPlan ------------------

    @Test
    public void testRetryGenerateRemindPlan_Success_ShouldSetStatusTo1() {
        RemindPlanRetryTaskRespBO.DetailBO detailBO = mock(RemindPlanRetryTaskRespBO.DetailBO.class);
        when(detailBO.getId()).thenReturn(1L);
        when(detailBO.getRetryTimes()).thenReturn(0);
        when(detailBO.getBizId()).thenReturn("123");
        when(detailBO.getCreateTime()).thenReturn(LocalDateTime.now().minusHours(1));

        doNothing().when(deliveryProductAppService).retryGenerateRemindPlan("123");

        RemindPlanRetryTaskReqBO.UpdateBO updateBO = appService.retryGenerateRemindPlan(detailBO);

        assertNotNull(updateBO);
        assertEquals(1, updateBO.getStatus());
        assertEquals(1, updateBO.getRetryTimes());
    }

    @Test
    public void testRetryGenerateRemindPlan_ExceptionAndTimeout_ShouldSetStatusTo2() {
        LocalDateTime createTime = LocalDateTime.now().minusHours(25);
        RemindPlanRetryTaskRespBO.DetailBO detailBO = mock(RemindPlanRetryTaskRespBO.DetailBO.class);
        when(detailBO.getId()).thenReturn(1L);
        when(detailBO.getRetryTimes()).thenReturn(0);
        when(detailBO.getBizId()).thenReturn("123");
        when(detailBO.getCreateTime()).thenReturn(createTime);

        doThrow(new RuntimeException("Simulated failure"))
                .when(deliveryProductAppService).retryGenerateRemindPlan("123");

        RemindPlanRetryTaskReqBO.UpdateBO updateBO = appService.retryGenerateRemindPlan(detailBO);

        assertNotNull(updateBO);
        assertEquals(2, updateBO.getStatus());
    }

    @Test
    public void testRetryGenerateRemindPlan_ExceptionButNotTimeout_ShouldSetStatusTo0() {
        LocalDateTime createTime = LocalDateTime.now().minusHours(23);
        RemindPlanRetryTaskRespBO.DetailBO detailBO = mock(RemindPlanRetryTaskRespBO.DetailBO.class);
        when(detailBO.getId()).thenReturn(1L);
        when(detailBO.getRetryTimes()).thenReturn(0);
        when(detailBO.getBizId()).thenReturn("123");
        when(detailBO.getCreateTime()).thenReturn(createTime);

        doThrow(new RuntimeException("Simulated failure"))
                .when(deliveryProductAppService).retryGenerateRemindPlan("123");

        RemindPlanRetryTaskReqBO.UpdateBO updateBO = appService.retryGenerateRemindPlan(detailBO);

        assertNotNull(updateBO);
        assertEquals(0, updateBO.getStatus());
    }
}
