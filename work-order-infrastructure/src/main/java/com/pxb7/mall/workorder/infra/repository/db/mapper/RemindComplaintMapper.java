package com.pxb7.mall.workorder.infra.repository.db.mapper;

import com.pxb7.mall.workorder.infra.repository.db.entity.RemindComplaint;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 客诉工单预警记录(RemindComplaint)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-04-24 23:31:56
 */
@Mapper
public interface RemindComplaintMapper extends BaseMapper<RemindComplaint> {
    /**
     * 批量新增数据
     *
     * @param entities List<RemindComplaint> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<RemindComplaint> entities);

    /**
     * 批量修改超时状态
     * @param ids
     * @param status
     */
    int batchUpdateTimeoutStatusByIds(List<Long> ids, Integer status);

}

