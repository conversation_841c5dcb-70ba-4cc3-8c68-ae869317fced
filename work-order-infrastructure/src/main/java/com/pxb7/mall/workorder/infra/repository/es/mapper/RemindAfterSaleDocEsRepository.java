package com.pxb7.mall.workorder.infra.repository.es.mapper;

import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.MatchQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.Query;
import co.elastic.clients.elasticsearch._types.query_dsl.QueryBuilders;
import co.elastic.clients.elasticsearch._types.query_dsl.TermQuery;
import com.pxb7.mall.workorder.client.enums.CompleteStatusEnum;
import com.pxb7.mall.workorder.client.enums.TimeoutStatusEnum;
import com.pxb7.mall.workorder.infra.enums.RemindPlanWorkOrderTypeEnum;
import com.pxb7.mall.workorder.infra.model.RemindAfterSaleReqPO;
import com.pxb7.mall.workorder.infra.repository.es.entity.RemindAfterSaleDoc;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.elasticsearch.client.elc.ElasticsearchTemplate;
import org.springframework.data.elasticsearch.client.elc.NativeQuery;
import org.springframework.data.elasticsearch.client.elc.NativeQueryBuilder;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHitSupport;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.SearchPage;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Slf4j
@Repository
public class RemindAfterSaleDocEsRepository {

    @Resource
    private ElasticsearchTemplate elasticsearchTemplate;

    public SearchPage<RemindAfterSaleDoc> pageQueryAfterSale(RemindAfterSaleReqPO.PagePO pagePO) {

        // 构造查询条件
        List<Query> mustQueryList = new ArrayList<>();

        if (ObjectUtils.isNotEmpty(pagePO.getWorkOrderType())) {
            TermQuery workOrderTypeQuery =
                QueryBuilders.term().field("work_order_type").value(pagePO.getWorkOrderType()).build();
            mustQueryList.add(workOrderTypeQuery._toQuery());
        }
        if (ObjectUtils.isNotEmpty(pagePO.getProductCode())) {
            TermQuery productCodeQuery =
                QueryBuilders.term().field("product_code").value(pagePO.getProductCode()).build();
            mustQueryList.add(productCodeQuery._toQuery());
        }
        if (ObjectUtils.isNotEmpty(pagePO.getOrderItemId())) {
            TermQuery OrderItemIdQuery =
                QueryBuilders.term().field("order_item_id").value(pagePO.getOrderItemId()).build();
            mustQueryList.add(OrderItemIdQuery._toQuery());
        }
        if (ObjectUtils.isNotEmpty(pagePO.getWorkOrderId())) {
            MatchQuery workOrderIdQuery = QueryBuilders.match().field("work_order_no").query(pagePO.getWorkOrderId()).build();
            mustQueryList.add(workOrderIdQuery._toQuery());
        }
        // 构造排序条件
        Sort sort = Sort.by(Sort.Order.desc("create_time"));

        // 构建最终查询结构
        var nativeQuery = NativeQuery.builder().withQuery(QueryBuilders.bool().must(mustQueryList).build()._toQuery())
            .withPageable(PageRequest.of((int)pagePO.getPageIndex() - 1, (int)pagePO.getPageSize())).withSort(sort)
            .withTrackTotalHits(true).build();

        log.info("pageQueryAfterSale DSL语句：{}", nativeQuery.getQuery().toString());

        SearchHits<RemindAfterSaleDoc> searchHits = elasticsearchTemplate.search(nativeQuery, RemindAfterSaleDoc.class);
        return SearchHitSupport.searchPageFor(searchHits, nativeQuery.getPageable());
    }

    /**
     * 查询超时工单数量
     *
     * @param timoutStatus
     * @param workOrderType
     * @return
     */
    public Long countWorkOrder(TimeoutStatusEnum timoutStatus, Integer workOrderType) {
        NativeQueryBuilder nativeQueryBuilder = new NativeQueryBuilder();
        BoolQuery.Builder boolFilter = QueryBuilders.bool();
        boolFilter.filter(
            QueryBuilders.term(a -> a.field("complete_status").value(CompleteStatusEnum.IN_PROCESS.getCode())));
        boolFilter.filter(QueryBuilders.term(a -> a.field("time_out_status").value(timoutStatus.getCode())));
        boolFilter.filter(QueryBuilders.term(a -> a.field("work_order_type").value(workOrderType)));

        nativeQueryBuilder.withQuery(boolFilter.build()._toQuery());

        SearchHits<RemindAfterSaleDoc> searchHits =
            elasticsearchTemplate.search(nativeQueryBuilder.build(), RemindAfterSaleDoc.class);
        return searchHits.getTotalHits();
    }

    /**
     * 通过房间查询售后工单预警记录
     * @param groupId
     * @param customerCareId
     * @return
     */
    public RemindAfterSaleDoc searchByGroupId(String groupId, String customerCareId) {
        NativeQueryBuilder nativeQueryBuilder = new NativeQueryBuilder();
        BoolQuery.Builder boolFilter = QueryBuilders.bool();
        boolFilter.filter(QueryBuilders.term(a -> a.field("group_id").value(groupId)));
        boolFilter.filter(QueryBuilders.term(a -> a.field("work_order_type")
                .value(RemindPlanWorkOrderTypeEnum.DISPUTE.getValue())));
        if (StringUtils.isNotBlank(customerCareId)) {
            boolFilter.filter(QueryBuilders.term(a -> a.field("dispute_user_id").value(customerCareId)));
        }
        nativeQueryBuilder.withQuery(boolFilter.build()._toQuery());

        SearchHit<RemindAfterSaleDoc> searchHit = elasticsearchTemplate.searchOne(
                nativeQueryBuilder.build(), RemindAfterSaleDoc.class);
        return Objects.isNull(searchHit) ? null : searchHit.getContent();
    }

}
