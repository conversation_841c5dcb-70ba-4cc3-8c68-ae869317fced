package com.pxb7.mall.workorder.infra.repository.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 账号交付预警记录(RemindDeliveryProduct)实体类
 *
 * <AUTHOR>
 * @since 2025-03-27 20:07:05
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "remind_delivery_product")
@ToString
public class RemindDeliveryProduct implements Serializable {
    private static final long serialVersionUID = -31016803846050585L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 预警记录id
     */
    @TableField(value = "remind_id")
    private String remindId;
    /**
     * 商品id
     */
    @TableField(value = "product_id")
    private String productId;
    /**
     * 商品编码
     */
    @TableField(value = "product_code")
    private String productCode;
    /**
     * 游戏id
     */
    @TableField(value = "game_id")
    private String gameId;
    /**
     * 群组id
     */
    @TableField(value = "group_id")
    private String groupId;
    /**
     * 订单id
     */
    @TableField(value = "order_id")
    private String orderId;
    /**
     * 子订单id
     */
    @TableField(value = "order_item_id")
    private String orderItemId;
    /**
     * 完结状态,1:未完结,2:已完结,3:终止
     */
    @TableField(value = "complete_status")
    private Integer completeStatus;
    /**
     * 交付状态 0: 交付中 1:交付完成 2:交付暂停 4:交付终止 5:交付完结(人工)
     */
    @TableField(value = "delivery_status")
    private Integer deliveryStatus;
    /**
     * 超时状态，0: 无 1:即将超时 2:已超时
     */
    @TableField(value = "time_out_status")
    private Integer timeOutStatus;
    /**
     * 交付客服
     */
    @TableField(value = "delivery_customer_care")
    private String deliveryCustomerCare;
    /**
     * 预期完结时间
     */
    @TableField(value = "expect_complete_time")
    private LocalDateTime expectCompleteTime;
    /**
     * im客服端倒计时开始时间
     */
    @TableField(value = "im_count_down_time")
    private LocalDateTime imCountDownTime;
    /**
     * 暂停开始时间
     */
    @TableField(value = "pause_time")
    private LocalDateTime pauseTime;
    /**
     * 支付时间
     */
    @TableField(value = "pay_time")
    private LocalDateTime payTime;
    /**
     * 完结时间
     */
    @TableField(value = "complete_time")
    private LocalDateTime completeTime;
    /**
     * 预警计划游戏配置id,remind_plan_game_config
     */
    @TableField(value = "game_config_id")
    private Long gameConfigId;
    /**
     * 预警计划id,remind_plan
     */
    @TableField(value = "remind_plan_id")
    private Long remindPlanId;
    /**
     * 预警子计划id,remind_sub_plan
     */
    @TableField(value = "remind_sub_plan_id")
    private Long remindSubPlanId;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;
    /**
     * 创建人
     */
    @TableField(value = "create_user_id")
    private String createUserId;
    /**
     * 更新人
     */
    @TableField(value = "update_user_id")
    private String updateUserId;
    /**
     * 是否删除 0:否, 1:是
     */
    @TableLogic
    @TableField(value = "is_deleted")
    private Boolean deleted;

    /**
      * 环境变量
      */
     @TableField(value = "env_profile", fill = FieldFill.INSERT)
     private Integer envProfile;

}

