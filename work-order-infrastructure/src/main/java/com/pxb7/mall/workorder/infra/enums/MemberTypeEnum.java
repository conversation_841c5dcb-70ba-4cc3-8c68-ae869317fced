package com.pxb7.mall.workorder.infra.enums;

import lombok.Getter;
import lombok.ToString;

/**
 * 用户成员类型
 * <AUTHOR>
 */
@Getter
@ToString
public enum MemberTypeEnum {

    /**
     * 成员类型 1:普通用户；2：群主
     */

    COMMON_USER(1, "普通用户"),
    GROUP_OWNER(2, "群主");

    private final Integer code;
    private final String desc;

    MemberTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}
