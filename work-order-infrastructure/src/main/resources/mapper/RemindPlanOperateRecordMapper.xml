<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pxb7.mall.workorder.infra.repository.db.mapper.RemindPlanOperateRecordMapper">
    <resultMap id="BaseResultMap"
               type="com.pxb7.mall.workorder.infra.repository.db.entity.RemindPlanOperateRecord">
        <result column="id" jdbcType="BIGINT" property="id"/>
        <result column="remind_plan_id" jdbcType="VARCHAR" property="remindPlanId"/>
        <result column="opt_type" jdbcType="INTEGER" property="optType"/>
        <result column="data_type" jdbcType="INTEGER" property="dataType"/>
        <result column="origin_content" jdbcType="VARCHAR" property="originContent"/>
        <result column="new_content" jdbcType="VARCHAR" property="newContent"/>
        <result column="trace_id" jdbcType="VARCHAR" property="traceId"/>
        <result column="opt_user_id" jdbcType="VARCHAR" property="optUserId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_deleted" jdbcType="BOOLEAN" property="deleted"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into
        remind_plan_operate_record(remind_plan_id,opt_type,data_type,origin_content,new_content,trace_id,opt_user_id)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.remindPlanId},#{entity.optType},#{entity.dataType},#{entity.originContent},#{entity.newContent},#{entity.traceId},#{entity.optUserId})
        </foreach>
    </insert>
</mapper>

