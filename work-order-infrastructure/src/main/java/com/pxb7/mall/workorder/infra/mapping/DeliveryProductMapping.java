package com.pxb7.mall.workorder.infra.mapping;

import com.pxb7.mall.workorder.infra.model.RemindDeliveryProductReqPO;
import com.pxb7.mall.workorder.infra.repository.db.entity.RemindDeliveryProduct;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2025/4/3 17:34
 */

@Mapper
public interface DeliveryProductMapping {

    DeliveryProductMapping INSTANCE = Mappers.getMapper(DeliveryProductMapping.class);

    RemindDeliveryProduct addDto2PO(RemindDeliveryProductReqPO.AddPO source);

}
