package com.pxb7.mall.workorder.domain.mapping;

import com.pxb7.mall.workorder.domain.model.RemindWorkOrderBO;
import com.pxb7.mall.workorder.domain.model.RemindWorkOrderReqBO;
import com.pxb7.mall.workorder.domain.model.RemindWorkOrderRespBO;
import com.pxb7.mall.workorder.infra.model.RemindWorkOrderReqPO;
import com.pxb7.mall.workorder.infra.repository.db.entity.RemindWorkOrder;
import com.pxb7.mall.workorder.infra.repository.es.entity.RemindWorkOrderDoc;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;


@Mapper
public interface RemindWorkOrderDomainMapping {

    RemindWorkOrderDomainMapping INSTANCE = Mappers.getMapper(RemindWorkOrderDomainMapping.class);


    RemindWorkOrderReqPO.AddPO remindWorkOrderBO2AddPO(RemindWorkOrderReqBO.AddBO source);

    RemindWorkOrderReqPO.UpdatePO remindWorkOrderBO2UpdatePO(RemindWorkOrderReqBO.UpdateBO source);

    RemindWorkOrderReqPO.DelPO remindWorkOrderBO2DelPO(RemindWorkOrderReqBO.DelBO source);

    RemindWorkOrderReqPO.SearchPO remindWorkOrderBO2SearchPO(RemindWorkOrderReqBO.SearchBO source);

    RemindWorkOrderReqPO.PagePO remindWorkOrderBO2PagePO(RemindWorkOrderReqBO.PageBO source);

    RemindWorkOrderRespBO.DetailBO remindWorkOrderPO2DetailBO(RemindWorkOrder source);

    List<RemindWorkOrderRespBO.DetailBO> remindWorkOrderPO2ListBO(List<RemindWorkOrder> source);

    Page<RemindWorkOrderRespBO.DetailBO> remindWorkOrderPO2PageBO(Page<RemindWorkOrder> source);

    List<RemindWorkOrderBO> remindWorkOrder2ListBO(List<RemindWorkOrder> source);

    RemindWorkOrderBO remindWorkOrderDoc2DetailBO(RemindWorkOrderDoc source);

    List<RemindWorkOrderBO> remindWorkOrderDoc2ListBO(List<RemindWorkOrderDoc> source);

    List<RemindWorkOrderDoc> remindWorkOrderBO2ListDoc(List<RemindWorkOrder> source);

    RemindWorkOrderDoc remindWorkOrderBO2Doc(RemindWorkOrder source);

}


