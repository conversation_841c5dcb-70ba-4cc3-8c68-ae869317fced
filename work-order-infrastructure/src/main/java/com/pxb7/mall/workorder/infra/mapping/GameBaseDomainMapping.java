package com.pxb7.mall.workorder.infra.mapping;


import com.pxb7.mall.workorder.infra.model.GameBasePO;
import com.pxb7.mall.product.client.dto.response.game.GameBaseDTO;
import com.pxb7.mall.product.client.dto.response.game.GameIdDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface GameBaseDomainMapping {


    GameBaseDomainMapping INSTANCE = Mappers.getMapper(GameBaseDomainMapping.class);

    @Mapping(target = "maker", source = "makers")
    GameBasePO dto2BO(GameBaseDTO source);

    List<GameBasePO> dto2ListPO(List<GameBaseDTO> source);

    List<GameBasePO> idDto2ListPO(List<GameIdDTO> source);

}
