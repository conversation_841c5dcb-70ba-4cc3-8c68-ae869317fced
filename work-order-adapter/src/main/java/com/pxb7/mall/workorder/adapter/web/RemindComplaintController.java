package com.pxb7.mall.workorder.adapter.web;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pxb7.mall.response.PxPageResponse;
import com.pxb7.mall.response.PxResponse;
import com.pxb7.mall.workorder.app.model.ComplaintPageDetailDTO;
import com.pxb7.mall.workorder.app.model.ComplaintStatisticDataDTO;
import com.pxb7.mall.workorder.app.model.ComplaintStatisticDataSearchDTO;
import com.pxb7.mall.workorder.app.model.RemindComplaintReqDTO;
import com.pxb7.mall.workorder.app.service.RemindComplaintAppService;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 客诉工单预警
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/web/complaint/")
public class RemindComplaintController {

    @Resource
    private RemindComplaintAppService remindComplaintAppService;

    /**
     * 管理后台-客诉工单预警明细分页
     */
    @PostMapping("/page")
    public PxPageResponse<ComplaintPageDetailDTO> page(@RequestBody @Validated RemindComplaintReqDTO.PageDTO param) {
        Page<ComplaintPageDetailDTO> page = remindComplaintAppService.page(param);
        return PxPageResponse.of(page.getRecords(), (int)page.getTotal(), (int)page.getSize(), (int)page.getCurrent());
    }

    /**
     * 管理后台-客诉工单预警统计列表
     */
    @PostMapping("/statistic/list")
    public PxResponse<List<ComplaintStatisticDataDTO>>
        getStatisticList(@RequestBody @Validated ComplaintStatisticDataSearchDTO param) {
        return PxResponse.ok(remindComplaintAppService.getStatisticList(param));
    }

    /**
     * 数据更新时间
     */
    @GetMapping("/date")
    public PxResponse<LocalDateTime> getUpdateTime() {
        return PxResponse.ok(remindComplaintAppService.getUpdateTime());
    }

}
