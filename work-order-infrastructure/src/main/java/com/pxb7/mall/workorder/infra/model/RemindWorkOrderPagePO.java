package com.pxb7.mall.workorder.infra.model;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@ToString
public class RemindWorkOrderPagePO {

    /**
     * 工单ID
     */
    private String workOrderId;

    /**
     * 页码，从1开始
     */

    private Integer pageIndex;
    /**
     * 每页数量
     */
    private Integer pageSize;
}
