package com.pxb7.mall.workorder.domain.service;


import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollectionUtil;
import com.pxb7.mall.workorder.domain.mapping.RemindPlanGameConfigDomainMapping;
import com.pxb7.mall.workorder.domain.model.RemindPlanGameConfigBO;
import com.pxb7.mall.workorder.domain.model.RemindPlanGameConfigRefreshBO;
import com.pxb7.mall.workorder.domain.model.RemindPlanGameConfigReqBO;
import com.pxb7.mall.workorder.infra.model.RemindPlanGameConfigReqPO;
import com.pxb7.mall.workorder.infra.repository.db.RemindPlanGameConfigRepository;
import com.pxb7.mall.workorder.infra.repository.db.entity.RemindPlanGameConfig;
import com.pxb7.mall.workorder.domain.model.RemindPlanGameConfigRespBO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.transaction.annotation.Transactional;

/**
 * 预警计划游戏配置domain服务
 *
 * <AUTHOR>
 * @since 2025-03-24 21:11:14
 */
@Service
public class RemindPlanGameConfigDomainService {

    @Resource
    private RemindPlanGameConfigRepository remindPlanGameConfigRepository;

    public boolean insert(RemindPlanGameConfigBO param) {
        RemindPlanGameConfigReqPO.AddPO addPO = RemindPlanGameConfigDomainMapping.INSTANCE.remindPlanGameConfigBO2AddPO(param);
        return remindPlanGameConfigRepository.insert(addPO);
    }

    public boolean saveBatch(List<RemindPlanGameConfigBO> params) {
        List<  RemindPlanGameConfigReqPO.AddPO> addPOList = new ArrayList<>();
        for (RemindPlanGameConfigBO param : params) {
            RemindPlanGameConfigReqPO.AddPO addPO = RemindPlanGameConfigDomainMapping.INSTANCE.remindPlanGameConfigBO2AddPO(param);
            addPOList.add(addPO);
        }
        return remindPlanGameConfigRepository.saveBatch(addPOList);
    }

    @Transactional(rollbackFor = Throwable.class)
    public boolean refresh(RemindPlanGameConfigRefreshBO param) {
        // todo 要处理每次执行返回的值！！！
        // 新增
        List<RemindPlanGameConfigBO> needAddPlanGameConfigs = param.getNeedAddPlanGameConfigs();
        if (CollectionUtil.isNotEmpty(needAddPlanGameConfigs)){
            this.saveBatch(needAddPlanGameConfigs);
        }
        // 更新
        List<RemindPlanGameConfigBO> needUpdatePlanGameConfigs = param.getNeedUpdatePlanGameConfigs();
        if (CollectionUtil.isNotEmpty(needUpdatePlanGameConfigs)){
            for (RemindPlanGameConfigBO needUpdatePlanRule : needUpdatePlanGameConfigs) {
                this.update(needUpdatePlanRule);
            }
        }
        // 删除
        List<RemindPlanGameConfigBO> needDeletePlanGameConfigs = param.getNeedDeletePlanGameConfigs();
        if (CollectionUtil.isNotEmpty(needDeletePlanGameConfigs)){
            List<Long> planGameConfigIds =
                needDeletePlanGameConfigs.stream().map(RemindPlanGameConfigBO::getId).filter(Objects::nonNull).collect(Collectors.toList());
            boolean b = remindPlanGameConfigRepository.removeByIds(planGameConfigIds);
        }
        return true;
    }


    public boolean update(RemindPlanGameConfigBO param) {
        RemindPlanGameConfigReqPO.UpdatePO updatePO = RemindPlanGameConfigDomainMapping.INSTANCE.remindPlanGameConfigBO2UpdatePO(param);
        return remindPlanGameConfigRepository.update(updatePO);
    }

    public boolean deleteById(RemindPlanGameConfigReqBO.DelBO param) {
        RemindPlanGameConfigReqPO.DelPO delPO = RemindPlanGameConfigDomainMapping.INSTANCE.remindPlanGameConfigBO2DelPO(param);
        return remindPlanGameConfigRepository.deleteById(delPO);
    }

    public boolean deleteByIds(List<Long> ids) {
        return remindPlanGameConfigRepository.removeByIds(ids);
    }


    public RemindPlanGameConfigRespBO.DetailBO findById(Long id) {
        RemindPlanGameConfig entity = remindPlanGameConfigRepository.findById(id);
        return RemindPlanGameConfigDomainMapping.INSTANCE.remindPlanGameConfigPO2DetailBO(entity);

    }

    public List<RemindPlanGameConfigBO> list(RemindPlanGameConfigReqBO.SearchBO param) {
        RemindPlanGameConfigReqPO.SearchPO searchPO = RemindPlanGameConfigDomainMapping.INSTANCE.remindPlanGameConfigBO2SearchPO(param);
        List<RemindPlanGameConfig> list = remindPlanGameConfigRepository.list(searchPO);
        return RemindPlanGameConfigDomainMapping.INSTANCE.remindPlanGameConfigPO2ListBO(list);
    }

    public Page<RemindPlanGameConfigRespBO.DetailBO> page(RemindPlanGameConfigReqBO.PageBO param) {
        RemindPlanGameConfigReqPO.PagePO pagePO = RemindPlanGameConfigDomainMapping.INSTANCE.remindPlanGameConfigBO2PagePO(param);
        Page<RemindPlanGameConfig> page = remindPlanGameConfigRepository.page(pagePO);
        return RemindPlanGameConfigDomainMapping.INSTANCE.remindPlanGameConfigPO2PageBO(page);
    }

    public List<RemindPlanGameConfigBO> getRemindPlanGameConfigList(Long remindPlanId) {
        if (null == remindPlanId || remindPlanId < 1) {
            return Collections.emptyList();
        }
        RemindPlanGameConfigReqPO.SearchPO searchPO = new RemindPlanGameConfigReqPO.SearchPO();
        searchPO.setRemindPlanId(remindPlanId);
        List<RemindPlanGameConfig> list = remindPlanGameConfigRepository.list(searchPO);
        return RemindPlanGameConfigDomainMapping.INSTANCE.remindPlanGameConfigPO2ListBO(list);
    }

    public List<RemindPlanGameConfigBO> getRemindPlanGameConfigList(List<Long> remindPlanIds) {
        if (null == remindPlanIds || remindPlanIds.isEmpty()) {
            return Collections.emptyList();
        }
        RemindPlanGameConfigReqPO.SearchPO searchPO = new RemindPlanGameConfigReqPO.SearchPO();
        searchPO.setRemindPlanIds(remindPlanIds);
        List<RemindPlanGameConfig> list = remindPlanGameConfigRepository.list(searchPO);
        return RemindPlanGameConfigDomainMapping.INSTANCE.remindPlanGameConfigPO2ListBO(list);
    }

    public List<RemindPlanGameConfigBO> getRemindPlanGameConfigListBySubPlanIds(List<Long> remindSubPlanIds) {
        if (null == remindSubPlanIds || remindSubPlanIds.isEmpty()) {
            return Collections.emptyList();
        }
        RemindPlanGameConfigReqPO.SearchPO searchPO = new RemindPlanGameConfigReqPO.SearchPO();
        searchPO.setRemindSubPlanIds(remindSubPlanIds);
        List<RemindPlanGameConfig> list = remindPlanGameConfigRepository.list(searchPO);
        return RemindPlanGameConfigDomainMapping.INSTANCE.remindPlanGameConfigPO2ListBO(list);
    }


    /**
     * 根据gameId和serviceMode获取预警计划游戏配置
     * @param gameId
     * @param serviceMode
     * @return
     */
    public RemindPlanGameConfigBO getRemindPlanGameConfig(String gameId, Integer serviceMode) {
        RemindPlanGameConfig remindPlanGameConfig = remindPlanGameConfigRepository.getRemindPlanGameConfig(gameId, serviceMode);
        return RemindPlanGameConfigDomainMapping.INSTANCE.remindPlanGameConfigPO2BO(remindPlanGameConfig);
    }

    /**
     * 根据gameId和工单信息查询预警计划游戏配置
     * @param gameId
     * @param workOrderStatus
     * @param onShelfType
     * @param membership
     * @return
     */
    public RemindPlanGameConfigBO getRemindPlanGameConfig(String gameId, Integer workOrderStatus,
                                                          Integer onShelfType, Integer membership) {
        RemindPlanGameConfig remindPlanGameConfig =
                remindPlanGameConfigRepository.getRemindPlanGameConfig(gameId, workOrderStatus, onShelfType, membership);
        return RemindPlanGameConfigDomainMapping.INSTANCE.remindPlanGameConfigPO2BO(remindPlanGameConfig);
    }

    /**
     * 根据gameId和workOrderType获取预警计划游戏配置
     * @param gameId
     * @param workOrderType
     * @param membership
     * @return
     */
    public RemindPlanGameConfigBO getAssRemindPlanGameConfig(String gameId, Integer workOrderType, Integer membership) {
        RemindPlanGameConfig remindPlanGameConfig = remindPlanGameConfigRepository.getAssRemindPlanGameConfig(gameId, workOrderType, membership);
        return RemindPlanGameConfigDomainMapping.INSTANCE.remindPlanGameConfigPO2BO(remindPlanGameConfig);
    }

    /**
     * 根据complaintLevel和complaintChannel获取预警计划游戏配置
     * @param complaintLevel
     * @param complaintChannel
     * @return
     */
    public RemindPlanGameConfigBO getComplaintRemindPlanGameConfig(Integer complaintLevel, Integer complaintChannel) {
        RemindPlanGameConfig remindPlanGameConfig = remindPlanGameConfigRepository.getComplaintRemindPlanGameConfig(complaintLevel, complaintChannel);
        return RemindPlanGameConfigDomainMapping.INSTANCE.remindPlanGameConfigPO2BO(remindPlanGameConfig);
    }
}

