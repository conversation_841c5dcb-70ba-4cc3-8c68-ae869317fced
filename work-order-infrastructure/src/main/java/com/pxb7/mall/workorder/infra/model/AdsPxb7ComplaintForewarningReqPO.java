package com.pxb7.mall.workorder.infra.model;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 客诉预警统计(AdsPxb7ComplaintForewarning)实体类
 *
 * <AUTHOR>
 * @since 2025-05-08 18:15:04
 */
public class AdsPxb7ComplaintForewarningReqPO {

    @Getter
    @Setter
    @Accessors(chain = true)
    public static class AddPO {

        private String createDate;

        private Integer channel;

        private String handleUserId;

        private Integer complaintLevel;

        private Long complaintWorkOrderCnt;

        private Long complaintCompleteWorkOrderCnt;

        private Long complaintProcessingWorkOrderCnt;

        private Long complaintTimeoutingWorkOrderCnt;

        private Long complaintTimeoutedWorkOrderCnt;

        private String createUserId;

        private String updateUserId;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    public static class UpdatePO {

        private Long id;

        private String createDate;

        private Integer channel;

        private String handleUserId;

        private Integer complaintLevel;

        private Long complaintWorkOrderCnt;

        private Long complaintCompleteWorkOrderCnt;

        private Long complaintProcessingWorkOrderCnt;

        private Long complaintTimeoutingWorkOrderCnt;

        private Long complaintTimeoutedWorkOrderCnt;

        private String createUserId;

        private String updateUserId;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    public static class DelPO {
        private Long id;
    }

    @Getter
    @Setter
    @Accessors(chain = true)
    public static class SearchPO {

        private String createDate;

        private Integer channel;

        private String handleUserId;

        private Integer complaintLevel;

        private Long complaintWorkOrderCnt;

        private Long complaintCompleteWorkOrderCnt;

        private Long complaintProcessingWorkOrderCnt;

        private Long complaintTimeoutingWorkOrderCnt;

        private Long complaintTimeoutedWorkOrderCnt;

        private String createUserId;

        private String updateUserId;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    public static class PagePO {

        private String createDate;

        private Integer channel;

        private String handleUserId;

        private Integer complaintLevel;

        private Long complaintWorkOrderCnt;

        private Long complaintCompleteWorkOrderCnt;

        private Long complaintProcessingWorkOrderCnt;

        private Long complaintTimeoutingWorkOrderCnt;

        private Long complaintTimeoutedWorkOrderCnt;

        private String createUserId;

        private String updateUserId;

        /**
         * 页码，从1开始
         */
        private Integer pageIndex;
        /**
         * 每页数量
         */
        private Integer pageSize;

    }

}
