package com.pxb7.mall.workorder.infra.repository.db;


import java.util.List;
import java.util.Objects;

import com.pxb7.mall.workorder.infra.model.RemindPlanReqPO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.pxb7.mall.workorder.infra.repository.db.mapper.RemindPlanMapper;
import com.pxb7.mall.workorder.infra.repository.db.entity.RemindPlan;

/**
 * 提醒服务预警计划(RemindPlan)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-24 21:09:54
 */
@Slf4j
@Repository
public class RemindPlanDbRepository extends ServiceImpl<RemindPlanMapper, RemindPlan> implements RemindPlanRepository {

    @Override
    public boolean insert(RemindPlanReqPO.AddPO param) {
        RemindPlan entity = new RemindPlan();
        entity.setId(param.getId());
        entity.setPlanName(param.getPlanName());
        entity.setServiceType(param.getServiceType());
        entity.setNotDisturbPeriod(param.getNotDisturbPeriod());
        entity.setCreateUserId(param.getCreateUserId());
        entity.setUpdateUserId(param.getUpdateUserId());
        return this.save(entity);
    }

    @Override
    public boolean update(RemindPlanReqPO.UpdatePO param) {
        LambdaUpdateWrapper<RemindPlan> updateWrapper = new LambdaUpdateWrapper<>();
        //where
        updateWrapper.eq(RemindPlan::getId, param.getId());
        //set
        if (StringUtils.isNotBlank(param.getPlanName())) {
            updateWrapper.set(RemindPlan::getPlanName, param.getPlanName());
        }
        if (Objects.nonNull(param.getServiceType())) {
            updateWrapper.set(RemindPlan::getServiceType, param.getServiceType());
        }
        if (StringUtils.isNotBlank(param.getNotDisturbPeriod())) {
            updateWrapper.set(RemindPlan::getNotDisturbPeriod, param.getNotDisturbPeriod());
        }
        if (StringUtils.isNotBlank(param.getCreateUserId())) {
            updateWrapper.set(RemindPlan::getCreateUserId, param.getCreateUserId());
        }
        if (StringUtils.isNotBlank(param.getUpdateUserId())) {
            updateWrapper.set(RemindPlan::getUpdateUserId, param.getUpdateUserId());
        }
        if (Objects.nonNull(param.getUpdateTime())){
            updateWrapper.set(RemindPlan::getUpdateTime, param.getUpdateTime());
        }
        return this.update(updateWrapper);
    }

    @Override
    public boolean deleteById(RemindPlanReqPO.DelPO param) {
        return this.removeById(param.getId());
    }

    @Override
    public RemindPlan findById(Long id) {
        return this.getById(id);
    }


    @Override
    public List<RemindPlan> list(RemindPlanReqPO.SearchPO param) {
        LambdaQueryWrapper<RemindPlan> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(param.getPlanName())) {
            queryWrapper.eq(RemindPlan::getPlanName, param.getPlanName());
        }
        if (Objects.nonNull(param.getServiceType())) {
            queryWrapper.eq(RemindPlan::getServiceType, param.getServiceType());
        }
        if (StringUtils.isNotBlank(param.getNotDisturbPeriod())) {
            queryWrapper.eq(RemindPlan::getNotDisturbPeriod, param.getNotDisturbPeriod());
        }
        if (StringUtils.isNotBlank(param.getCreateUserId())) {
            queryWrapper.eq(RemindPlan::getCreateUserId, param.getCreateUserId());
        }
        if (StringUtils.isNotBlank(param.getUpdateUserId())) {
            queryWrapper.eq(RemindPlan::getUpdateUserId, param.getUpdateUserId());
        }
        return this.list(queryWrapper);
    }

    @Override
    public Page<RemindPlan> page(RemindPlanReqPO.PagePO param) {
        Page<RemindPlan> page = new Page<>(param.getPageIndex(), param.getPageSize());
        return this.getBaseMapper().page(page, param);
    }

    @Override
    public long countByPlanName(String palanName,Integer serviceType) {
        LambdaQueryWrapper<RemindPlan> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RemindPlan::getPlanName, palanName);
        queryWrapper.eq(RemindPlan::getServiceType,serviceType);
        return this.count(queryWrapper);
    }
}
