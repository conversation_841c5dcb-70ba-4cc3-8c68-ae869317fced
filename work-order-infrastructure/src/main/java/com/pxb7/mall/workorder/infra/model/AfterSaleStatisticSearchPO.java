package com.pxb7.mall.workorder.infra.model;

import java.util.List;

import lombok.Data;

@Data
public class AfterSaleStatisticSearchPO {


    /**
     * 工单类型 1:找回 2:纠纷
     */
    private Integer workOrderType;

    /**
     * 起始时间
     */
    //@JsonFormat(pattern = "yyyy-MM-dd")
    private String startDate;

    /**
     * 结束时间
     */
    //@JsonFormat(pattern = "yyyy-MM-dd")
    private String endDate;

    /**
     * 游戏id 列表
     */
    private List<String> gameIds;

    /**
     * 找回/纠纷处理人ID
     */
    private List<String> processUserIds;

    /**
     * 订单来源 1:散户 2:号商 3:3A号商
     */
    private Integer membership;

}
