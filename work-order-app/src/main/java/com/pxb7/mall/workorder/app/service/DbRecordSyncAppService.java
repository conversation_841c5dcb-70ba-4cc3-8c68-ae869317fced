package com.pxb7.mall.workorder.app.service;

import com.pxb7.mall.workorder.app.handler.AbstractDbRecordSyncHandler;
import com.pxb7.mall.workorder.app.handler.DbRecordSyncHandlerFactory;
import com.pxb7.mall.workorder.client.dto.DbRecordSyncDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class DbRecordSyncAppService {

    @Resource
    private DbRecordSyncHandlerFactory dbRecordSyncHandlerFactory;

    public void syncDbRecord(DbRecordSyncDTO dbRecordSyncDTO) {
        log.info("DbRecordSyncAppService.sync dbRecordSyncDTO:{}", dbRecordSyncDTO);
        AbstractDbRecordSyncHandler syncHandler =
            dbRecordSyncHandlerFactory.getSyncHandler(dbRecordSyncDTO.getTableName());
        if (null == syncHandler) {
            log.info("DbRecordSyncAppService.sync not match handler:{}", dbRecordSyncDTO.getTableName());
            return;
        }
        syncHandler.handle(dbRecordSyncDTO);
    }
}
