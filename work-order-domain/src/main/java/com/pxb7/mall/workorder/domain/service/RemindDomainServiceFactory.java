package com.pxb7.mall.workorder.domain.service;

import com.pxb7.mall.workorder.client.enums.BizTypeEnum;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/4/30 10:43
 */

@Component
public class RemindDomainServiceFactory {

    @Resource
    private List<BaseRemindDomainService> remindServiceList;

    public BaseRemindDomainService getRemindService(BizTypeEnum bizType) {
        if (Objects.isNull(bizType)) {
            return null;
        }
        if (CollectionUtils.isEmpty(remindServiceList)) {
            return null;
        }

        for (BaseRemindDomainService remindService : remindServiceList) {
            if (remindService.getBizType() == bizType) {
                return remindService;
            }
        }
        return null;
    }

}
