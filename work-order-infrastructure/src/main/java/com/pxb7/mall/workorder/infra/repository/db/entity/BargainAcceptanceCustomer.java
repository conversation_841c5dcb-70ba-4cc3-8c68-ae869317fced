package com.pxb7.mall.workorder.infra.repository.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 客服接单信息表(BargainAcceptanceCustomer)实体类
 *
 * <AUTHOR>
 * @since 2025-04-18 17:22:02
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "bargain_acceptance_customer")
@ToString
public class BargainAcceptanceCustomer implements Serializable {
    private static final long serialVersionUID = -54285697243382603L;
    /**
     * 自增id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 业务主键id
     */
    @TableField(value = "receive_id")
    private String receiveId;
    /**
     * 买家用户id
     */
    @TableField(value = "buyer_user_id")
    private String buyerUserId;
    /**
     * 接单日期
     */
    @TableField(value = "receive_date")
    private String receiveDate;
    /**
     * 接单客服ID
     */
    @TableField(value = "customer_id")
    private String customerId;
    /**
     * 接单客服名称
     */
    @TableField(value = "customer_name")
    private String customerName;
    /**
     * 接单状态 0：不可接单 1：待接单 2：已接单 3：已完结
     */
    @TableField(value = "receive_status")
    private Integer receiveStatus;
    /**
     * 接单时间
     */
    @TableField(value = "receive_time")
    private LocalDateTime receiveTime;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;
    /**
     * 删除标记
     */
    @TableLogic
    @TableField(value = "is_deleted")
    private Boolean deleted;


}

