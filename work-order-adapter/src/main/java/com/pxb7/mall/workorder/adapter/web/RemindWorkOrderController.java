package com.pxb7.mall.workorder.adapter.web;


import com.alibaba.cola.dto.MultiResponse;
import com.pxb7.mall.response.PxPageResponse;
import com.pxb7.mall.workorder.app.model.*;
import com.pxb7.mall.workorder.app.service.RemindWorkOrderAppService;
import jakarta.annotation.Resource;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.alibaba.cola.dto.PageResponse;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

/**
 * 商品工单预警
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/web/work-order/")
public class RemindWorkOrderController {

    @Resource
    private RemindWorkOrderAppService remindWorkOrderAppService;


    /**
     * 管理后台-商品工单预警明细分页
     */
    @PostMapping("page")
    public PxPageResponse<WorkOrderPageDetailDTO> page(@RequestBody @Validated RemindWorkOrderReqDTO.PageDTO param) {
        Page<WorkOrderPageDetailDTO> page = remindWorkOrderAppService.page(param);
        return PxPageResponse.of(page.getRecords(), (int) page.getTotal(), (int) page.getSize(), (int) page.getCurrent());
    }


    /**
     * 管理后台-商品工单预警统计列表
     */
    @PostMapping("statistic-list")
    public MultiResponse<WorkOrderStatisticDataDTO> getStatisticList(@RequestBody @Validated WorkOrderStatisticDataSearchDTO param) {
        List<WorkOrderStatisticDataDTO> list = remindWorkOrderAppService.getStatisticList(param);
        return MultiResponse.of(list);
    }

}

