package com.pxb7.mall.workorder.infra.repository.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 提醒服务预警子计划(RemindSubPlan)实体类
 *
 * <AUTHOR>
 * @since 2025-03-24 21:09:56
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "remind_sub_plan")
@ToString
public class RemindSubPlan implements Serializable {
    private static final long serialVersionUID = -12221001524136414L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 预警计划id
     */
    @TableField(value = "remind_plan_id")
    private Long remindPlanId;

    /**
     * 业务类型，1:代售，2:中介，服务类型为账号交付有值
     */
    @TableField(value = "business_type")
    private Integer businessType;
    /**
     * 工单状态：1:待接单，2:已接单，3:待跟进，服务类型为商品工单有值
     */
    @TableField(value = "work_order_status")
    private Integer workOrderStatus;
    /**
     * 上架方式：1:官方截图，2:自主截图，服务类型为商品工单且 不为“待接单”时有值
     */
    @TableField(value = "on_shelf_type")
    private Integer onShelfType;

    /**
     * 订单来源：1:散户工单,2:号商工单,3:3A工单，服务类型为商品工单有值
     */
    @TableField(value = "membership")
    private Integer membership;

    /**
     * 工单类型  1:找回 2:纠纷 服务类型为售后工单有值
     */
    @TableField(value = "work_order_type")
    private Integer workOrderType;

    /**
     * 投诉级别：1:一级，2:二级，3:三级，4:四级，5:五级，6:六级，服务类型为客诉工单有值
     */
    @TableField(value = "complaint_level")
    private Integer complaintLevel;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;
    /**
     * 创建人
     */
    @TableField(value = "create_user_id")
    private String createUserId;
    /**
     * 更新人
     */
    @TableField(value = "update_user_id")
    private String updateUserId;
    /**
     * 是否删除 0:否, 1:是
     */
    @TableLogic
    @TableField(value = "is_deleted")
    private Boolean deleted;

}

