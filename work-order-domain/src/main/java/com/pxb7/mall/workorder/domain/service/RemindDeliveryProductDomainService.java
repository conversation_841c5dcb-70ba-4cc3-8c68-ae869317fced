package com.pxb7.mall.workorder.domain.service;


import com.alibaba.cola.exception.Assert;
import com.alibaba.excel.util.BooleanUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pxb7.mall.trade.order.client.dto.response.order.RoomOrderDetailsRespDTO;
import com.pxb7.mall.workorder.client.enums.BizErrorCodeEnum;
import com.pxb7.mall.workorder.client.enums.BizTypeEnum;
import com.pxb7.mall.workorder.client.enums.CompleteStatusEnum;
import com.pxb7.mall.workorder.client.enums.DeliveryStatusEnum;
import com.pxb7.mall.workorder.client.enums.PlanExecuteStatusEnum;
import com.pxb7.mall.workorder.client.enums.TimeoutStatusEnum;
import com.pxb7.mall.workorder.client.message.RemindPlanMessage;
import com.pxb7.mall.workorder.domain.mapping.RemindDeliveryProductDomainMapping;
import com.pxb7.mall.workorder.domain.model.DeliveryProductStatisticDataBO;
import com.pxb7.mall.workorder.domain.model.DeliveryProductStatisticDataSearchBO;
import com.pxb7.mall.workorder.domain.model.ImGroupCmdMsgBO;
import com.pxb7.mall.workorder.domain.model.RemindDeliveryProductBO;
import com.pxb7.mall.workorder.domain.model.RemindDeliveryProductReqBO;
import com.pxb7.mall.workorder.domain.model.RemindDeliveryProductRespBO;
import com.pxb7.mall.workorder.domain.model.RemindPlanGameConfigBO;
import com.pxb7.mall.workorder.domain.model.RemindPlanRecordRespBO;
import com.pxb7.mall.workorder.domain.model.TimeConfigBO;
import com.pxb7.mall.workorder.domain.remind.task.RemindMessageTask;
import com.pxb7.mall.workorder.infra.aop.ClusterRedisLock;
import com.pxb7.mall.workorder.infra.constant.CommonConstants;
import com.pxb7.mall.workorder.infra.model.DeliveryStatictisDataPO;
import com.pxb7.mall.workorder.infra.model.DeliveryStatictisSearchPO;
import com.pxb7.mall.workorder.infra.model.RemindDeliveryProductReqPO;
import com.pxb7.mall.workorder.infra.repository.db.RemindDeliveryProductRepository;
import com.pxb7.mall.workorder.infra.repository.db.entity.RemindDeliveryProduct;
import com.pxb7.mall.workorder.infra.repository.db.mapper.AdsPxb7DeliveryForewarningMapper;
import com.pxb7.mall.workorder.infra.repository.es.entity.RemindDeliveryProductDoc;
import com.pxb7.mall.workorder.infra.repository.es.mapper.RemindDeliveryProductDocEsRepository;
import com.pxb7.mall.workorder.infra.repository.es.mapper.RemindDeliveryProductDocRepository;
import com.pxb7.mall.workorder.infra.util.IdGenUtil;
import com.pxb7.mall.workorder.infra.util.TimeUtils;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchPage;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * 账号交付预警记录domain服务
 *
 * <AUTHOR>
 * @since 2025-03-27 20:07:49
 */
@Slf4j
@Service
public class RemindDeliveryProductDomainService extends BaseRemindDomainService {

    @Resource
    private RemindDeliveryProductRepository remindDeliveryProductRepository;

    @Resource
    private RemindDeliveryProductDocEsRepository remindDeliveryProductDocEsRepository;

    @Resource
    private RemindDeliveryProductDocRepository remindDeliveryProductDocRepository;

    @Resource
    private AdsPxb7DeliveryForewarningMapper adsPxb7DeliveryForewarningMapper;

    @Resource
    private RemindMessageTask remindMessageTask;

    @Resource
    private RemindPlanRecordDomainService remindPlanRecordDomainService;

    @Override
    public BizTypeEnum getBizType() {
        return BizTypeEnum.DELIVERY_PRODUCT;
    }

    public boolean updateByOrderItemId(RemindDeliveryProductReqBO.UpdateBO param) {
        RemindDeliveryProductReqPO.UpdatePO updatePO = RemindDeliveryProductDomainMapping.INSTANCE.remindDeliveryProductBO2UpdatePO(param);
        return remindDeliveryProductRepository.updateByOrderItemId(updatePO);
    }

    public Page<RemindDeliveryProductBO> pageEs(RemindDeliveryProductReqBO.PageBO param) {

        RemindDeliveryProductReqPO.PagePO pagePO = RemindDeliveryProductDomainMapping.INSTANCE.remindDeliveryProductBO2PagePO(param);
        SearchPage<RemindDeliveryProductDoc> searchHits = remindDeliveryProductDocEsRepository.pageQueryDeliveryProduct(pagePO);
        if (searchHits == null){
            return new Page<>(param.getPageIndex(), param.getPageSize());
        }
        if (CollectionUtils.isEmpty(searchHits.getContent())) {
            return new Page<>(searchHits.getNumber(), searchHits.getSize(), searchHits.getTotalElements());
        }
        List<SearchHit<RemindDeliveryProductDoc>> content = searchHits.getContent();
        List<RemindDeliveryProductDoc> list = content.stream().map(SearchHit::getContent).toList();
        List<RemindDeliveryProductBO> remindWorkOrderBOS = RemindDeliveryProductDomainMapping.INSTANCE.remindDeliveryProductDoc2ListBO(list);
        Page<RemindDeliveryProductBO> remindWorkOrderBOPage = new Page<>(searchHits.getNumber(), searchHits.getSize(), searchHits.getTotalElements());
        remindWorkOrderBOPage.setRecords(remindWorkOrderBOS);
        return remindWorkOrderBOPage;
    }


    /**
     * 生成账号交付预警记录
     * @param orderInfoRespDTO
     * @param remindPlanGameConfig
     * @return
     */
    public RemindDeliveryProductReqBO.AddBO generateRemindDeliveryProduct(
            RoomOrderDetailsRespDTO orderInfoRespDTO, RemindPlanGameConfigBO remindPlanGameConfig) {
        //查询是否已存在
        RemindDeliveryProductRespBO.DetailBO detailBO = findByOrderItemId(orderInfoRespDTO.getOrderItemId(), null);
        if (Objects.nonNull(detailBO)) {
            return null;
        }
        //预期完结时间
        TimeConfigBO expectCompleteTimeConfig = remindPlanGameConfig.getExpectCompleteTimeConfig();
        LocalDateTime expectCompleteTime = TimeUtils.addSpecificTime(
                expectCompleteTimeConfig.getHours(), expectCompleteTimeConfig.getMinutes(), 0);
        //im客服端倒计时开始时间
        TimeConfigBO imCountDownTimeConfig = remindPlanGameConfig.getImCountDownTimeConfig();
        LocalDateTime imCountDownTime = Objects.nonNull(imCountDownTimeConfig) ?
                TimeUtils.subtractSpecificTime(expectCompleteTime, imCountDownTimeConfig.getHours(), imCountDownTimeConfig.getMinutes(), 0)
                : null;
        //插入账号交付预警记录
        RemindDeliveryProductReqBO.AddBO addBO = new RemindDeliveryProductReqBO.AddBO()
                .setRemindId("DP" + IdGenUtil.getShardingColumnId(orderInfoRespDTO.getOrderItemId()))
                .setProductId(orderInfoRespDTO.getProductId())
                .setProductCode(orderInfoRespDTO.getProductUniqueNo())
                .setGameId(orderInfoRespDTO.getGameId())
                .setOrderId(orderInfoRespDTO.getOrderId())
                .setOrderItemId(orderInfoRespDTO.getOrderItemId())
                .setGroupId(orderInfoRespDTO.getRoomId())
                .setCompleteStatus(CompleteStatusEnum.IN_PROCESS.getCode())
                .setTimeOutStatus(TimeoutStatusEnum.NONE.getCode())
                .setDeliveryCustomerCare(orderInfoRespDTO.getDeliveryCustomerId())
                .setExpectCompleteTime(expectCompleteTime)
                .setImCountDownTime(imCountDownTime)
                .setGameConfigId(remindPlanGameConfig.getId())
                .setRemindPlanId(remindPlanGameConfig.getRemindPlanId())
                .setRemindSubPlanId(remindPlanGameConfig.getRemindSubPlanId())
                .setCreateUserId(CommonConstants.SYS_DEFAULT_USER)
                .setUpdateUserId(CommonConstants.SYS_DEFAULT_USER);

        RemindDeliveryProductReqPO.AddPO addPO = RemindDeliveryProductDomainMapping.INSTANCE.remindDeliveryProductBO2AddPO(addBO);
        boolean result = remindDeliveryProductRepository.insert(addPO);
        Assert.isTrue(result,
                BizErrorCodeEnum.REMIND_RECORD_SAVE_ERROR.getErrCode(), BizErrorCodeEnum.REMIND_RECORD_SAVE_ERROR.getErrDesc());
        return addBO;
    }

    /**
     * 根据orderItemId查询账号交付预警记录
     * @param orderItemId
     * @param remindId
     * @return
     */
    public RemindDeliveryProductRespBO.DetailBO findByOrderItemId(String orderItemId, String remindId) {
        RemindDeliveryProduct entity = remindDeliveryProductRepository.findByOrderItemId(orderItemId, remindId);
        return RemindDeliveryProductDomainMapping.INSTANCE.remindDeliveryProductPO2DetailBO(entity);
    }

    public void deleteRemindDeliveryProductEs(List<String> remindIds) {
        if (CollectionUtils.isEmpty(remindIds)) {
            return;
        }
        remindDeliveryProductDocRepository.deleteAllById(remindIds);
    }

    @ClusterRedisLock(prefix = "work_order_data_sync", value = "#remindId")
    public void syncRemindDeliveryProductToEs(String remindId) {
        RemindDeliveryProduct remindDeliveryProduct = remindDeliveryProductRepository.getByRemindId(remindId);
        if (Objects.isNull(remindDeliveryProduct)
                || BooleanUtils.isTrue(remindDeliveryProduct.getDeleted())) {
            remindDeliveryProductDocRepository.deleteById(remindId);
        } else {
            RemindDeliveryProductDoc remindDeliveryProductDoc =
                    RemindDeliveryProductDomainMapping.INSTANCE.remindDeliveryProductPO2Doc(remindDeliveryProduct);
            if (null==remindDeliveryProductDoc) {
                return;
            }
            remindDeliveryProductDocRepository.save(remindDeliveryProductDoc);
        }
    }

    /**
     * 更新账号交付预警记录超时状态
     * @param remindPlanMessage
     */
    @Override
    public void updateWillTimeOutStatus(RemindPlanMessage remindPlanMessage) {
        RemindDeliveryProductRespBO.DetailBO remindDeliveryProduct =
                findByOrderItemId(remindPlanMessage.getBizId(), remindPlanMessage.getRemindId());
        if (!Objects.equals(CompleteStatusEnum.IN_PROCESS.getCode(), remindDeliveryProduct.getCompleteStatus())) {
            return;
        }
        //更新账号交付预警记录状态
        RemindDeliveryProductReqBO.UpdateBO updateBO = new RemindDeliveryProductReqBO.UpdateBO();
        updateBO.setOrderItemId(remindPlanMessage.getBizId());
        updateBO.setRemindId(remindPlanMessage.getRemindId());
        updateBO.setTimeOutStatus(TimeoutStatusEnum.WILL_TIMEOUT.getCode());
        this.updateByOrderItemId(updateBO);

        //通知客服端，把超时房间移到超时tab
        moveGroupToTimeoutList(
                Collections.singletonList(remindDeliveryProduct), TimeoutStatusEnum.WILL_TIMEOUT);
    }

    /**
     * 通过房间查询账号交付预警记录
     * @param groupId
     * @return
     */
    @Override
    public RemindDeliveryProductBO searchByGroupId(String groupId, String customerCareId) {
        RemindDeliveryProductDoc deliveryProductDoc = remindDeliveryProductDocEsRepository.searchByGroupId(groupId, customerCareId);
        return RemindDeliveryProductDomainMapping.INSTANCE.remindDeliveryProductDoc2BO(deliveryProductDoc);
    }

    /**
     * 处理提醒消息发送
     * @param remindPlanMessage
     */
    @Override
    public PlanExecuteStatusEnum handleRemindMessage(RemindPlanMessage remindPlanMessage) {
        //根据orderItemId查询账号交付预警记录
        RemindDeliveryProductRespBO.DetailBO remindDeliveryProduct =
                findByOrderItemId(remindPlanMessage.getBizId(), remindPlanMessage.getRemindId());
        if (Objects.isNull(remindDeliveryProduct)) {
            log.error("根据orderItemId查询账号交付预警记录为空，orderItemId:{}", remindPlanMessage.getBizId());
            return PlanExecuteStatusEnum.FAILED;
        }
        //判断交付状态
        if (Objects.equals(DeliveryStatusEnum.PAUSE.getCode(), remindDeliveryProduct.getDeliveryStatus())) {
            return PlanExecuteStatusEnum.PAUSE;
        }
        //获取最新的提醒计划
        RemindPlanRecordRespBO.DetailBO remindPlanRecord = remindPlanRecordDomainService.findByRemindIdWithPlanRecordId(remindPlanMessage.getRemindId(), remindPlanMessage.getPlanRecordId());

        //判断提醒是否是否变更了，如果变更了，放弃当前消息的提醒
        if (!Objects.equals(remindPlanRecord.getRemindTime(), remindPlanMessage.getRemindTime())) {
            return PlanExecuteStatusEnum.PAUSE;
        }
        //完结状态,1:未完结,2:已完结,3:终止
        Integer completeStatus = remindDeliveryProduct.getCompleteStatus();
        if (!Objects.equals(CompleteStatusEnum.IN_PROCESS.getCode(), completeStatus)) {
            return PlanExecuteStatusEnum.EXPIRED;
        }
        //处理提醒消息发送
        RemindDeliveryProductBO remindRecord = new RemindDeliveryProductBO();
        BeanUtils.copyProperties(remindDeliveryProduct, remindRecord);
        Boolean result = remindMessageTask.handleRemindMessage(remindPlanMessage, remindRecord);
        return Objects.equals(result, Boolean.TRUE)
                ? PlanExecuteStatusEnum.DONE : PlanExecuteStatusEnum.FAILED;
    }

    /**
     * 执行账号交付超时状态更新任务
     */
    @Override
    public void executeUpdateTimeoutStatusTask(String databaseName) {
        log.info("执行账号交付超时状态更新任务，任务开始，databaseName:{}", databaseName);
        //提前时间
        LocalDateTime nowDateTime = LocalDateTime.now();

        //分页查询符合条件的提醒计划记录
        //游标id
        long minId = 0;
        long pageSize = CommonConstants.QUERY_PAGE_SIZE;
        while (true) {
            RemindDeliveryProductReqBO.PageBO param = new RemindDeliveryProductReqBO.PageBO();
            param.setId(minId);
            param.setCompleteStatus(CompleteStatusEnum.IN_PROCESS.getCode());
            param.setTimeOutStatus(TimeoutStatusEnum.TIMEOUT.getCode());
            param.setExpectCompleteTime(nowDateTime);
            param.setPageIndex(1);
            param.setPageSize(pageSize);

            Page<RemindDeliveryProductRespBO.DetailBO> page = pageDb(param);
            List<RemindDeliveryProductRespBO.DetailBO> pageRecords = page.getRecords();
            if (CollectionUtils.isEmpty(pageRecords)) {
                break;
            }
            minId = pageRecords.get(pageRecords.size() - 1).getId();
            //更新状态为过期
            List<Long> ids = pageRecords.stream().map(RemindDeliveryProductRespBO.DetailBO::getId).toList();
            batchUpdateTimeoutStatusByIds(
                    ids, TimeoutStatusEnum.TIMEOUT);

            //通知客服端，把超时房间移到超时tab
            moveGroupToTimeoutList(
                    pageRecords, TimeoutStatusEnum.TIMEOUT);

            log.info("执行账号交付超时状态更新任务，databaseName:{}，page:{}，page size:{}", databaseName, page.getCurrent(), page.getSize());
        }
        log.info("执行账号交付超时状态更新任务，任务结束，databaseName:{}", databaseName);
    }

    /**
     * 分页查询
     * @param param
     * @return
     */
    private Page<RemindDeliveryProductRespBO.DetailBO> pageDb(RemindDeliveryProductReqBO.PageBO param) {
        RemindDeliveryProductReqPO.PagePO pagePO =
                RemindDeliveryProductDomainMapping.INSTANCE.remindDeliveryProductBO2PagePO(param);
        Page<RemindDeliveryProduct> page = remindDeliveryProductRepository.page(pagePO);
        return RemindDeliveryProductDomainMapping.INSTANCE.remindDeliveryProductPO2PageBO(page);
    }

    /**
     * 批量更新账号交付超时状态
     * @param ids
     * @param status
     * @return
     */
    private Boolean batchUpdateTimeoutStatusByIds(List<Long> ids, TimeoutStatusEnum status) {
        if (CollectionUtils.isEmpty(ids)) {
            return Boolean.FALSE;
        }
        return remindDeliveryProductRepository.batchUpdateTimeoutStatusByIds(ids, status.getCode());
    }

    /**
     * 通知客服端，把超时房间移到超时tab
     * @param deliveryProducts
     * @param status
     */
    private void moveGroupToTimeoutList(
            List<RemindDeliveryProductRespBO.DetailBO> deliveryProducts, TimeoutStatusEnum status) {
        List<ImGroupCmdMsgBO> imGroupCmdMsgList = new ArrayList<>();
        for (RemindDeliveryProductRespBO.DetailBO deliveryProduct : deliveryProducts) {
            ImGroupCmdMsgBO imGroupCmdMsgBO = new ImGroupCmdMsgBO()
                    .setGroupId(deliveryProduct.getGroupId())
                    .setTargetUserId(deliveryProduct.getDeliveryCustomerCare())
                    .setStatus(status);
            imGroupCmdMsgList.add(imGroupCmdMsgBO);
        }
        moveGroupToTimeoutList(imGroupCmdMsgList);
    }

    public List<DeliveryProductStatisticDataBO> getStatisticList(DeliveryProductStatisticDataSearchBO searchBO) {
        DeliveryStatictisSearchPO param = new DeliveryStatictisSearchPO();
        param.setStartDate(searchBO.getStartDate());
        param.setEndDate(searchBO.getEndDate());
        param.setGameIds(searchBO.getGameIds());
        param.setBusinessType(searchBO.getBusinessType());
        param.setDeliveryCustomerCareIds(searchBO.getDeliveryCustomerCareIds());
        List<DeliveryStatictisDataPO> deliveryStatictisDataPOS =
            adsPxb7DeliveryForewarningMapper.countBySearchParam(param);
        List<DeliveryProductStatisticDataBO> resultList= RemindDeliveryProductDomainMapping.INSTANCE.deliveryStatictisDataPO2ListBO(deliveryStatictisDataPOS);
        return resultList;
    }
}

