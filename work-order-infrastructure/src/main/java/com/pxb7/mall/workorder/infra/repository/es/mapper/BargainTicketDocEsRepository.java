package com.pxb7.mall.workorder.infra.repository.es.mapper;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.FieldValue;
import co.elastic.clients.elasticsearch._types.Refresh;
import co.elastic.clients.elasticsearch._types.query_dsl.*;
import co.elastic.clients.elasticsearch.core.UpdateRequest;
import co.elastic.clients.json.JsonData;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import com.pxb7.mall.workorder.infra.model.BargainTicketReqPO;
import com.pxb7.mall.workorder.infra.repository.es.entity.BargainTicketDoc;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.elasticsearch.client.elc.ElasticsearchTemplate;
import org.springframework.data.elasticsearch.client.elc.NativeQuery;
import org.springframework.data.elasticsearch.client.elc.NativeQueryBuilder;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHitSupport;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.SearchPage;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version : BargainTicketDocEsRepository.java, v 0.1 2025年04月16日 21:12 yang.xuexi Exp $
 */
@Slf4j
@Repository
public class BargainTicketDocEsRepository {
    @Resource
    private ElasticsearchTemplate elasticsearchTemplate;

    @Resource
    private ElasticsearchClient elasticsearchClient;


    /**
     * 已读更新
     *
     * @param receivedId
     * @param readFlag
     */
    public void updateReadFlag(String receivedId, Integer readFlag) {
        Map<String, Object> pramMap = Maps.newHashMap();
        pramMap.put("read_flag", readFlag);
        updateByMap(receivedId, pramMap);
    }


    /**
     * 交易状态更新
     *
     * @param receivedId
     * @param dealFlag
     */
    public void updateTradeFlag(String receivedId, Integer dealFlag) {
        Map<String, Object> pramMap = Maps.newHashMap();
        pramMap.put("deal_flag", dealFlag);
        updateByMap(receivedId, pramMap);
    }


    public void updateReceiveStatus(String receivedId, Integer receivedStatus) {
        Map<String, Object> pramMap = Maps.newHashMap();
        pramMap.put("receive_status", receivedStatus);
        updateByMap(receivedId, pramMap);
    }


    public void updateByMap(String docId, Map<String, Object> map) {
        UpdateRequest<String, Object> updateRequest = UpdateRequest.of(u -> u
                .index("bargain_ticket") // 指定索引名
                .id(docId) // 指定文档ID
                .doc(map) // 指定要更新的字段和值
                .refresh(Refresh.True)
        );

        try {
            elasticsearchClient.update(updateRequest, Map.class);
        } catch (Exception e) {
            log.error("update partial fields fail, exception message:", e);
        }
    }


    public Page<BargainTicketDoc> page(BargainTicketReqPO.PagePO param) {
        BoolQuery.Builder builder = buildPageQueryParam(param);
        // 修改: 支持多字段排序，先按 readFlag 升序，再按 createTime 降序
        Sort sort = Sort.by(
                Sort.Order.desc(BargainTicketDoc.Fields.updateTime)
        );
        if (Objects.equals(param.getSortType(), NumberUtils.INTEGER_TWO)) {
            sort = Sort.by(
                    Sort.Order.asc(BargainTicketDoc.Fields.readFlag),
                    Sort.Order.desc(BargainTicketDoc.Fields.updateTime)
            );
        }
        NativeQuery nativeQuery = NativeQuery.builder()
                .withQuery(builder.build()._toQuery())
                .withPageable(PageRequest.of(param.getPageIndex() - 1, param.getPageSize()))
                .withSort(sort)
                .withTrackTotalHits(true)
                .build();

        SearchHits<BargainTicketDoc> searchHits = elasticsearchTemplate.search(nativeQuery, BargainTicketDoc.class);
        SearchPage<BargainTicketDoc> searchHitsPage = SearchHitSupport.searchPageFor(searchHits, nativeQuery.getPageable());

        if (CollUtil.isEmpty(searchHitsPage.getContent())) {
            return new Page<>(searchHitsPage.getNumber(), searchHitsPage.getSize(), searchHitsPage.getTotalElements());
        }
        List<BargainTicketDoc> list = searchHitsPage.getContent().stream().map(SearchHit::getContent).toList();
        Page<BargainTicketDoc> page = new Page<>(searchHitsPage.getNumber(), searchHitsPage.getSize(), searchHitsPage.getTotalElements());
        page.setRecords(list);
        return page;
    }

    /**
     * 构造参数
     */
    public BoolQuery.Builder buildPageQueryParam(BargainTicketReqPO.PagePO param) {
        // 存储查询条件列表
        List<Query> mustQueryList = new ArrayList<>();
        if (!ObjectUtil.hasNull(param.getBeginTime(), param.getEndTime())) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS");

            // 使用 JsonData.of 直接处理时间戳，避免手动格式化
            RangeQuery query = QueryBuilders.range()
                    .field("create_time")
                    .gte(JsonData.of(param.getBeginTime().format(formatter)))
                    .lte(JsonData.of(param.getEndTime().format(formatter)))
                    .format("yyyy-MM-dd'T'HH:mm:ss.SSS")
                    .build();
            mustQueryList.add(query._toQuery());
        }

        if (StrUtil.isNotBlank(param.getBuyerPhone())) {
            mustQueryList.add(QueryBuilders.term()
                    .field("buyer_phone")
                    .value(param.getBuyerPhone())
                    .build()
                    ._toQuery());
        }
        if (StrUtil.isNotBlank(param.getReceiveId())) {
            mustQueryList.add(QueryBuilders.term()
                    .field("receive_id")
                    .value(param.getReceiveId())
                    .build()
                    ._toQuery());
        }

        if (Objects.nonNull(param.getNumberPriceMin())) {
            mustQueryList.add(QueryBuilders.range()
                    .field("number_price_min")
                    .gte(JsonData.of(param.getNumberPriceMin()))
                    .build()._toQuery());
        }

        if (Objects.nonNull(param.getDealFlag())) {
            mustQueryList.add(QueryBuilders.term()
                    .field("deal_flag")
                    .value(param.getDealFlag())
                    .build()
                    ._toQuery());
        }

        if (Objects.nonNull(param.getNumberPriceMax())) {
            mustQueryList.add(QueryBuilders.range()
                    .field("number_price_max")
                    .lte(JsonData.of(param.getNumberPriceMax()))
                    .build()._toQuery());
        }

        if (Objects.nonNull(param.getBargainingRatioStart())) {
            mustQueryList.add(QueryBuilders.range()
                    .field("bargaining_ratio_start")
                    .gte(JsonData.of(param.getBargainingRatioStart()))
                    .build()._toQuery());
        }

        if (Objects.nonNull(param.getBargainingRatioEnd())) {
            mustQueryList.add(QueryBuilders.range()
                    .field("bargaining_ratio_end")
                    .lte(JsonData.of(param.getBargainingRatioEnd()))
                    .build()._toQuery());
        }
        if (Objects.nonNull(param.getReadFlag())) {
            mustQueryList.add(QueryBuilders.term()
                    .field("read_flag")
                    .value(param.getReadFlag())
                    .build()
                    ._toQuery());
        }

        if (CollUtil.isNotEmpty(param.getGameIdList())) {
            List<FieldValue> values = param.getGameIdList().stream().filter(Objects::nonNull).map(FieldValue::of).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(values)) {
                mustQueryList.add(QueryBuilders.nested()
                        .path("game_info_list")
                        .query(QueryBuilders.terms()
                                .field("game_info_list.game_id")
                                .terms(builder -> builder.value(values))
                                .build()._toQuery())
                        .scoreMode(ChildScoreMode.Avg)
                        .build()._toQuery());
            }
        }

        if(StrUtil.isNotBlank(param.getProductUniqueNo())){
            mustQueryList.add(QueryBuilders.nested()
                    .path("product_info_list")
                    .query(QueryBuilders.term()
                            .field("product_info_list.product_unique_no")
                            .value(param.getProductUniqueNo())
                            .build()._toQuery())
                    .scoreMode(ChildScoreMode.Avg)
                    .build()._toQuery());
        }

        // 还价阶段查询
        if (CollUtil.isNotEmpty(param.getBargainStatusList())) {
            List<FieldValue> values = param.getBargainStatusList()
                    .stream()
                    .filter(Objects::nonNull)
                    .map(FieldValue::of)
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(values)) {
                // 修改: 使用嵌套查询处理内部结构 bargainStatus
                NestedQuery nestedQuery = QueryBuilders.nested()
                        .path("bargain_info_list")
                        .query(QueryBuilders.terms()
                                .field("bargain_info_list.bargain_status")
                                .terms(builder -> builder.value(values))
                                .build()._toQuery())
                        .scoreMode(ChildScoreMode.Avg)
                        .build();
                mustQueryList.add(nestedQuery._toQuery());
            }
        }

        // 工单状态查询
        if (Objects.nonNull(param.getReceiveStatus())) {
            TermQuery query = QueryBuilders.term()
                    .field("receive_status")
                    .value(param.getReceiveStatus())
                    .build();
            mustQueryList.add(query._toQuery());
        }
        if (CollUtil.isNotEmpty(param.getCustomerReceiveStatusList())) {
            List<FieldValue> values = param.getCustomerReceiveStatusList()
                    .stream()
                    .filter(Objects::nonNull)
                    .map(FieldValue::of)
                    .collect(Collectors.toList());
            if(CollUtil.isNotEmpty( values)){
                TermsQuery query = QueryBuilders.terms()
                        .field("receive_status")
                        .terms(builder -> builder.value(values))
                        .build();
                mustQueryList.add(query._toQuery());
            }
        }

        if (StrUtil.isNotBlank(param.getBuyerId())) {
            TermQuery query = QueryBuilders.term()
                    .field("buyer_id")
                    .value(param.getBuyerId())
                    .build();
            mustQueryList.add(query._toQuery());
        }

        if (StrUtil.isNotBlank(param.getCustomerId())) {
            mustQueryList.add(QueryBuilders.term()
                    .field("customer_id")
                    .value(param.getCustomerId())
                    .build()
                    ._toQuery());
        }
        return QueryBuilders.bool().must(mustQueryList);
    }

    public BargainTicketDoc findByReceiveId(String receiveId) {
        NativeQueryBuilder nativeQueryBuilder = new NativeQueryBuilder();
        BoolQuery.Builder boolFilter = QueryBuilders.bool();
        boolFilter.filter(QueryBuilders.term(a -> a.field("receive_id").value(receiveId)));
        nativeQueryBuilder.withQuery(boolFilter.build()._toQuery());
        SearchHit<BargainTicketDoc> bargainTicketDocSearchHit = elasticsearchTemplate.searchOne(nativeQueryBuilder.build(), BargainTicketDoc.class);
        return Objects.nonNull(bargainTicketDocSearchHit) ? bargainTicketDocSearchHit.getContent() : null;
    }
}
