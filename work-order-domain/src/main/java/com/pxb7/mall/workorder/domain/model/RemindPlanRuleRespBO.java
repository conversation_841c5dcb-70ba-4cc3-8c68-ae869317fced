package com.pxb7.mall.workorder.domain.model;

import java.util.Date;
import java.time.*;
import java.math.BigDecimal;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.ToString;


/**
 * 预警计划提醒规则配置(RemindPlanRule)实体类
 *
 * <AUTHOR>
 * @since 2025-03-24 21:11:14
 */
public class RemindPlanRuleRespBO {


    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class DetailBO {
        private Long id;
        private Integer nodeNumber;
        private String remindTimeConfig;
        private String remindMethodConfig;
        private Long remindPlanId;
        private Long remindSubPlanId;
        private LocalDateTime createTime;
        private LocalDateTime updateTime;
        private String createUserId;
        private String updateUserId;
        private Boolean deleted;
    }
}

