package com.pxb7.mall.workorder.infra.model;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;


/**
 * 生成提醒计划异常重试表(RemindPlanRetryTask)实体类
 *
 * <AUTHOR>
 * @since 2025-05-06 20:53:53
 */
public class RemindPlanRetryTaskReqPO {

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class AddPO {


        private String bizId;


        private Integer serviceType;


        private String recordInfo;


        private Integer retryTimes;


        private Integer status;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class UpdatePO {


        private Long id;


        private String bizId;


        private Integer serviceType;


        private String recordInfo;


        private Integer retryTimes;


        private Integer status;


    }


    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class DelPO {
        private Long id;
    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class SearchPO {

        private String bizId;


        private Integer serviceType;


        private String recordInfo;


        private Integer retryTimes;


        private Integer status;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class PagePO {

        private Long id;


        private String bizId;


        private Integer serviceType;


        private String recordInfo;


        private Integer retryTimes;


        private Integer status;


        /**
         * 页码，从1开始
         */
        private Long pageIndex;
        /**
         * 每页数量
         */
        private Long pageSize;

    }

}

