<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pxb7.mall.workorder.infra.repository.db.mapper.BargainAcceptanceCustomerMapper">
    <resultMap id="BaseResultMap" type="com.pxb7.mall.workorder.infra.repository.db.entity.BargainAcceptanceCustomer">
        <result column="id" jdbcType="INTEGER" property="id"/>
        <result column="receive_id" jdbcType="VARCHAR" property="receiveId"/>
        <result column="buyer_user_id" jdbcType="VARCHAR" property="buyerUserId"/>
        <result column="receive_date" jdbcType="VARCHAR" property="receiveDate"/>
        <result column="customer_id" jdbcType="VARCHAR" property="customerId"/>
        <result column="customer_name" jdbcType="VARCHAR" property="customerName"/>
        <result column="receive_status" jdbcType="INTEGER" property="receiveStatus"/>
        <result column="receive_time" jdbcType="TIMESTAMP" property="receiveTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_deleted" jdbcType="BOOLEAN" property="deleted"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into
        bargain_acceptance_customer(receive_id,buyer_user_id,receive_date,customer_id,customer_name,receive_status,receive_time,create_time,update_time,is_deleted)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.receiveId},#{entity.buyerUserId},#{entity.receiveDate},#{entity.customerId},#{entity.customerName},#{entity.receiveStatus},#{entity.receiveTime},#{entity.createTime},#{entity.updateTime},#{entity.deleted})
        </foreach>
    </insert>
</mapper>

