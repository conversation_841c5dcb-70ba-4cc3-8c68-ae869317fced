package com.pxb7.mall.workorder.infra.repository.db;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pxb7.mall.workorder.infra.model.RemindPlanGameConfigReqPO;
import com.pxb7.mall.workorder.infra.repository.db.entity.RemindPlanGameConfig;

/**
 * 预警计划游戏配置(RemindPlanGameConfig)表服务接口
 *
 * <AUTHOR>
 * @since 2025-03-24 21:09:56
 */
public interface RemindPlanGameConfigRepository extends IService<RemindPlanGameConfig> {

    boolean insert(RemindPlanGameConfigReqPO.AddPO param);

    boolean update(RemindPlanGameConfigReqPO.UpdatePO param);

    boolean deleteById(RemindPlanGameConfigReqPO.DelPO param);

    RemindPlanGameConfig findById(Long id);

    List<RemindPlanGameConfig> list(RemindPlanGameConfigReqPO.SearchPO param);

    Page<RemindPlanGameConfig> page(RemindPlanGameConfigReqPO.PagePO param);


    boolean saveBatch(List<RemindPlanGameConfigReqPO.AddPO> params);

    /**
     * 根据gameId和businessType查询预警计划游戏配置
     * @param gameId
     * @param businessType
     * @return
     */
    RemindPlanGameConfig getRemindPlanGameConfig(String gameId, Integer businessType);

    /**
     * 根据gameId和工单信息查询预警计划游戏配置
     * @param gameId
     * @param workOrderStatus
     * @param onShelfType
     * @param membership
     * @return
     */
    RemindPlanGameConfig getRemindPlanGameConfig(String gameId, Integer workOrderStatus,
                                                 Integer onShelfType, Integer membership);

    /**
     * 根据gameId和workOrderType查询预警计划游戏配置
     * @param gameId
     * @param workOrderType
     * @param membership
     * @return
     */
    RemindPlanGameConfig getAssRemindPlanGameConfig(String gameId, Integer workOrderType, Integer membership);

    /**
     * 根据complaintLevel和complaintChannel查询预警计划游戏配置
     * @param complaintLevel
     * @param complaintChannel
     * @return
     */
    RemindPlanGameConfig getComplaintRemindPlanGameConfig(Integer complaintLevel, Integer complaintChannel);

}

