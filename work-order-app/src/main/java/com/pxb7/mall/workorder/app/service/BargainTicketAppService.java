package com.pxb7.mall.workorder.app.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pxb7.mall.auth.c.util.AdminUserUtil;
import com.pxb7.mall.response.PxPageResponse;
import com.pxb7.mall.workorder.app.handler.bargain.BargainTicketSyncFactory;
import com.pxb7.mall.workorder.app.mapping.BargainTicketAppMapping;
import com.pxb7.mall.workorder.app.model.BargainTicketDetailDTO;
import com.pxb7.mall.workorder.app.model.BargainTicketPageDetailDTO;
import com.pxb7.mall.workorder.app.model.BargainTicketReqDTO;
import com.pxb7.mall.workorder.app.model.BargainTicketRespDTO;
import com.pxb7.mall.workorder.client.enums.BargainTicketEventEnum;
import com.pxb7.mall.workorder.domain.message.BargainTicketMessage;
import com.pxb7.mall.workorder.domain.model.BargainTicketBO;
import com.pxb7.mall.workorder.domain.model.BargainTicketCacheBO;
import com.pxb7.mall.workorder.domain.model.BargainTicketReqBO;
import com.pxb7.mall.workorder.domain.service.BargainTicketDomainService;
import com.pxb7.mall.workorder.infra.aop.ClusterRedisLock;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version : BargainTicketAppService.java, v 0.1 2025年04月16日 20:27 yang.xuexi Exp $
 */
@Service
@Slf4j
public class BargainTicketAppService {

    @Resource
    private BargainTicketDomainService bargainTicketDomainService;

    @Resource
    private BargainTicketSyncFactory bargainTicketSyncFactory;


    public PxPageResponse<BargainTicketPageDetailDTO> page(BargainTicketReqDTO.PageDTO pageDTO) {
        if (filterPage(pageDTO)) {
            return PxPageResponse.of(Collections.emptyList(), 0, pageDTO.getPageSize(), pageDTO.getPageIndex());
        }
        Page<BargainTicketBO> bargainTicketBOPage = bargainTicketDomainService.pageEs(BargainTicketAppMapping.INSTANCE.convert2Bo(pageDTO));
        List<BargainTicketPageDetailDTO> recordList = BargainTicketAppMapping.INSTANCE.convertList(bargainTicketBOPage.getRecords());
        return PxPageResponse.of(recordList, (int) bargainTicketBOPage.getTotal(), (int) bargainTicketBOPage.getSize(), (int) bargainTicketBOPage.getCurrent());
    }

    private boolean filterPage(BargainTicketReqDTO.PageDTO po) {
        String userId = AdminUserUtil.getUserId();
        if (Objects.equals(po.getPageTabSource(), 2)) {
            if (StrUtil.isBlank(userId)) {
                return true;
            }

            if (CollUtil.isNotEmpty(po.getCustomerReceiveStatusList()) &&
                    po.getCustomerReceiveStatusList().stream().anyMatch(status -> !Objects.equals(status, 2))) {
                return true;
            }

            po.setCustomerId(userId);
            po.setSortType(NumberUtils.INTEGER_TWO);
        }

        if (Objects.equals(po.getPageTabSource(), 3)) {
            if (StrUtil.isBlank(userId)) {
                return true;
            }
            if (CollUtil.isNotEmpty(po.getCustomerReceiveStatusList()) &&
                    po.getCustomerReceiveStatusList().stream().anyMatch(status -> !Objects.equals(status, 3))) {
                return true;
            }
            po.setCustomerId(userId);
        }
        return false;
    }


    /**
     * 工单详情页面查询
     *
     * @param receiveId
     * @return 工单页面详情信息
     */
    public BargainTicketRespDTO detail(String receiveId) {
        BargainTicketDetailDTO buyerInfo = getDetailByReceiveId(receiveId);
        if (Objects.isNull(buyerInfo)) {
            return new BargainTicketRespDTO();
        }
        //买家近7日工单记录
        List<BargainTicketDetailDTO> recentlySevenDaysDetailList = queryRecentlySevenDaysDetailListBy(buyerInfo.getBuyerId(), buyerInfo.getCreateTime());

        return new BargainTicketRespDTO(buyerInfo, recentlySevenDaysDetailList);
    }


    /**
     * 查询近七天买家还价工单记录
     *
     * @param buyerId 买家id
     * @return 返回最近七天记录
     */
    public List<BargainTicketDetailDTO> queryRecentlySevenDaysDetailListBy(String buyerId,LocalDateTime createTime) {
        BargainTicketReqBO.PageBO pageDTO = new BargainTicketReqBO.PageBO();
        pageDTO.setBuyerId(buyerId);
        pageDTO.setPageIndex(NumberUtils.INTEGER_ONE);
        pageDTO.setBeginTime(createTime.minusDays(7).toLocalDate().atStartOfDay());
        pageDTO.setEndTime(createTime.with(LocalTime.MIN));
        //7天最多能查询到7条记录
        pageDTO.setPageSize(10);
        Page<BargainTicketBO> bargainTicketBOPage = bargainTicketDomainService.pageEs(pageDTO);
        if (Objects.isNull(bargainTicketBOPage) || CollUtil.isEmpty(bargainTicketBOPage.getRecords())) {
            return Collections.emptyList();
        }
        return BargainTicketAppMapping.INSTANCE.convertTicketBo2DetailDtoList(bargainTicketBOPage.getRecords());
    }


    /**
     * 根据工单id查询
     *
     * @param receiveId
     * @return
     */
    public BargainTicketDetailDTO getDetailByReceiveId(String receiveId) {
        return BargainTicketAppMapping.INSTANCE.convertTicketBo2DetailDto(bargainTicketDomainService.findByReceiveId(receiveId));
    }

    @ClusterRedisLock(prefix = "bargain_ticket_data_sync", value = "#message.receiveId")
    public void syncBargainTicket(BargainTicketMessage message) {
        bargainTicketSyncFactory.getProcessor(BargainTicketEventEnum.of(message.getEventType())).process(message);
    }


    public String getCacheReceived(String userId) {
        return bargainTicketDomainService.getCacheReceived(userId);
    }

}
