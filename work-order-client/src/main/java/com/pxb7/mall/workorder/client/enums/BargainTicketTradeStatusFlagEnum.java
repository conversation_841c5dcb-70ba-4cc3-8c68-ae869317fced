package com.pxb7.mall.workorder.client.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version : BargainTicketReadFlagEnum.java, v 0.1 2025年04月23日 16:06 yang.xuexi Exp $
 */
@Getter
public enum BargainTicketTradeStatusFlagEnum {
    NONE(10,"未成交"),
    DEAL(20,"已成交"),
    ;

    BargainTicketTradeStatusFlagEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private Integer code;
    private String desc;


    public static BargainTicketTradeStatusFlagEnum of(Integer code) {
        return Arrays.stream(values())
                .filter(a -> Objects.equals(a.getCode(), code))
                .findFirst()
                .orElse(null);
    }
}
