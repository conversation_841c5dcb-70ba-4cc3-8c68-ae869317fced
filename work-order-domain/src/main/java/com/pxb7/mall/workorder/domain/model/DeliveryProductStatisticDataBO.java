package com.pxb7.mall.workorder.domain.model;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;

@Slf4j
@Data
public class DeliveryProductStatisticDataBO {

    /**
     * 日期
     */
    private String date;


    /**
     * 处理总数量 (交付房间数)
     */
    private Long processTotalCount;

    /**
     * 已完成数量 (已完结房间数)
     */
    private Long completeCount;


    /**
     * 处理中数量 （交付中房间数量）
     */
    private Long processingCount;


    /**
     * 即将超时
     */
    private Long willTimeoutCount;

    /**
     * 超时数量
     */
    private Long timeoutCount;


    /**
     * 游戏id 列表
     */
    private String gameId;


    /**
     * 游戏名称
     */
    private String gameName;


    /**
     * 交易类型，1:代售，2:中介
     */
    private Integer businessType;

    /**
     * 交付客服id
     */
    private String deliveryCustomerCareId;

    /**
     * 交付客服id
     */
    private String deliveryCustomerCareName;


    /**
     * 超时率= 已超时数/交付中房间数
     *
     * timeoutCount/processingCount
     */
    public BigDecimal getTimeoutRate() {
        if (timeoutCount == null) {
            return BigDecimal.ZERO;
        }
        if (processTotalCount == null || processTotalCount <= 0) {
            log.info("[work-order] 超时率计算异常，处理分母为零");
            return null; // 处理分母为零的情况
        }
        // 计算超时率
        try {
            BigDecimal timeout = BigDecimal.valueOf(timeoutCount);
            BigDecimal processTotalCount = BigDecimal.valueOf(this.processTotalCount);

            String timeoutRateStr = timeout.divide(processTotalCount, 4, RoundingMode.HALF_UP) // 设置精度和舍入模式
                .multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP).stripTrailingZeros()
                .toPlainString();

            return new BigDecimal(timeoutRateStr); // 确保最终结果的精度
        } catch (ArithmeticException | NumberFormatException e) {
            // 捕获可能的异常并返回 null
            log.error("[work-order] 计算超时率异常", e);
            return null;
        }
    }
}
