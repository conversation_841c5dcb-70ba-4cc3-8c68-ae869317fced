package com.pxb7.mall.workorder.app.model;

import java.util.Date;
import java.time.*;
import java.math.BigDecimal;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.ToString;


/**
 * 提醒服务预警子计划(RemindSubPlan)实体类
 *
 * <AUTHOR>
 * @since 2025-03-24 21:12:18
 */
public class RemindSubPlanRespDTO {


    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class DetailDTO {
        private Long id;
        private Integer businessType;
        private Integer workOrderStatus;
        private Integer onShelfType;
    }
}

