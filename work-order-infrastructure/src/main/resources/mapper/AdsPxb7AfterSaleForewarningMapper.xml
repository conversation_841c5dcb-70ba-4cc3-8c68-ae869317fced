<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pxb7.mall.workorder.infra.repository.db.mapper.AdsPxb7AfterSaleForewarningMapper">
    <resultMap id="BaseResultMap" type="com.pxb7.mall.workorder.infra.repository.db.entity.AdsPxb7AfterSaleForewarning">
        <result column="id" jdbcType="BIGINT" property="id"/>
        <result column="create_date" jdbcType="VARCHAR" property="createDate"/>
        <result column="process_user_id" jdbcType="VARCHAR" property="processUserId"/>
        <result column="game_id" jdbcType="VARCHAR" property="gameId"/>
        <result column="work_order_type" jdbcType="INTEGER" property="workOrderType"/>
        <result column="membership" jdbcType="INTEGER" property="membership"/>
        <result column="work_order_cnt" jdbcType="BIGINT" property="workOrderCnt"/>
        <result column="complete_work_order_cnt" jdbcType="BIGINT" property="completeWorkOrderCnt"/>
        <result column="processing_work_order_cnt" jdbcType="BIGINT" property="processingWorkOrderCnt"/>
        <result column="timeouting_work_order_cnt" jdbcType="BIGINT" property="timeoutingWorkOrderCnt"/>
        <result column="timeouted_work_order_cnt" jdbcType="BIGINT" property="timeoutedWorkOrderCnt"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_user_id" jdbcType="VARCHAR" property="createUserId"/>
        <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId"/>
        <result column="is_deleted" jdbcType="BOOLEAN" property="deleted"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into
        ads_pxb7_after_sale_forewarning(create_date,process_user_id,game_id,work_order_type,membership,work_order_cnt,complete_work_order_cnt,processing_work_order_cnt,timeouting_work_order_cnt,timeouted_work_order_cnt,create_time,update_time,create_user_id,update_user_id,is_deleted)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.createDate},#{entity.processUserId},#{entity.gameId},#{entity.workOrderType},#{entity.membership},#{entity.workOrderCnt},#{entity.completeWorkOrderCnt},#{entity.processingWorkOrderCnt},#{entity.timeoutingWorkOrderCnt},#{entity.timeoutedWorkOrderCnt},#{entity.createTime},#{entity.updateTime},#{entity.createUserId},#{entity.updateUserId},#{entity.deleted})
        </foreach>
    </insert>

    <select id="countBySearchParam" resultType="com.pxb7.mall.workorder.infra.model.AfterSaleStatisticDataPO">
        select
        create_date,
        sum(work_order_cnt) as work_order_cnt,
        sum(complete_work_order_cnt) as complete_work_order_cnt,
        sum(processing_work_order_cnt) as processing_work_order_cnt,
        sum(timeouting_work_order_cnt) as timeouting_work_order_cnt,
        sum(timeouted_work_order_cnt) as timeouted_work_order_cnt
        from ads_pxb7_after_sale_forewarning
        where is_deleted = 0
        and create_date BETWEEN #{param.startDate} AND #{param.endDate}
        <if test="param.workOrderType != null">
            and work_order_type = #{param.workOrderType}
        </if>
        <if test="param.gameIds !=null and !param.gameIds.isEmpty()">
            and game_id IN
            <foreach collection="param.gameIds" item="gameId" separator="," open="(" close=")">
                <!-- 使用 #{gameId} 防止 SQL 注入 -->
                #{gameId}
            </foreach>
        </if>
        <if test="param.membership != null">
            and membership = #{param.membership}
        </if>
        <if test="param.processUserIds !=null and !param.processUserIds.isEmpty()">
            and process_user_id IN
            <foreach collection="param.processUserIds" item="processUserId" separator="," open="(" close=")">
                #{processUserId}
            </foreach>
        </if>
        group by create_date
        ORDER BY create_date DESC
    </select>

    <select id="getLastDataInsertTime" resultType="java.time.LocalDateTime">
        select create_time from ads_pxb7_after_sale_forewarning order by create_time DESC limit 1;
    </select>
</mapper>

