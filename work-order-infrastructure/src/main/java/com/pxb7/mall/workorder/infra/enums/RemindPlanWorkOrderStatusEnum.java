package com.pxb7.mall.workorder.infra.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum RemindPlanWorkOrderStatusEnum {

    /**
     * 1:待接单，2:已接单，3:待跟进
     */
    WAIT_ACCEPT(1, "待接单", "waitAccept"),
    ACCEPTED(2, "已接单", "accepted"),
    WAIT_FLOW(3, "待跟进", "waitFlow"),
        ;

    private final Integer value;
    private final String label;
    private final String bizCode;

    public static RemindPlanWorkOrderStatusEnum getEnum(Integer value) {
        if (null == value) {
            return null;
        }
        return Arrays.stream(RemindPlanWorkOrderStatusEnum.values()).filter(e -> value.equals(e.getValue())).findFirst()
            .orElse(null);
    }


}
