package com.pxb7.mall.workorder.infra.model;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 售后找回纠纷预警统计(AdsPxb7AfterSaleForewarning)实体类
 *
 * <AUTHOR>
 * @since 2025-05-08 18:13:53
 */
public class AdsPxb7AfterSaleForewarningReqPO {

    @Getter
    @Setter
    @Accessors(chain = true)
    public static class AddPO {

        private String createDate;

        private String processUserId;

        private String gameId;

        private Integer workOrderType;

        private Integer membership;

        private Long workOrderCnt;

        private Long completeWorkOrderCnt;

        private Long processingWorkOrderCnt;

        private Long timeoutingWorkOrderCnt;

        private Long timeoutedWorkOrderCnt;

        private String createUserId;

        private String updateUserId;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    public static class UpdatePO {

        private Long id;

        private String createDate;

        private String processUserId;

        private String gameId;

        private Integer workOrderType;

        private Integer membership;

        private Long workOrderCnt;

        private Long completeWorkOrderCnt;

        private Long processingWorkOrderCnt;

        private Long timeoutingWorkOrderCnt;

        private Long timeoutedWorkOrderCnt;

        private String createUserId;

        private String updateUserId;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    public static class DelPO {
        private Long id;
    }

    @Getter
    @Setter
    @Accessors(chain = true)
    public static class SearchPO {

        private String createDate;

        private String processUserId;

        private String gameId;

        private Integer workOrderType;

        private Integer membership;

        private Long workOrderCnt;

        private Long completeWorkOrderCnt;

        private Long processingWorkOrderCnt;

        private Long timeoutingWorkOrderCnt;

        private Long timeoutedWorkOrderCnt;

        private String createUserId;

        private String updateUserId;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    public static class PagePO {

        private String createDate;

        private String processUserId;

        private String gameId;

        private Integer workOrderType;

        private Integer membership;

        private Long workOrderCnt;

        private Long completeWorkOrderCnt;

        private Long processingWorkOrderCnt;

        private Long timeoutingWorkOrderCnt;

        private Long timeoutedWorkOrderCnt;

        private String createUserId;

        private String updateUserId;

        /**
         * 页码，从1开始
         */
        private Integer pageIndex;
        /**
         * 每页数量
         */
        private Integer pageSize;

    }

}
