package com.pxb7.mall.workorder.app.model;

import com.pxb7.mall.workorder.client.enums.RemindMethodEnum;
import com.pxb7.mall.workorder.client.enums.RemindObjectEnum;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.List;

@Getter
@Setter
@Accessors(chain = true)
@ToString
public class RemindMethodConfigDTO {

    /**
     * 提醒方式  feishu:飞书;im:im渠道;backend:管理后台
     * @see  RemindMethodEnum
     */
    @NotNull(message = "提醒方式字段不能为空")
    private String method;

    /**
     * 通知对象列表
     */
    @Valid
    @NotEmpty(message = "提醒对象不能为空")
    private List<RemindObjectDTO> objects;


    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class RemindObjectDTO {

        /**
         * 提醒对象： customerCare:交付客服;group:飞书群组;artDesigner:美工;auditCustomerCare:审核客服;follower:跟进人;custom:自定义
         *  retrieveCustomerCare:找回客服
         *
         * @see RemindObjectEnum
         */
        @NotNull(message = "提醒对象字段不能为空")
        private String object;

        /**
         * 钩子/账号。。。
         */
        private String webhook;


        /**
         * 自定义其他提醒对象
         */
        private String customUserIds;
    }
}
