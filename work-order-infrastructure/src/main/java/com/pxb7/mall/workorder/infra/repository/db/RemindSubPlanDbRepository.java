package com.pxb7.mall.workorder.infra.repository.db;


import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import cn.hutool.core.collection.CollectionUtil;
import com.pxb7.mall.workorder.infra.model.RemindSubPlanReqPO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.pxb7.mall.workorder.infra.repository.db.mapper.RemindSubPlanMapper;
import com.pxb7.mall.workorder.infra.repository.db.entity.RemindSubPlan;

/**
 * 提醒服务预警子计划(RemindSubPlan)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-24 21:09:57
 */
@Slf4j
@Repository
public class RemindSubPlanDbRepository extends ServiceImpl<RemindSubPlanMapper, RemindSubPlan> implements RemindSubPlanRepository {

    @Override
    public boolean insert(RemindSubPlanReqPO.AddPO param) {
        RemindSubPlan entity = new RemindSubPlan();
        entity.setId(param.getId());
        entity.setBusinessType(param.getBusinessType());
        entity.setWorkOrderStatus(param.getWorkOrderStatus());
        entity.setOnShelfType(param.getOnShelfType());
        entity.setMembership(param.getMembership());
        entity.setWorkOrderType(param.getWorkOrderType());
        entity.setComplaintLevel(param.getComplaintLevel());
        entity.setCreateUserId(param.getCreateUserId());
        entity.setUpdateUserId(param.getUpdateUserId());
        return this.save(entity);
    }

    @Override
    public boolean saveBatch(List<RemindSubPlanReqPO.AddPO> params) {
        List<RemindSubPlan> entities = new ArrayList<>();
        for (RemindSubPlanReqPO.AddPO param : params){
            RemindSubPlan entity = new RemindSubPlan();
            entity.setId(param.getId());
            entity.setBusinessType(param.getBusinessType());
            entity.setWorkOrderStatus(param.getWorkOrderStatus());
            entity.setOnShelfType(param.getOnShelfType());
            entity.setMembership(param.getMembership());
            entity.setWorkOrderType(param.getWorkOrderType());
            entity.setComplaintLevel(param.getComplaintLevel());
            entity.setCreateUserId(param.getCreateUserId());
            entity.setUpdateUserId(param.getUpdateUserId());
            entity.setRemindPlanId(param.getRemindPlanId());
            entities.add(entity);
        }
        return this.saveBatch(entities);
    }

    @Override
    public boolean update(RemindSubPlanReqPO.UpdatePO param) {
        LambdaUpdateWrapper<RemindSubPlan> updateWrapper = new LambdaUpdateWrapper<>();
        //where
        updateWrapper.eq(RemindSubPlan::getId, param.getId());
        //set
        if (Objects.nonNull(param.getRemindPlanId())) {
            updateWrapper.eq(RemindSubPlan::getRemindPlanId, param.getRemindPlanId());
        }
        if (Objects.nonNull(param.getBusinessType())) {
            updateWrapper.set(RemindSubPlan::getBusinessType, param.getBusinessType());
        }
        if (Objects.nonNull(param.getWorkOrderStatus())) {
            updateWrapper.set(RemindSubPlan::getWorkOrderStatus, param.getWorkOrderStatus());
        }
        if (Objects.nonNull(param.getOnShelfType())) {
            updateWrapper.set(RemindSubPlan::getOnShelfType, param.getOnShelfType());
        }
        if (Objects.nonNull(param.getMembership())) {
            updateWrapper.set(RemindSubPlan::getMembership, param.getMembership());
        }
        if (Objects.nonNull(param.getWorkOrderType())) {
            updateWrapper.set(RemindSubPlan::getWorkOrderType, param.getWorkOrderType());
        }
        if (Objects.nonNull(param.getComplaintLevel())) {
            updateWrapper.set(RemindSubPlan::getComplaintLevel, param.getComplaintLevel());
        }

        if (StringUtils.isNotBlank(param.getCreateUserId())) {
            updateWrapper.set(RemindSubPlan::getCreateUserId, param.getCreateUserId());
        }
        if (StringUtils.isNotBlank(param.getUpdateUserId())) {
            updateWrapper.set(RemindSubPlan::getUpdateUserId, param.getUpdateUserId());
        }
        return this.update(updateWrapper);
    }

    @Override
    public boolean deleteById(RemindSubPlanReqPO.DelPO param) {
        return this.removeById(param.getId());
    }

    @Override
    public RemindSubPlan findById(Long id) {
        return this.getById(id);
    }

    @Override
    public List<RemindSubPlan> list(RemindSubPlanReqPO.SearchPO param) {
        LambdaQueryWrapper<RemindSubPlan> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RemindSubPlan::getDeleted, false);
        if (Objects.nonNull(param.getRemindPlanId())) {
            queryWrapper.eq(RemindSubPlan::getRemindPlanId, param.getRemindPlanId());
        }
        if (CollectionUtil.isNotEmpty(param.getRemindPlanIds())){
            queryWrapper.in(RemindSubPlan::getRemindPlanId, param.getRemindPlanIds());
        }
        if (Objects.nonNull(param.getBusinessType())) {
            queryWrapper.eq(RemindSubPlan::getBusinessType, param.getBusinessType());
        }
        if (Objects.nonNull(param.getWorkOrderStatus())) {
            queryWrapper.eq(RemindSubPlan::getWorkOrderStatus, param.getWorkOrderStatus());
        }
        if (Objects.nonNull(param.getOnShelfType())) {
            queryWrapper.eq(RemindSubPlan::getOnShelfType, param.getOnShelfType());
        }
        if (Objects.nonNull(param.getMembership())) {
            queryWrapper.eq(RemindSubPlan::getMembership, param.getMembership());
        }
        if (Objects.nonNull(param.getWorkOrderType())) {
            queryWrapper.eq(RemindSubPlan::getWorkOrderType, param.getWorkOrderType());
        }
        if (Objects.nonNull(param.getComplaintLevel())) {
            queryWrapper.eq(RemindSubPlan::getComplaintLevel, param.getComplaintLevel());
        }

        if (StringUtils.isNotBlank(param.getCreateUserId())) {
            queryWrapper.eq(RemindSubPlan::getCreateUserId, param.getCreateUserId());
        }
        if (StringUtils.isNotBlank(param.getUpdateUserId())) {
            queryWrapper.eq(RemindSubPlan::getUpdateUserId, param.getUpdateUserId());
        }

        if (CollectionUtil.isNotEmpty(param.getBusinessTypes())){
            queryWrapper.in(RemindSubPlan::getBusinessType, param.getBusinessTypes());
        }

        if (CollectionUtil.isNotEmpty(param.getOnShelfTypes())){
            queryWrapper.in(RemindSubPlan::getOnShelfType, param.getOnShelfTypes());
        }

        if (CollectionUtil.isNotEmpty(param.getWorkOrderStatuses())){
            queryWrapper.in(RemindSubPlan::getWorkOrderStatus, param.getWorkOrderStatuses());
        }

        if (CollectionUtil.isNotEmpty(param.getMemberships())){
            queryWrapper.in(RemindSubPlan::getMembership, param.getMemberships());
        }
        return this.list(queryWrapper);
    }



    @Override
    public Page<RemindSubPlan> page(RemindSubPlanReqPO.PagePO param) {
        LambdaQueryWrapper<RemindSubPlan> queryWrapper = new LambdaQueryWrapper<>();
        // 返回分页查询结果
        if (Objects.nonNull(param.getRemindPlanId())) {
            queryWrapper.eq(RemindSubPlan::getRemindPlanId, param.getRemindPlanId());
        }
        if (Objects.nonNull(param.getBusinessType())) {
            queryWrapper.eq(RemindSubPlan::getBusinessType, param.getBusinessType());
        }
        if (Objects.nonNull(param.getWorkOrderStatus())) {
            queryWrapper.eq(RemindSubPlan::getWorkOrderStatus, param.getWorkOrderStatus());
        }
        if (Objects.nonNull(param.getOnShelfType())) {
            queryWrapper.eq(RemindSubPlan::getOnShelfType, param.getOnShelfType());
        }
        if (Objects.nonNull(param.getMembership())) {
            queryWrapper.eq(RemindSubPlan::getMembership, param.getMembership());
        }
        if (Objects.nonNull(param.getWorkOrderType())) {
            queryWrapper.eq(RemindSubPlan::getWorkOrderType, param.getWorkOrderType());
        }
        if (Objects.nonNull(param.getComplaintLevel())) {
            queryWrapper.eq(RemindSubPlan::getComplaintLevel, param.getComplaintLevel());
        }

        if (StringUtils.isNotBlank(param.getCreateUserId())) {
            queryWrapper.eq(RemindSubPlan::getCreateUserId, param.getCreateUserId());
        }
        if (StringUtils.isNotBlank(param.getUpdateUserId())) {
            queryWrapper.eq(RemindSubPlan::getUpdateUserId, param.getUpdateUserId());
        }
        return this.page(new Page<>(param.getPageIndex(), param.getPageSize()), queryWrapper);
    }
}
