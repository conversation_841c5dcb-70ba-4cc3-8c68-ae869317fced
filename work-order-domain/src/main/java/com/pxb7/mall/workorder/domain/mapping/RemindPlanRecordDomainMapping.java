package com.pxb7.mall.workorder.domain.mapping;

import com.pxb7.mall.workorder.domain.model.RemindPlanRecordReqBO;
import com.pxb7.mall.workorder.domain.model.RemindPlanRecordRespBO;
import com.pxb7.mall.workorder.infra.model.RemindPlanRecordReqPO;
import com.pxb7.mall.workorder.infra.repository.db.entity.RemindPlanRecord;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;


@Mapper
public interface RemindPlanRecordDomainMapping {

    RemindPlanRecordDomainMapping INSTANCE = Mappers.getMapper(RemindPlanRecordDomainMapping.class);


    RemindPlanRecordReqPO.AddPO remindPlanRecordBO2AddPO(RemindPlanRecordReqBO.AddBO source);

    RemindPlanRecordReqPO.UpdatePO remindPlanRecordBO2UpdatePO(RemindPlanRecordReqBO.UpdateBO source);

    RemindPlanRecordReqPO.DelPO remindPlanRecordBO2DelPO(RemindPlanRecordReqBO.DelBO source);

    RemindPlanRecordReqPO.SearchPO remindPlanRecordBO2SearchPO(RemindPlanRecordReqBO.SearchBO source);

    RemindPlanRecordReqPO.PagePO remindPlanRecordBO2PagePO(RemindPlanRecordReqBO.PageBO source);

    RemindPlanRecordRespBO.DetailBO remindPlanRecordPO2DetailBO(RemindPlanRecord source);

    List<RemindPlanRecordRespBO.DetailBO> remindPlanRecordPO2ListBO(List<RemindPlanRecord> source);

    Page<RemindPlanRecordRespBO.DetailBO> remindPlanRecordPO2PageBO(Page<RemindPlanRecord> source);

    List<RemindPlanRecordReqPO.AddPO> remindPlanRecordBO2AddPOList(List<RemindPlanRecordReqBO.AddBO> source);


}


