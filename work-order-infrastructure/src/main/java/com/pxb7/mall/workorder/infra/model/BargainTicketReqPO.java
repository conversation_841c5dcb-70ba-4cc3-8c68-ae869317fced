package com.pxb7.mall.workorder.infra.model;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version : BargainTicketReqDTO.java, v 0.1 2025年04月16日 14:33 yang.xuexi Exp $
 */
public class BargainTicketReqPO {


    /**
     * 分页查询入参
     */
    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class PagePO {

        /**
         * 工单id
         */
        private String receiveId;
        /**
         * 买家id
         */
        private String buyerId;


        /**
         * 买家手机号
         */
        private String buyerPhone;

        /**
         * 商品编号
         */
        private String productUniqueNo;


        /**
         * 最低号价
         */
        private Long numberPriceMin;

        /**
         * 最高号价
         */
        private Long numberPriceMax;

        /**
         * 议价比例开始价（单位：分）
         */
        private Double bargainingRatioStart;

        /**
         * 议价比例结束价（单位：分）
         */
        private Double bargainingRatioEnd;
        /**
         * 游戏ID
         */
        private String gameId;
        /**
         * 游戏ID
         */
        private List<String> gameIdList;


        /**
         * 还价阶段：0- 全部  1:买家出价  2:卖家还价  3:卖家拒绝  4:卖家同意  5:买家已下单  6：买家超时未处理  7：买家超时未下单  8：卖家超时未处理
         */
        private Integer bargainStatus = 0;
        /**
         * 还价阶段：0- 全部  1:买家出价  2:卖家还价  3:卖家拒绝  4:卖家同意  5:买家已下单  6：买家超时未处理  7：买家超时未下单  8：卖家超时未处理
         */
        private List<Integer> bargainStatusList;

        /**
         * 工单状态( 0：不可接单 1：待接单 2：已接单 3：已完结') 不填查所有
         */
        private Integer receiveStatus;

        /**
         * 接单客服ID
         */
        private String customerId;

        /**
         * 页码，从1开始
         */
        private Integer pageIndex;
        /**
         * 每页数量
         */
        private Integer pageSize;

        /**
         * 查询开始时间
         */
        private LocalDateTime beginTime;
        /**
         * 查询结束时间
         */
        private LocalDateTime endTime;

        /**
         * 10:未读 20:已读
         */
        private Integer readFlag;
        /**
         * 工单状态( 0：不可接单 1：待接单 2：已接单 3：已完结') 不填查所有
         */
        private List<Integer> customerReceiveStatusList;

        /**
         * 1：按照创建时间 2：先按照已读未读再按照创建时间排序
         */
        private Integer sortType;
        /**
         * 10:未成交 20:已成交
         */
        private Integer dealFlag;

    }


}
