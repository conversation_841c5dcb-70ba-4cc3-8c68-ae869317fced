package com.pxb7.mall.workorder.infra.repository.db;


import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import com.pxb7.mall.workorder.infra.model.RemindPlanOperateRecordReqPO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.pxb7.mall.workorder.infra.repository.db.mapper.RemindPlanOperateRecordMapper;
import com.pxb7.mall.workorder.infra.repository.db.entity.RemindPlanOperateRecord;

/**
 * 预警计划操作记录表(RemindPlanOperateRecord)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-31 20:36:21
 */
@Slf4j
@Repository
public class RemindPlanOperateRecordDbRepository extends ServiceImpl<RemindPlanOperateRecordMapper, RemindPlanOperateRecord> implements RemindPlanOperateRecordRepository {

    @Override
    public boolean insert(RemindPlanOperateRecordReqPO.AddPO param) {
        RemindPlanOperateRecord entity = new RemindPlanOperateRecord();
        entity.setRemindPlanId(param.getRemindPlanId());
        entity.setOptType(param.getOptType());
        entity.setDataType(param.getDataType());
        entity.setOriginContent(param.getOriginContent());
        entity.setNewContent(param.getNewContent());
        entity.setTraceId(param.getTraceId());
        entity.setOptUserId(param.getOptUserId());
        return this.save(entity);
    }

    @Override
    public boolean update(RemindPlanOperateRecordReqPO.UpdatePO param) {
        LambdaUpdateWrapper<RemindPlanOperateRecord> updateWrapper = new LambdaUpdateWrapper<>();
        //where
        updateWrapper.eq(RemindPlanOperateRecord::getId, param.getId());
        //set
        if (StringUtils.isNotBlank(param.getRemindPlanId())) {
            updateWrapper.set(RemindPlanOperateRecord::getRemindPlanId, param.getRemindPlanId());
        }
        if (Objects.nonNull(param.getOptType())) {
            updateWrapper.set(RemindPlanOperateRecord::getOptType, param.getOptType());
        }
        if (Objects.nonNull(param.getDataType())) {
            updateWrapper.set(RemindPlanOperateRecord::getDataType, param.getDataType());
        }
        if (StringUtils.isNotBlank(param.getOriginContent())) {
            updateWrapper.set(RemindPlanOperateRecord::getOriginContent, param.getOriginContent());
        }
        if (StringUtils.isNotBlank(param.getNewContent())) {
            updateWrapper.set(RemindPlanOperateRecord::getNewContent, param.getNewContent());
        }
        if (StringUtils.isNotBlank(param.getTraceId())) {
            updateWrapper.set(RemindPlanOperateRecord::getTraceId, param.getTraceId());
        }
        if (StringUtils.isNotBlank(param.getOptUserId())) {
            updateWrapper.set(RemindPlanOperateRecord::getOptUserId, param.getOptUserId());
        }
        return this.update(updateWrapper);
    }

    @Override
    public boolean deleteById(RemindPlanOperateRecordReqPO.DelPO param) {
        return this.removeById(param.getId());
    }

    @Override
    public RemindPlanOperateRecord findById(Long id) {
        return this.getById(id);
    }

    @Override
    public List<RemindPlanOperateRecord> list(RemindPlanOperateRecordReqPO.SearchPO param) {
        LambdaQueryWrapper<RemindPlanOperateRecord> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(param.getRemindPlanId())) {
            queryWrapper.eq(RemindPlanOperateRecord::getRemindPlanId, param.getRemindPlanId());
        }
        if (Objects.nonNull(param.getOptType())) {
            queryWrapper.eq(RemindPlanOperateRecord::getOptType, param.getOptType());
        }
        if (Objects.nonNull(param.getDataType())) {
            queryWrapper.eq(RemindPlanOperateRecord::getDataType, param.getDataType());
        }
        if (StringUtils.isNotBlank(param.getOriginContent())) {
            queryWrapper.eq(RemindPlanOperateRecord::getOriginContent, param.getOriginContent());
        }
        if (StringUtils.isNotBlank(param.getNewContent())) {
            queryWrapper.eq(RemindPlanOperateRecord::getNewContent, param.getNewContent());
        }
        if (StringUtils.isNotBlank(param.getTraceId())) {
            queryWrapper.eq(RemindPlanOperateRecord::getTraceId, param.getTraceId());
        }
        if (StringUtils.isNotBlank(param.getOptUserId())) {
            queryWrapper.eq(RemindPlanOperateRecord::getOptUserId, param.getOptUserId());
        }
        return this.list(queryWrapper);
    }

    @Override
    public Page<RemindPlanOperateRecord> page(RemindPlanOperateRecordReqPO.PagePO param) {
        LambdaQueryWrapper<RemindPlanOperateRecord> queryWrapper = new LambdaQueryWrapper<>();
        // 返回分页查询结果
        if (StringUtils.isNotBlank(param.getRemindPlanId())) {
            queryWrapper.eq(RemindPlanOperateRecord::getRemindPlanId, param.getRemindPlanId());
        }
        if (Objects.nonNull(param.getOptType())) {
            queryWrapper.eq(RemindPlanOperateRecord::getOptType, param.getOptType());
        }
        if (Objects.nonNull(param.getDataType())) {
            queryWrapper.eq(RemindPlanOperateRecord::getDataType, param.getDataType());
        }
        if (StringUtils.isNotBlank(param.getOriginContent())) {
            queryWrapper.eq(RemindPlanOperateRecord::getOriginContent, param.getOriginContent());
        }
        if (StringUtils.isNotBlank(param.getNewContent())) {
            queryWrapper.eq(RemindPlanOperateRecord::getNewContent, param.getNewContent());
        }
        if (StringUtils.isNotBlank(param.getTraceId())) {
            queryWrapper.eq(RemindPlanOperateRecord::getTraceId, param.getTraceId());
        }
        if (StringUtils.isNotBlank(param.getOptUserId())) {
            queryWrapper.eq(RemindPlanOperateRecord::getOptUserId, param.getOptUserId());
        }
        return this.page(new Page<>(param.getPageIndex(), param.getPageSize()), queryWrapper);
    }

    @Override
    public boolean saveBatch(List<RemindPlanOperateRecordReqPO.AddPO> params) {
        List<RemindPlanOperateRecord> entities = new ArrayList<>();
        for (RemindPlanOperateRecordReqPO.AddPO param : params){
            RemindPlanOperateRecord entity = new RemindPlanOperateRecord();
            entity.setRemindPlanId(param.getRemindPlanId());
            entity.setOptType(param.getOptType());
            entity.setDataType(param.getDataType());
            entity.setOriginContent(param.getOriginContent());
            entity.setNewContent(param.getNewContent());
            entity.setTraceId(param.getTraceId());
            entity.setOptUserId(param.getOptUserId());
            entities.add(entity);
        }
        return this.saveBatch(entities);
    }
}
