package com.pxb7.mall.workorder.app.mapping;

import com.pxb7.mall.workorder.app.model.*;
import com.pxb7.mall.workorder.domain.model.*;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;


@Mapper
public interface RemindWorkOrderAppMapping {

    RemindWorkOrderAppMapping INSTANCE = Mappers.getMapper(RemindWorkOrderAppMapping.class);


    RemindWorkOrderReqBO.AddBO remindWorkOrderDTO2AddBO(RemindWorkOrderReqDTO.AddDTO source);

    RemindWorkOrderReqBO.UpdateBO remindWorkOrderDTO2UpdateBO(RemindWorkOrderReqDTO.UpdateDTO source);

    RemindWorkOrderReqBO.DelBO remindWorkOrderDTO2DelBO(RemindWorkOrderReqDTO.DelDTO source);

    RemindWorkOrderReqBO.SearchBO remindWorkOrderDTO2SearchBO(RemindWorkOrderReqDTO.SearchDTO source);

    RemindWorkOrderReqBO.PageBO remindWorkOrderDTO2PageBO(RemindWorkOrderReqDTO.PageDTO source);

    RemindWorkOrderRespDTO.DetailDTO remindWorkOrderBO2DetailDTO(RemindWorkOrderRespBO.DetailBO source);

    List<RemindWorkOrderRespDTO.DetailDTO> remindWorkOrderBO2ListDTO(List<RemindWorkOrderRespBO.DetailBO> source);

    Page<WorkOrderPageDetailDTO> remindWorkOrderBO2PageDTO(Page<RemindWorkOrderBO> source);

    WorkOrderStatisticDataSearchBO statisticParam2BO(WorkOrderStatisticDataSearchDTO param);


    WorkOrderStatisticDataDTO statisticResul2DTO(WorkOrderStatisticDataBO source);

    List<WorkOrderStatisticDataDTO> statisticResul2DTOList(List<WorkOrderStatisticDataBO> list);
}


