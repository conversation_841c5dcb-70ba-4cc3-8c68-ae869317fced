package com.pxb7.mall.workorder.domain.model;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 商品工单预警记录
 *
 * <AUTHOR>
 * @since 2025-03-27 20:08:02
 */
@Getter
@Setter
@Accessors(chain = true)
@ToString
public class RemindWorkOrderBO extends BaseRemindRecordBO {

    /**
     * 工单id
     */
    private String workOrderId;
    /**
     * 商品id
     */
    private String productId;
    /**
     * 商品编码
     */
    private String productCode;
    /**
     * 游戏id
     */
    private String gameId;
    /**
     * 工单状态,1:待接单,2:已接单,3:待跟进
     */
    private Integer workOrderStatus;
    /**
     * 接单美工
     */
    private String artDesignerId;
    /**
     * 跟进人
     */
    private String followerId;
    /**
     * 审核客服
     */
    private String auditUserId;


    /**
     * 上架方式
     */
    private Integer onShelfType;

    /**
     * 待跟进流入时间
     */
    private LocalDateTime followedUpInflowTime;

}

