package com.pxb7.mall.workorder.infra.repository.db.entity;

import java.io.Serializable;
import java.util.Date;
import java.time.*;
import java.math.BigDecimal;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import com.baomidou.mybatisplus.annotation.*;

/**
 * 预警计划操作记录表(RemindPlanOperateRecord)实体类
 *
 * <AUTHOR>
 * @since 2025-03-31 20:36:21
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "remind_plan_operate_record")
@ToString
public class RemindPlanOperateRecord implements Serializable {
    private static final long serialVersionUID = 132424255763288841L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 主订单id
     */
    @TableField(value = "remind_plan_id")
    private String remindPlanId;
    /**
     * 操作类型：1新增 2修改 3删除
     */
    @TableField(value = "opt_type")
    private Integer optType;
    /**
     * 数据类型:1预警计划 2游戏预警设置 3提醒规则
     */
    @TableField(value = "data_type")
    private Integer dataType;
    /**
     * 原始内容
     */
    @TableField(value = "origin_content")
    private String originContent;
    /**
     * 新内容
     */
    @TableField(value = "new_content")
    private String newContent;
    /**
     * 请求traceId
     */
    @TableField(value = "trace_id")
    private String traceId;
    /**
     * 操作的用户id
     */
    @TableField(value = "opt_user_id")
    private String optUserId;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;
    /**
     * 逻辑删除，0:删除，1:正常
     */
    @TableLogic
    @TableField(value = "is_deleted")
    private Boolean deleted;


}

