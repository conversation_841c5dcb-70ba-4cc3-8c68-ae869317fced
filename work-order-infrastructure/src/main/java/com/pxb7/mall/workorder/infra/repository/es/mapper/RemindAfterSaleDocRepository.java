package com.pxb7.mall.workorder.infra.repository.es.mapper;

import com.pxb7.mall.workorder.infra.repository.es.entity.RemindAfterSaleDoc;
import org.springframework.data.elasticsearch.repository.ElasticsearchRepository;

import java.io.Serializable;

public interface RemindAfterSaleDocRepository extends ElasticsearchRepository<RemindAfterSaleDoc, Serializable> {

    //SearchPage<RemindWorkOrderDoc> pageQueryWorkOrder(RemindWorkOrderReqPO.PagePO workOrderPagePO);
}
