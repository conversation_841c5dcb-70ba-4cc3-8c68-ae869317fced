package com.pxb7.mall.workorder.infra.model;

import java.util.List;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.ToString;


/**
 * 预警计划游戏配置(RemindPlanGameConfig)实体类
 *
 * <AUTHOR>
 * @since 2025-03-24 21:09:56
 */
public class RemindPlanGameConfigReqPO {

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class AddPO {

        private Long id;

        private String gameId;


        private String gameName;


        private Integer maker;


        private Integer channel;


        private String expectCompleteTimeConfig;


        private String imCountDownTimeConfig;


        private Long remindPlanId;


        private Long remindSubPlanId;


        private String createUserId;


        private String updateUserId;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class UpdatePO {


        private Long id;


        private String gameId;


        private String gameName;


        private Integer maker;


        private Integer channel;


        private String expectCompleteTimeConfig;


        private String imCountDownTimeConfig;


        private Long remindPlanId;


        private Long remindSubPlanId;


        private String createUserId;


        private String updateUserId;


    }


    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class DelPO {
        private Long id;
    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class SearchPO {

        private String gameId;


        private String gameName;


        private Integer maker;


        private Integer channel;


        private String expectCompleteTimeConfig;


        private String imCountDownTimeConfig;


        private Long remindPlanId;

        private List<Long> remindPlanIds;


        private Long  remindSubPlanId;

        private List<Long>  remindSubPlanIds;

        private String createUserId;


        private String updateUserId;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class PagePO {


        private String gameId;


        private String gameName;


        private Integer maker;


        private Integer channel;


        private String expectCompleteTimeConfig;


        private String imCountDownTimeConfig;


        private Long remindPlanId;


        private Long remindSubPlanId;


        private String createUserId;


        private String updateUserId;


        /**
         * 页码，从1开始
         */
        private Integer pageIndex;
        /**
         * 每页数量
         */
        private Integer pageSize;

    }

}

