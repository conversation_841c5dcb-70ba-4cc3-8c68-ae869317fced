package com.pxb7.mall.workorder.app.mapping;


import java.util.List;

import com.pxb7.mall.workorder.app.model.*;
import com.pxb7.mall.workorder.domain.model.*;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;


@Mapper
public interface RemindPlanGameConfigAppMapping {

    RemindPlanGameConfigAppMapping INSTANCE = Mappers.getMapper(RemindPlanGameConfigAppMapping.class);

    RemindPlanGameConfigBO remindPlanGameConfigDTO2BO(PlanGameConfigDTO source);

    RemindPlanGameConfigBO remindPlanGameConfigDTO2BO(AssPlanGameConfigDTO source);

    RemindPlanGameConfigBO remindPlanGameConfigDTO2BO(ComplaintPlanGameConfigDTO source);

    RemindPlanGameConfigBO remindPlanGameConfigDTO2BO(RemindPlanGameConfigReqDTO.AddDTO source);

    RemindPlanGameConfigBO remindPlanGameConfigDTO2UpdateBO(RemindPlanGameConfigReqDTO.UpdateDTO source);

    RemindPlanGameConfigReqBO.DelBO remindPlanGameConfigDTO2DelBO(RemindPlanGameConfigReqDTO.DelDTO source);

    RemindPlanGameConfigReqBO.SearchBO remindPlanGameConfigDTO2SearchBO(RemindPlanGameConfigReqDTO.SearchDTO source);

    RemindPlanGameConfigReqBO.PageBO remindPlanGameConfigDTO2PageBO(RemindPlanGameConfigReqDTO.PageDTO source);

    RemindPlanGameConfigRespDTO.DetailDTO remindPlanGameConfigBO2DetailDTO(RemindPlanGameConfigRespBO.DetailBO source);

    List<RemindPlanGameConfigRespDTO.DetailDTO> remindPlanGameConfigBO2ListDTOV2(List<RemindPlanGameConfigRespBO.DetailBO> source);

    Page<RemindPlanGameConfigRespDTO.DetailDTO> remindPlanGameConfigBO2PageDTO(Page<RemindPlanGameConfigRespBO.DetailBO> source);

    PlanGameConfigDTO remindPlanGameConfigBO2DTO(RemindPlanGameConfigBO source);

    ComplaintPlanGameConfigDTO complaintRemindPlanGameConfigBO2DTO(RemindPlanGameConfigBO source);

    AssPlanGameConfigDTO assRemindPlanGameConfigBO2DTO(RemindPlanGameConfigBO source);

    List<PlanGameConfigDTO> remindPlanGameConfigBO2ListDTO(List<RemindPlanGameConfigBO> source);

    OptionalGameReqBO optionalGameReqDTO2BO(OptionalGameReqDTO source);

    OptionalGameReqBO optionalGameReqDTO2BO(AssOptionalGameReqDTO source);

    OptionalGameRespDTO optionalGameRespBO2DTO(OptionalGameRespBO source);

    List<OptionalGameRespDTO> optionalGameRespBO2ListDTO(List<OptionalGameRespBO> source);

    RemindPlanGameConfigBO remindAssPlanGameConfigDTO2BO(AssPlanGameConfigDTO assPlanGameConfigDTO);

    RemindPlanGameConfigBO remindAssPlanGameConfigDTO2BO(ComplaintPlanGameConfigDTO assPlanGameConfigDTO);

}


