package com.pxb7.mall.workorder.domain.mapping;

import com.pxb7.mall.workorder.domain.model.DeliveryProductStatisticDataBO;
import com.pxb7.mall.workorder.domain.model.RemindDeliveryProductBO;
import com.pxb7.mall.workorder.domain.model.RemindDeliveryProductReqBO;
import com.pxb7.mall.workorder.domain.model.RemindDeliveryProductRespBO;
import com.pxb7.mall.workorder.infra.model.DeliveryStatictisDataPO;
import com.pxb7.mall.workorder.infra.model.RemindDeliveryProductReqPO;
import com.pxb7.mall.workorder.infra.repository.db.entity.RemindDeliveryProduct;
import com.pxb7.mall.workorder.infra.repository.es.entity.RemindDeliveryProductDoc;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;


@Mapper
public interface RemindDeliveryProductDomainMapping {

    RemindDeliveryProductDomainMapping INSTANCE = Mappers.getMapper(RemindDeliveryProductDomainMapping.class);


    RemindDeliveryProductReqPO.AddPO remindDeliveryProductBO2AddPO(RemindDeliveryProductReqBO.AddBO source);

    RemindDeliveryProductReqPO.UpdatePO remindDeliveryProductBO2UpdatePO(RemindDeliveryProductReqBO.UpdateBO source);

    RemindDeliveryProductReqPO.DelPO remindDeliveryProductBO2DelPO(RemindDeliveryProductReqBO.DelBO source);

    RemindDeliveryProductReqPO.SearchPO remindDeliveryProductBO2SearchPO(RemindDeliveryProductReqBO.SearchBO source);

    RemindDeliveryProductReqPO.PagePO remindDeliveryProductBO2PagePO(RemindDeliveryProductReqBO.PageBO source);

    RemindDeliveryProductRespBO.DetailBO remindDeliveryProductPO2DetailBO(RemindDeliveryProduct source);

    List<RemindDeliveryProductRespBO.DetailBO> remindDeliveryProductPO2ListBO(List<RemindDeliveryProduct> source);

    List<RemindDeliveryProductDoc> remindDeliveryProductPO2ListDoc(List<RemindDeliveryProduct> source);

    RemindDeliveryProductDoc remindDeliveryProductPO2Doc(RemindDeliveryProduct source);

    Page<RemindDeliveryProductRespBO.DetailBO> remindDeliveryProductPO2PageBO(Page<RemindDeliveryProduct> source);

    RemindDeliveryProductBO remindDeliveryProductDoc2BO(RemindDeliveryProductDoc source);

    List<RemindDeliveryProductBO> remindDeliveryProductDoc2ListBO(List<RemindDeliveryProductDoc> source);



    @Mapping(target = "date", source = "createDate")
    @Mapping(target = "processTotalCount", source = "deliveryRemindCnt")
    @Mapping(target = "completeCount", source = "deliveryRemindFinishCnt")
    @Mapping(target = "processingCount", source = "deliveryRemindUnfinishedCnt")
    @Mapping(target = "willTimeoutCount", source = "deliveryRemindTimeoutCnt")
    @Mapping(target = "timeoutCount", source = "deliveryRemindTimeoutFinishCnt")
    @Mapping(target = "deliveryCustomerCareId", source = "deliveryCustomerCare")
    DeliveryProductStatisticDataBO deliveryStatictisDataPO2BO(DeliveryStatictisDataPO deliveryStatictisDataPO);


    List<DeliveryProductStatisticDataBO> deliveryStatictisDataPO2ListBO(List<DeliveryStatictisDataPO> deliveryStatictisDataPOS);
}


