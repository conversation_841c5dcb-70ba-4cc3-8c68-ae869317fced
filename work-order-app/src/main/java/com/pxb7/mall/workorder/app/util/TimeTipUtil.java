package com.pxb7.mall.workorder.app.util;

import java.time.Duration;
import java.time.LocalDateTime;

public class TimeTipUtil {

    private final static long DAY_TO_SECOND = 24 * 60 * 60;

    private final static long HOUR_TO_SECOND = 60 * 60;

    private final static long MINUTE_TO_SECOND = 60;

    public static String getDurationTip(LocalDateTime createTime, LocalDateTime completeTime) {

        if (null == createTime || completeTime == null) {
            return null;
        }
        long diffSeconds = Duration.between(createTime, completeTime).toSeconds();
        if (diffSeconds <= 0) {
            return "0分钟";
        }
        long day = diffSeconds / DAY_TO_SECOND;
        long hour = (diffSeconds - day * DAY_TO_SECOND) / HOUR_TO_SECOND;
        long leftSeconds = (diffSeconds - (day * DAY_TO_SECOND) - (hour * HOUR_TO_SECOND));
        //分：需要向上取整
        long minute = (long)Math.ceil((double)leftSeconds / MINUTE_TO_SECOND);
        //向上取整，会出现 59分n秒 取整成 60分钟的问题
        if (minute == 60) {
            minute = 0;
            hour = hour + 1;
        }
        if (hour == 24) {
            hour = 0;
            day = day + 1;
        }
        if (day == 0 && hour == 0 && minute == 0) {
            return "";
        }

        StringBuilder sb = new StringBuilder();
        if (day > 0) {
            sb.append(day).append("天");
        }
        if (hour > 0 || (day > 0 && hour == 0 && minute > 0)) {
            sb.append(hour).append("小时");
        }
        if (minute > 0) {
            sb.append(minute).append("分钟");
        }
        return sb.toString();
    }

    public static String getCountDownTip(LocalDateTime now, LocalDateTime expectCompleteTime) {

        if (null == now || expectCompleteTime == null) {
            return null;
        }

        boolean timeOut = false;
        long diffSeconds = Duration.between(now, expectCompleteTime).toSeconds();
        if (diffSeconds < 0) {
            timeOut = true;
            //取绝对值
            diffSeconds = -diffSeconds;
        }
        long day = diffSeconds / DAY_TO_SECOND;
        long hour = (diffSeconds - day * DAY_TO_SECOND) / HOUR_TO_SECOND;
        long leftSeconds = (diffSeconds - (day * DAY_TO_SECOND) - (hour * HOUR_TO_SECOND));
        //分：需要向上取整
        long minute = (long)Math.ceil((double)leftSeconds / MINUTE_TO_SECOND);
        //向上取整，会出现 59分n秒 取整成 60分钟的问题
        if (minute == 60) {
            minute = 0;
            hour = hour + 1;
        }
        if (hour == 24) {
            hour = 0;
            day = day + 1;
        }
        StringBuilder sb = new StringBuilder();
        /**
         * 注意：此处返回的提示文案 修改前要 和 前端开发沟通下，前端已根据内容做了展示上的处理。
         */
        if (day == 0 && hour == 0 && minute == 0) {
            return "";
        }
        if (timeOut) {
            sb.append("已超时");
        }
        if (day > 0) {
            sb.append(day).append("天");
        }
        if (hour > 0 || (day > 0 && hour == 0 && minute > 0)) {
            sb.append(hour).append("小时");
        }
        if (minute > 0) {
            sb.append(minute).append("分钟");
        }
        if (!timeOut) {
            sb.append("后超时");
        }
        return sb.toString();
    }
}
