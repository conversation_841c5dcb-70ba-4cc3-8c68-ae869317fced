package com.pxb7.mall.workorder.client.constant;

/**
 * 消息模板常量
 * <AUTHOR>
 * @date 2025/4/1 19:53
 */
public class MessageTemplateConstant {

    public static final String DELIVERY_PRODUCT_REMIND_TITLE = "账号交付超时预警";

    public static final String FEISHU_DELIVERY_PRODUCT_REMIND_TEMPLATE = "**游戏名称：**%s\\n**"
            + "商品编号：**%s\\n**"
            + "订单编号：**%s\\n**"
            + "房间ID：**%s\\n**"
            + "交付客服：**%s\\n**"
            + "交付时长：**%s\\n**"
            + "超时预警：**%s\\n**"
            + "预警次数：**第%s次提醒";

    public static final String WORK_ORDER_REMIND_TITLE = "商品工单超时预警";

    public static final String WORK_ORDER_REMIND_TITLE_BACK_END = "【%s】<br/>"
            + "商品工单超时预警";

    public static final String WORK_ORDER_REMIND_TEMPLATE = "游戏名称：%s<br/>"
            + "商品编号：%s<br/>"
            + "工单ID：%s<br/>"
            + "工单状态：%s<br/>"
            + "上架方式：%s<br/>"
            + "跟进人：%s<br/>"
            + "审核客服：%s<br/>"
            + "接单美工：%s<br/>"
            + "工单时长：%s<br/>"
            + "超时预警：%s<br/>"
            + "预警次数：第%s次提醒";

    public static final String FEISHU_WORK_ORDER_REMIND_TEMPLATE = "**游戏名称：**%s\\n**"
            + "商品编号：**%s\\n**"
            + "工单ID：**%s\\n**"
            + "工单状态：**%s\\n**"
            + "上架方式：**%s\\n**"
            + "跟进人：**%s\\n**"
            + "审核客服：**%s\\n**"
            + "接单美工：**%s\\n**"
            + "工单时长：**%s\\n**"
            + "超时预警：**%s\\n**"
            + "预警次数：**第%s次提醒";


    public static final String WORK_ORDER_REMIND_TOAST_TEMPLATE = "你有<font color=\"red\">1</font>单商品工单%s，请及时关注 > <br/>"
            + "即将超时商品工单：<font color=\"red\">%s</font>单<br/>"
            + "已超时商品工单：<font color=\"red\">%s</font>单";

    public static final String AFTER_SALE_RETRIEVE_REMIND_TOAST_TEMPLATE = "你有<font color=\"red\">1</font>单找回工单%s，请及时关注 > <br/>"
            + "即将超时找回工单：<font color=\"red\">%s</font>单<br/>"
            + "已超时找回工单：<font color=\"red\">%s</font>单";

    public static final String COMPLAINT_REMIND_TOAST_TEMPLATE = "你有<font color=\"red\">1</font>单客诉工单%s，请及时关注 > <br/>"
            + "即将超时客诉工单：<font color=\"red\">%s</font>单<br/>"
            + "已超时客诉工单：<font color=\"red\">%s</font>单";

    public static final String AFTER_SALE_RETRIEVE_REMIND_TITLE = "找回工单超时预警";

    public static final String AFTER_SALE_RETRIEVE_REMIND_TITLE_BACK_END = "【%s】<br/>"
            + "找回工单超时预警";

    public static final String AFTER_SALE_RETRIEVE_REMIND_TEMPLATE = "归属游戏：%s<br/>"
            + "工单编号：%s<br/>"
            + "订单编号：%s<br/>"
            + "商品编号：%s<br/>"
            + "找回处理人员：%s<br/>"
            + "商品来源：%s<br/>"
            + "工单时长：%s<br/>"
            + "超时预警：%s<br/>"
            + "预警次数：第%s次提醒";

    public static final String FEISHU_AFTER_SALE_RETRIEVE_REMIND_TEMPLATE = "**归属游戏：**%s\\n**"
            + "工单编号：**%s\\n**"
            + "订单编号：**%s\\n**"
            + "商品编号：**%s\\n**"
            + "找回处理人员：**%s\\n**"
            + "商品来源：**%s\\n**"
            + "工单时长：**%s\\n**"
            + "超时预警：**%s\\n**"
            + "预警次数：**第%s次提醒";

    public static final String AFTER_SALE_DISPUTE_REMIND_TITLE = "纠纷工单超时预警";

    public static final String AFTER_SALE_DISPUTE_REMIND_TITLE_BACK_END = "【%s】<br/>"
            + "纠纷工单超时预警";

    public static final String AFTER_SALE_DISPUTE_REMIND_TEMPLATE = "归属游戏：%s<br/>"
            + "工单编号：%s<br/>"
            + "订单编号：%s<br/>"
            + "商品编号：%s<br/>"
            + "纠纷售后：%s<br/>"
            + "工单时长：%s<br/>"
            + "超时预警：%s<br/>"
            + "预警次数：第%s次提醒";

    public static final String FEISHU_AFTER_SALE_DISPUTE_REMIND_TEMPLATE = "**归属游戏：**%s\\n**"
            + "工单编号：**%s\\n**"
            + "订单编号：**%s\\n**"
            + "商品编号：**%s\\n**"
            + "纠纷售后：**%s\\n**"
            + "工单时长：**%s\\n**"
            + "超时预警：**%s\\n**"
            + "预警次数：**第%s次提醒";


    public static final String COMPLAINT_REMIND_TITLE = "客诉工单超时预警";

    public static final String COMPLAINT_REMIND_TITLE_BACK_END = "【%s】<br/>"
            + "客诉工单超时预警";

    public static final String COMPLAINT_REMIND_TEMPLATE = "工单标题：%s<br/>"
            + "工单编号：%s<br/>"
            + "投诉级别：%s<br/>"
            + "当前处理人：%s<br/>"
            + "投诉渠道：%s<br/>"
            + "投诉与建议内容：%s<br/>"
            + "工单时长：%s<br/>"
            + "超时预警：%s<br/>"
            + "预警次数：第%s次提醒";

    public static final String FEISHU_COMPLAINT_REMIND_TEMPLATE = "**工单标题：**%s\\n**"
            + "工单编号：**%s\\n**"
            + "投诉级别：**%s\\n**"
            + "当前处理人：**%s\\n**"
            + "投诉渠道：**%s\\n**"
            + "投诉与建议内容：**%s\\n**"
            + "工单时长：**%s\\n**"
            + "超时预警：**%s\\n**"
            + "预警次数：**第%s次提醒";

}
