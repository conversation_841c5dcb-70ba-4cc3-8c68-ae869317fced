package com.pxb7.mall.workorder.infra.model;

import java.util.List;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.ToString;


/**
 * 提醒服务预警子计划(RemindSubPlan)实体类
 *
 * <AUTHOR>
 * @since 2025-03-24 21:09:57
 */
public class RemindSubPlanReqPO {

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class AddPO {

        private Long id;

        private Long remindPlanId;

        private Integer businessType;


        private Integer workOrderStatus;


        private Integer onShelfType;

        private Integer membership;

        private Integer workOrderType;

        private Integer complaintLevel;


        private String createUserId;


        private String updateUserId;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class UpdatePO {


        private Long id;

        private Long remindPlanId;

        private Integer businessType;

        private Integer workOrderStatus;


        private Integer onShelfType;

        private Integer membership;

        private Integer workOrderType;

        private Integer complaintLevel;

        private String createUserId;


        private String updateUserId;


    }


    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class DelPO {
        private Long id;
    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class SearchPO {

        private Long id;

        private Long remindPlanId;

        private List<Long> remindPlanIds;

        private Integer businessType;


        private Integer workOrderStatus;


        private Integer onShelfType;

        private Integer membership;

        private Integer workOrderType;

        private Integer complaintLevel;

        private String createUserId;


        private String updateUserId;


        private List<Integer> businessTypes;


        private List<Integer> workOrderStatuses;


        private List<Integer> onShelfTypes;

        private List<Integer> memberships;


    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class PagePO {

        private Long id;

        private Long remindPlanId;

        private Integer businessType;


        private Integer workOrderStatus;


        private Integer onShelfType;

        private Integer membership;

        private Integer workOrderType;

        private Integer complaintLevel;


        private String createUserId;


        private String updateUserId;


        /**
         * 页码，从1开始
         */
        private Integer pageIndex;
        /**
         * 每页数量
         */
        private Integer pageSize;

    }

}

