package com.pxb7.mall.workorder.adapter.schedule;

import com.pxb7.mall.workorder.app.service.RemindPlanRecordAppService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.shardingsphere.infra.hint.HintManager;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import tech.powerjob.worker.core.processor.ProcessResult;
import tech.powerjob.worker.core.processor.TaskContext;
import tech.powerjob.worker.core.processor.sdk.BasicProcessor;

import java.time.LocalDateTime;
import java.util.List;

/**
 *  预警计划执行任务
 * <AUTHOR>
 * @date 2025/3/28 20:05
 */

@Component
@Slf4j
public class RemindExecuteTask implements BasicProcessor {

    /**
     *  分库数据库实例
     */
    @Value("${remind.service.datasources}")
    private String datasourceStr;

    @Resource
    private RemindPlanRecordAppService planRecordAppService;

    @Override
    public ProcessResult process(TaskContext taskContext) throws Exception {
        log.info("执行预警计划任务:{}", LocalDateTime.now());
        List<String> databaseInstances = List.of(datasourceStr.split(","));
        //扫描所有分库中符合条件的提醒计划记录
        for (String databaseInstance : databaseInstances) {
            processSharding(databaseInstance);
        }

        return new ProcessResult(true, "执行预警计划任务成功！");
    }

    /**
     * 处理分片任务
     * @param databaseName
     */
    private void processSharding(String databaseName) {
        log.info("start to execute remind task, databaseName:{}", databaseName);
        try (HintManager hintManager = HintManager.getInstance()) {
            // 指定数据源(Sharding-JDBC 会路由到指定的数据源)
            hintManager.setDataSourceName(databaseName);
            planRecordAppService.executeRemindTask(databaseName);
        }
        log.info("end to execute remind task, databaseName:{}", databaseName);
    }

}
