package com.pxb7.mall.workorder.infra.repository.gateway.dubbo.ass;

import com.alibaba.cola.dto.SingleResponse;
import com.pxb7.mall.trade.ass.client.api.AfcWorkOrderServiceI;
import com.pxb7.mall.trade.ass.client.dto.response.afc.ComplaintWORespDTO;
import com.pxb7.mall.trade.ass.client.dto.response.afc.DisputeWORespDTO;
import com.pxb7.mall.trade.ass.client.dto.response.afc.RetrieveWORespDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Repository;

@Slf4j
@Repository
public class AfcWorkOrderGateway {

    @DubboReference
    private AfcWorkOrderServiceI afcWorkOrderServiceI;


    /**
     * 根据工单ID查询找回工单信息
     * @param workOrderId
     * @return
     */
    public RetrieveWORespDTO getRetrieveWorkOrderInfo(String workOrderId) {
        try {
            SingleResponse<RetrieveWORespDTO> response = afcWorkOrderServiceI.searchRetrieveWOById(workOrderId);
            if (response == null || !response.isSuccess()) {
                log.error("Failed to retrieve data from the AfcWorkOrderServiceI#searchRetrieveWOById,workOrderId:{}",workOrderId);
                return null;
            }
            return response.getData();
        } catch (Exception e) {
            log.error("An exception occurred while calling AfcWorkOrderServiceI#searchRetrieveWOById.workOrderId:{}",workOrderId, e);
            return null;
        }
    }


    /**
     * 根据工单ID查询纠纷工单信息
     * @param workOrderId
     * @return
     */
    public DisputeWORespDTO getDisputeWorkOrderInfo(String workOrderId) {
        try {
            SingleResponse<DisputeWORespDTO> response = afcWorkOrderServiceI.searchDisputeWOById(workOrderId);
            if (response == null || !response.isSuccess()) {
                log.error("Failed to retrieve data from the AfcWorkOrderServiceI#searchDisputeWOById,workOrderId:{}",workOrderId);
                return null;
            }
            return response.getData();
        } catch (Exception e) {
            log.error("An exception occurred while calling AfcWorkOrderServiceI#searchDisputeWOById.workOrderId:{}",workOrderId, e);
            return null;
        }
    }


    /**
     * 根据工单ID查询投诉工单信息
     * @param workOrderId
     * @return
     */
    public ComplaintWORespDTO getComplaintWorkOrderInfo(String workOrderId) {
        try {
            SingleResponse<ComplaintWORespDTO> response = afcWorkOrderServiceI.searchComplaintWOById(workOrderId);
            if (response == null || !response.isSuccess()) {
                log.error("Failed to retrieve data from the AfcWorkOrderServiceI#searchComplaintWOById,workOrderId:{}",workOrderId);
                return null;
            }
            return response.getData();
        } catch (Exception e) {
            log.error("An exception occurred while calling AfcWorkOrderServiceI#searchComplaintWOById.workOrderId:{}",workOrderId, e);
            return null;
        }
    }

}
