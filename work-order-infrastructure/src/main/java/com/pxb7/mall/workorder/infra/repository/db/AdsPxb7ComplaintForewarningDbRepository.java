package com.pxb7.mall.workorder.infra.repository.db;

import java.util.List;
import java.util.Objects;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.pxb7.mall.workorder.infra.model.AdsPxb7ComplaintForewarningReqPO;
import com.pxb7.mall.workorder.infra.repository.db.mapper.AdsPxb7ComplaintForewarningMapper;
import com.pxb7.mall.workorder.infra.repository.db.entity.AdsPxb7ComplaintForewarning;

/**
 * 客诉预警统计(AdsPxb7ComplaintForewarning)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-08 18:15:03
 */
@Slf4j
@Repository
public class AdsPxb7ComplaintForewarningDbRepository
    extends ServiceImpl<AdsPxb7ComplaintForewarningMapper, AdsPxb7ComplaintForewarning>
    implements AdsPxb7ComplaintForewarningRepository {

    @Override
    public boolean insert(AdsPxb7ComplaintForewarningReqPO.AddPO param) {
        AdsPxb7ComplaintForewarning entity = new AdsPxb7ComplaintForewarning();
        entity.setCreateDate(param.getCreateDate());
        entity.setChannel(param.getChannel());
        entity.setHandleUserId(param.getHandleUserId());
        entity.setComplaintLevel(param.getComplaintLevel());
        entity.setComplaintWorkOrderCnt(param.getComplaintWorkOrderCnt());
        entity.setComplaintCompleteWorkOrderCnt(param.getComplaintCompleteWorkOrderCnt());
        entity.setComplaintProcessingWorkOrderCnt(param.getComplaintProcessingWorkOrderCnt());
        entity.setComplaintTimeoutingWorkOrderCnt(param.getComplaintTimeoutingWorkOrderCnt());
        entity.setComplaintTimeoutedWorkOrderCnt(param.getComplaintTimeoutedWorkOrderCnt());
        entity.setCreateUserId(param.getCreateUserId());
        entity.setUpdateUserId(param.getUpdateUserId());
        return this.save(entity);
    }

    @Override
    public boolean update(AdsPxb7ComplaintForewarningReqPO.UpdatePO param) {
        LambdaUpdateWrapper<AdsPxb7ComplaintForewarning> updateWrapper = new LambdaUpdateWrapper<>();
        // where
        updateWrapper.eq(AdsPxb7ComplaintForewarning::getId, param.getId());
        // set
        if (StringUtils.isNotBlank(param.getCreateDate())) {
            updateWrapper.set(AdsPxb7ComplaintForewarning::getCreateDate, param.getCreateDate());
        }
        if (Objects.nonNull(param.getChannel())) {
            updateWrapper.set(AdsPxb7ComplaintForewarning::getChannel, param.getChannel());
        }
        if (StringUtils.isNotBlank(param.getHandleUserId())) {
            updateWrapper.set(AdsPxb7ComplaintForewarning::getHandleUserId, param.getHandleUserId());
        }
        if (Objects.nonNull(param.getComplaintLevel())) {
            updateWrapper.set(AdsPxb7ComplaintForewarning::getComplaintLevel, param.getComplaintLevel());
        }
        if (Objects.nonNull(param.getComplaintWorkOrderCnt())) {
            updateWrapper.set(AdsPxb7ComplaintForewarning::getComplaintWorkOrderCnt, param.getComplaintWorkOrderCnt());
        }
        if (Objects.nonNull(param.getComplaintCompleteWorkOrderCnt())) {
            updateWrapper.set(AdsPxb7ComplaintForewarning::getComplaintCompleteWorkOrderCnt,
                param.getComplaintCompleteWorkOrderCnt());
        }
        if (Objects.nonNull(param.getComplaintProcessingWorkOrderCnt())) {
            updateWrapper.set(AdsPxb7ComplaintForewarning::getComplaintProcessingWorkOrderCnt,
                param.getComplaintProcessingWorkOrderCnt());
        }
        if (Objects.nonNull(param.getComplaintTimeoutingWorkOrderCnt())) {
            updateWrapper.set(AdsPxb7ComplaintForewarning::getComplaintTimeoutingWorkOrderCnt,
                param.getComplaintTimeoutingWorkOrderCnt());
        }
        if (Objects.nonNull(param.getComplaintTimeoutedWorkOrderCnt())) {
            updateWrapper.set(AdsPxb7ComplaintForewarning::getComplaintTimeoutedWorkOrderCnt,
                param.getComplaintTimeoutedWorkOrderCnt());
        }
        if (StringUtils.isNotBlank(param.getCreateUserId())) {
            updateWrapper.set(AdsPxb7ComplaintForewarning::getCreateUserId, param.getCreateUserId());
        }
        if (StringUtils.isNotBlank(param.getUpdateUserId())) {
            updateWrapper.set(AdsPxb7ComplaintForewarning::getUpdateUserId, param.getUpdateUserId());
        }
        return this.update(updateWrapper);
    }

    @Override
    public boolean deleteById(AdsPxb7ComplaintForewarningReqPO.DelPO param) {
        return this.removeById(param.getId());
    }

    @Override
    public AdsPxb7ComplaintForewarning findById(Long id) {
        return this.getById(id);
    }

    @Override
    public List<AdsPxb7ComplaintForewarning> list(AdsPxb7ComplaintForewarningReqPO.SearchPO param) {
        LambdaQueryWrapper<AdsPxb7ComplaintForewarning> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(param.getCreateDate())) {
            queryWrapper.eq(AdsPxb7ComplaintForewarning::getCreateDate, param.getCreateDate());
        }
        if (Objects.nonNull(param.getChannel())) {
            queryWrapper.eq(AdsPxb7ComplaintForewarning::getChannel, param.getChannel());
        }
        if (StringUtils.isNotBlank(param.getHandleUserId())) {
            queryWrapper.eq(AdsPxb7ComplaintForewarning::getHandleUserId, param.getHandleUserId());
        }
        if (Objects.nonNull(param.getComplaintLevel())) {
            queryWrapper.eq(AdsPxb7ComplaintForewarning::getComplaintLevel, param.getComplaintLevel());
        }
        if (Objects.nonNull(param.getComplaintWorkOrderCnt())) {
            queryWrapper.eq(AdsPxb7ComplaintForewarning::getComplaintWorkOrderCnt, param.getComplaintWorkOrderCnt());
        }
        if (Objects.nonNull(param.getComplaintCompleteWorkOrderCnt())) {
            queryWrapper.eq(AdsPxb7ComplaintForewarning::getComplaintCompleteWorkOrderCnt,
                param.getComplaintCompleteWorkOrderCnt());
        }
        if (Objects.nonNull(param.getComplaintProcessingWorkOrderCnt())) {
            queryWrapper.eq(AdsPxb7ComplaintForewarning::getComplaintProcessingWorkOrderCnt,
                param.getComplaintProcessingWorkOrderCnt());
        }
        if (Objects.nonNull(param.getComplaintTimeoutingWorkOrderCnt())) {
            queryWrapper.eq(AdsPxb7ComplaintForewarning::getComplaintTimeoutingWorkOrderCnt,
                param.getComplaintTimeoutingWorkOrderCnt());
        }
        if (Objects.nonNull(param.getComplaintTimeoutedWorkOrderCnt())) {
            queryWrapper.eq(AdsPxb7ComplaintForewarning::getComplaintTimeoutedWorkOrderCnt,
                param.getComplaintTimeoutedWorkOrderCnt());
        }
        if (StringUtils.isNotBlank(param.getCreateUserId())) {
            queryWrapper.eq(AdsPxb7ComplaintForewarning::getCreateUserId, param.getCreateUserId());
        }
        if (StringUtils.isNotBlank(param.getUpdateUserId())) {
            queryWrapper.eq(AdsPxb7ComplaintForewarning::getUpdateUserId, param.getUpdateUserId());
        }
        return this.list(queryWrapper);
    }

    @Override
    public Page<AdsPxb7ComplaintForewarning> page(AdsPxb7ComplaintForewarningReqPO.PagePO param) {
        LambdaQueryWrapper<AdsPxb7ComplaintForewarning> queryWrapper = new LambdaQueryWrapper<>();
        // 返回分页查询结果
        if (StringUtils.isNotBlank(param.getCreateDate())) {
            queryWrapper.eq(AdsPxb7ComplaintForewarning::getCreateDate, param.getCreateDate());
        }
        if (Objects.nonNull(param.getChannel())) {
            queryWrapper.eq(AdsPxb7ComplaintForewarning::getChannel, param.getChannel());
        }
        if (StringUtils.isNotBlank(param.getHandleUserId())) {
            queryWrapper.eq(AdsPxb7ComplaintForewarning::getHandleUserId, param.getHandleUserId());
        }
        if (Objects.nonNull(param.getComplaintLevel())) {
            queryWrapper.eq(AdsPxb7ComplaintForewarning::getComplaintLevel, param.getComplaintLevel());
        }
        if (Objects.nonNull(param.getComplaintWorkOrderCnt())) {
            queryWrapper.eq(AdsPxb7ComplaintForewarning::getComplaintWorkOrderCnt, param.getComplaintWorkOrderCnt());
        }
        if (Objects.nonNull(param.getComplaintCompleteWorkOrderCnt())) {
            queryWrapper.eq(AdsPxb7ComplaintForewarning::getComplaintCompleteWorkOrderCnt,
                param.getComplaintCompleteWorkOrderCnt());
        }
        if (Objects.nonNull(param.getComplaintProcessingWorkOrderCnt())) {
            queryWrapper.eq(AdsPxb7ComplaintForewarning::getComplaintProcessingWorkOrderCnt,
                param.getComplaintProcessingWorkOrderCnt());
        }
        if (Objects.nonNull(param.getComplaintTimeoutingWorkOrderCnt())) {
            queryWrapper.eq(AdsPxb7ComplaintForewarning::getComplaintTimeoutingWorkOrderCnt,
                param.getComplaintTimeoutingWorkOrderCnt());
        }
        if (Objects.nonNull(param.getComplaintTimeoutedWorkOrderCnt())) {
            queryWrapper.eq(AdsPxb7ComplaintForewarning::getComplaintTimeoutedWorkOrderCnt,
                param.getComplaintTimeoutedWorkOrderCnt());
        }
        if (StringUtils.isNotBlank(param.getCreateUserId())) {
            queryWrapper.eq(AdsPxb7ComplaintForewarning::getCreateUserId, param.getCreateUserId());
        }
        if (StringUtils.isNotBlank(param.getUpdateUserId())) {
            queryWrapper.eq(AdsPxb7ComplaintForewarning::getUpdateUserId, param.getUpdateUserId());
        }
        return this.page(new Page<>(param.getPageIndex(), param.getPageSize()), queryWrapper);
    }
}
