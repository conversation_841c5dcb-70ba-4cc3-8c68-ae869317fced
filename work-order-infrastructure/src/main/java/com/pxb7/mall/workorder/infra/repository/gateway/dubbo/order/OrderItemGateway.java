package com.pxb7.mall.workorder.infra.repository.gateway.dubbo.order;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.cola.dto.MultiResponse;
import com.google.common.collect.Maps;
import com.pxb7.mall.trade.order.client.api.OrderItemDubboServiceI;
import com.pxb7.mall.trade.order.client.dto.response.order.dubbo.NonCanceledOrderItemRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version : OrderItemGateway.java, v 0.1 2025年04月18日 10:02 yang.xuexi Exp $
 */
@Repository
@Slf4j
public class OrderItemGateway {

    @DubboReference
    private OrderItemDubboServiceI orderItemDubboServiceI;

    /**
     * 查询是否成交
     * @return
     */
    public Map<String, Boolean> batchQueryUserDealByToday(Set<String> userIdSet) {
        if (CollUtil.isEmpty(userIdSet)) {
            log.warn("userIdSet is empty");
            return Maps.newHashMap();
        }
        log.info("batchQueryUserDealByToday {}", userIdSet);
        try {
            MultiResponse<NonCanceledOrderItemRespDTO> itemRespDTOMultiResponse = orderItemDubboServiceI.checkUserHasNonCancelledOrder(new ArrayList<>(userIdSet));
            if (Objects.nonNull(itemRespDTOMultiResponse) && CollUtil.isNotEmpty(itemRespDTOMultiResponse.getData())) {
                Map<String, Boolean> result = itemRespDTOMultiResponse.getData()
                        .stream()
                        .collect(Collectors.toMap(NonCanceledOrderItemRespDTO::getUserId, NonCanceledOrderItemRespDTO::getHasNonCanceledOrderItem, (a, b) -> a));
                log.info("batchQueryUserDealByToday result {}", result);
                return result;
            }
        } catch (Exception e) {
            log.error("orderItemDubboServiceI.checkUserHasNonCancelledOrder fail{}", e);
        }
        return Maps.newHashMap();
    }


}
