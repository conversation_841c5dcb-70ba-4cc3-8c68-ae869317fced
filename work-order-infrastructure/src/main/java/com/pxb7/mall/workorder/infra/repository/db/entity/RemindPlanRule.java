package com.pxb7.mall.workorder.infra.repository.db.entity;

import java.io.Serializable;
import java.util.Date;
import java.time.*;
import java.math.BigDecimal;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import com.baomidou.mybatisplus.annotation.*;

/**
 * 预警计划提醒规则配置(RemindPlanRule)实体类
 *
 * <AUTHOR>
 * @since 2025-03-24 21:09:56
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "remind_plan_rule")
@ToString
public class RemindPlanRule implements Serializable {
    private static final long serialVersionUID = 455557795258134016L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 节点序号
     */
    @TableField(value = "node_number")
    private Integer nodeNumber;
    /**
     * 提醒时间点,{"hours":"20","minutes":"30"}
     */
    @TableField(value = "remind_time_config")
    private String remindTimeConfig;
    /**
     * 提醒方式配置,[{"method":"feishu","objects":[{"object":"customercare"},{"object":"group","webhook","111"}]},{"method":"im","objects":[{"object":"customercare"}]}]
     */
    @TableField(value = "remind_method_config")
    private String remindMethodConfig;
    /**
     * 预警计划id,remind_plan
     */
    @TableField(value = "remind_plan_id")
    private Long remindPlanId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;
    /**
     * 创建人
     */
    @TableField(value = "create_user_id")
    private String createUserId;
    /**
     * 更新人
     */
    @TableField(value = "update_user_id")
    private String updateUserId;
    /**
     * 是否删除 0:否, 1:是
     */
    @TableLogic
    @TableField(value = "is_deleted")
    private Boolean deleted;


}

