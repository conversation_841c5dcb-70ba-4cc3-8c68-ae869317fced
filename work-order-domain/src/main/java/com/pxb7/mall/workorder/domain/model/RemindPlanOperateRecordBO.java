package com.pxb7.mall.workorder.domain.model;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@ToString
public class RemindPlanOperateRecordBO {


    private Long remindPlanId;


    private Integer optType;


    private Integer dataType;


    private String originContent;


    private String newContent;


    private String traceId;


    private String optUserId;
}
