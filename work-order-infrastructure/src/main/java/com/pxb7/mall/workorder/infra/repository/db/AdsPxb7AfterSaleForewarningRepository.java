package com.pxb7.mall.workorder.infra.repository.db;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pxb7.mall.workorder.infra.model.AdsPxb7AfterSaleForewarningReqPO;
import com.pxb7.mall.workorder.infra.repository.db.entity.AdsPxb7AfterSaleForewarning;

/**
 * 售后找回纠纷预警统计(AdsPxb7AfterSaleForewarning)表服务接口
 *
 * <AUTHOR>
 * @since 2025-05-08 18:13:53
 */
public interface AdsPxb7AfterSaleForewarningRepository extends IService<AdsPxb7AfterSaleForewarning> {

    boolean insert(AdsPxb7AfterSaleForewarningReqPO.AddPO param);

    boolean update(AdsPxb7AfterSaleForewarningReqPO.UpdatePO param);

    boolean deleteById(AdsPxb7AfterSaleForewarningReqPO.DelPO param);

    AdsPxb7AfterSaleForewarning findById(Long id);

    List<AdsPxb7AfterSaleForewarning> list(AdsPxb7AfterSaleForewarningReqPO.SearchPO param);

    Page<AdsPxb7AfterSaleForewarning> page(AdsPxb7AfterSaleForewarningReqPO.PagePO param);

}
