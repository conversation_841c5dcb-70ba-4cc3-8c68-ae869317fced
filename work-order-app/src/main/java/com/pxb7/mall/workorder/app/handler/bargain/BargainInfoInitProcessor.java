package com.pxb7.mall.workorder.app.handler.bargain;

import com.pxb7.mall.user.dto.response.user.UserShortInfoDTO;
import com.pxb7.mall.workorder.app.mapping.BargainTicketAppMapping;
import com.pxb7.mall.workorder.client.enums.BargainTicketReadFlagEnum;
import com.pxb7.mall.workorder.client.enums.BargainTicketTradeStatusFlagEnum;
import com.pxb7.mall.workorder.domain.message.BargainTicketMessage;
import com.pxb7.mall.workorder.domain.model.BargainTicketCacheBO;
import com.pxb7.mall.workorder.infra.repository.db.entity.BargainAcceptanceCustomer;
import com.pxb7.mall.workorder.infra.repository.es.entity.BargainTicketDoc;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version : BargainInfoChangeProcessor.java, v 0.1 2025年04月21日 16:26 yang.xuexi Exp $
 */
@Component("BARGAIN_INFO_INIT")
@Slf4j
public class BargainInfoInitProcessor extends AbstractBargainTicketSyncProcessor {

    @Override
    protected void doProcess(BargainTicketMessage message, BargainAcceptanceCustomer byReceiveId) {
        //查询es工单
        BargainTicketDoc originTicket = bargainTicketDocEsRepository.findByReceiveId(message.getReceiveId());
        //根据买家id查询买家信息
        Optional<UserShortInfoDTO> userInfoOptional = bargainTicketDomainService.getUserInfo(byReceiveId.getBuyerUserId());
        boolean dealSuccess = bargainTicketDomainService.checkUserIfDealToday(byReceiveId.getBuyerUserId());
        if (Objects.isNull(originTicket)) {
            //直接用当前事件消息初始化
            BargainTicketDoc doc = BargainTicketAppMapping.INSTANCE.convertEventMessageToInitDoc(message, userInfoOptional, byReceiveId, dealSuccess);
            bargainTicketDocRepository.save(doc);
            bargainTicketDomainService.addUserCache(byReceiveId.getBuyerUserId(), doc.getReceiveId());
            return;
        }

        //议价单变更
        BargainTicketDoc updateDoc = BargainTicketAppMapping.INSTANCE.convertEventMessageToUpdateDoc(message, userInfoOptional, byReceiveId, dealSuccess, originTicket);
        updateDoc.setReadFlag(BargainTicketReadFlagEnum.UN_READ.getCode());
        bargainTicketDocRepository.save(updateDoc);
        bargainTicketDomainService.addUserCache(byReceiveId.getBuyerUserId(), updateDoc.getReceiveId());
    }
}
