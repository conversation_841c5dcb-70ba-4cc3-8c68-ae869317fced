package com.pxb7.mall.workorder.app.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
@Accessors(chain = true)
@ToString
public class WorkOrderStatisticDataSearchDTO {

    /**
     * 起始时间 精确到天
     */
    //@JsonFormat(pattern = "yyyy-MM-dd")
    //@NotNull(message = "起始时间不能为空")
    private String startDate;

    /**
     * 结束时间 精确到天
     */
    //@JsonFormat(pattern = "yyyy-MM-dd")
    //@NotNull(message = "结束时间不能为空")
    private String endDate;



    /**
     * 工单状态,1:待接单,2:已接单,3:待跟进
     */
    @NotNull(message = "工单状态不能为空")
    private Integer workOrderStatus;

    /**
     * 游戏id 列表
     */
    private List<String> gameIds;


    /**
     * 上架方式：1:官方截图，2:自主截图，
     */
    private String onShelfType;

    /**
     * 审核客服id列表
     */
    private List<String> customerCareIds;



    /**
     * 接单美工Id 列表
     */
    private List<String> artDesignerIds;

    /**
     * 跟进人Id 列表
     */
    private List<String> followerIds;
}
