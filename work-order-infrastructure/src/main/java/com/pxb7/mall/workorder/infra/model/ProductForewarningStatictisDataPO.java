package com.pxb7.mall.workorder.infra.model;

import lombok.Data;

@Data
public class ProductForewarningStatictisDataPO {

    private String createDate;

    /**
     * 工单状态,1:待接单,2:已接单,3:待跟进
     */
    private Integer workOrderStatus;

    /**
     * 游戏id 列表
     */
    private String gameId;

    /**
     * 上架方式：1:官方截图，2:自主截图，
     */
    private Integer onShelfType;

    /**
     * 审核客服id
     */
    private String auditUserId;

    /**
     * 接单美工Id
     */
    private String artDesignerId;

    /**
     * 跟进人Id
     */
    private String followerId;

    /**
     * 已接单已完结数
     */
    private Long acceptedCompletedWorkOrderCnt;
    /**
     * 已接单已失效数
     */
    private Long acceptedTerminatedWorkOrderCnt;
    /**
     * 已接单已超时数
     */
    private Long acceptedTimeoutedWorkOrderCnt;
    /**
     * 已接单即将超时数
     */
    private Long acceptedTimeoutingWorkOrderCnt;
    /**
     * 已接单数
     */
    private Long acceptedWorkOrderCnt;

    /**
     * 待跟进已完结数
     */
    private Long followedCompletedWorkOrderCnt;
    /**
     * 待跟进已失效数
     */
    private Long followedTerminatedWorkOrderCnt;
    /**
     * 待跟进已超时数
     */
    private Long followedTimeoutedWorkOrderCnt;
    /**
     * 待跟进即将超时数
     */
    private Long followedTimeoutingWorkOrderCnt;
    /**
     * 待跟进数
     */
    private Long followedWorkOrderCnt;

    /**
     * 待接单已完结数
     */
    private Long waitCompletedWorkOrderCnt;
    /**
     * 待接单已超时数
     */
    private Long waitTimeoutedWorkOrderCnt;
    /**
     * 待接单即将超时数
     */
    private Long waitTimeoutingWorkOrderCnt;
    /**
     * 待接单数
     */
    private Long waitWorkOrderCnt;
}
