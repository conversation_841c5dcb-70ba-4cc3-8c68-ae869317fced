package com.pxb7.mall.workorder.infra.mapping;

import com.pxb7.mall.user.admin.dto.response.sysuser.GetSysUserRespDTO;
import com.pxb7.mall.workorder.infra.model.SysUserRespPO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface SysUserGateWayMapping {

    SysUserGateWayMapping INSTANCE = Mappers.getMapper(SysUserGateWayMapping.class);


    SysUserRespPO dto2PO(GetSysUserRespDTO source);


    List<SysUserRespPO> dto2POList(List<GetSysUserRespDTO> source);



}
