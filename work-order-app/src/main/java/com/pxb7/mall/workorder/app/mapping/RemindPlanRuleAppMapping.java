package com.pxb7.mall.workorder.app.mapping;

import com.pxb7.mall.workorder.app.model.PlanRuleDTO;
import com.pxb7.mall.workorder.app.model.RemindPlanRuleReqDTO;
import com.pxb7.mall.workorder.app.model.RemindPlanRuleRespDTO;
import com.pxb7.mall.workorder.domain.model.RemindPlanRuleBO;
import com.pxb7.mall.workorder.domain.model.RemindPlanRuleReqBO;
import com.pxb7.mall.workorder.domain.model.RemindPlanRuleRespBO;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;


@Mapper
public interface RemindPlanRuleAppMapping {

    RemindPlanRuleAppMapping INSTANCE = Mappers.getMapper(RemindPlanRuleAppMapping.class);

    RemindPlanRuleBO remindPlanRuleDTO2BO(PlanRuleDTO source);

    RemindPlanRuleBO remindPlanRuleDTO2AddBO(RemindPlanRuleReqDTO.AddDTO source);

    RemindPlanRuleBO remindPlanRuleDTO2UpdateBO(RemindPlanRuleReqDTO.UpdateDTO source);

    RemindPlanRuleReqBO.DelBO remindPlanRuleDTO2DelBO(RemindPlanRuleReqDTO.DelDTO source);

    RemindPlanRuleReqBO.SearchBO remindPlanRuleDTO2SearchBO(RemindPlanRuleReqDTO.SearchDTO source);


    RemindPlanRuleRespDTO.DetailDTO remindPlanRuleBO2DetailDTO(RemindPlanRuleRespBO.DetailBO source);

    List<RemindPlanRuleRespDTO.DetailDTO> remindPlanRuleBO2ListDTOV2(List<RemindPlanRuleRespBO.DetailBO> source);

    Page<RemindPlanRuleRespDTO.DetailDTO> remindPlanRuleBO2PageDTO(Page<RemindPlanRuleRespBO.DetailBO> source);




    List<PlanRuleDTO> remindPlanRuleBO2ListDTO(List<RemindPlanRuleBO> source);

}


