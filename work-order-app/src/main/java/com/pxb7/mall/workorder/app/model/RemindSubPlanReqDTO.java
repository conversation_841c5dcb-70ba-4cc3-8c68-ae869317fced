package com.pxb7.mall.workorder.app.model;

import java.util.Date;
import java.time.*;
import java.math.BigDecimal;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Min;
import lombok.ToString;


/**
 * 提醒服务预警子计划(RemindSubPlan)实体类
 *
 * <AUTHOR>
 * @since 2025-03-24 21:12:18
 */
public class RemindSubPlanReqDTO {

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class AddDTO {


        @NotNull(message = "businessType不能为空")
        private Integer businessType;


        @NotNull(message = "workOrderStatus不能为空")
        private Integer workOrderStatus;


        @NotNull(message = "onShelfType不能为空")
        private Integer onShelfType;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class UpdateDTO {


        @NotNull(message = "id不能为空")
        private Long id;


        @NotNull(message = "businessType不能为空")
        private Integer businessType;


        @NotNull(message = "workOrderStatus不能为空")
        private Integer workOrderStatus;


        @NotNull(message = "onShelfType不能为空")
        private Integer onShelfType;


    }


    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class DelDTO {
        @NotNull(message = "id不能为空")
        private Long id;
    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class SearchDTO {

        @NotNull(message = "businessType不能为空")
        private Integer businessType;


        @NotNull(message = "workOrderStatus不能为空")
        private Integer workOrderStatus;


        @NotNull(message = "onShelfType不能为空")
        private Integer onShelfType;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class PageDTO {


        @NotNull(message = "businessType不能为空")
        private Integer businessType;


        @NotNull(message = "workOrderStatus不能为空")
        private Integer workOrderStatus;


        @NotNull(message = "onShelfType不能为空")
        private Integer onShelfType;


        /**
         * 页码，从1开始
         */
        @NotNull(message = "pageIndex不能为空")
        @Min(value = 1, message = "pageIndex不能小于1")
        private Integer pageIndex;
        /**
         * 每页数量
         */
        @NotNull(message = "pageSize不能为空")
        private Integer pageSize;

    }

}

