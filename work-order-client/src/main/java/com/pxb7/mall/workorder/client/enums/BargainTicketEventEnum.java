package com.pxb7.mall.workorder.client.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * 0:还价单创建 1:还价单更新 2:工单状态更新
 *
 * <AUTHOR>
 * @version : BaigainTicketEventEnum.java, v 0.1 2025年04月21日 15:54 yang.xuexi Exp $
 */

public enum BargainTicketEventEnum {

    BARGAIN_INFO_INIT(0, "议价单变更"),

    BARGAIN_INFO_CHANGE(1, "议价单变更"),
    BARGAIN_TICKET_STATUS_CHANGE(2, "议价工单状态变更"),
    /**
     * 已读未读状态变更
     */
    BARGAIN_TICKET_READ_STATUS_CHANGE(3, "工单已读未读状态变更"),

    BARGAIN_TICKET_ORDER_STATUS_CHANGE(4, "当日是否成交"),
    ;


    BargainTicketEventEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Getter
    private Integer code;

    @Getter
    private String desc;


    public static BargainTicketEventEnum of(Integer code) {
        return Arrays.stream(values()).filter(e -> Objects.equals(e.getCode(), code)).findFirst().orElse(null);
    }
}
