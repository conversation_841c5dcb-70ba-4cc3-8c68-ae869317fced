package com.pxb7.mall.workorder.adapter.consumer.bargain;

import com.alibaba.fastjson.JSON;
import com.pxb7.mall.components.rocketmq.RocketMQListenerExt;
import com.pxb7.mall.workorder.app.service.BargainTicketAppService;
import com.pxb7.mall.workorder.domain.message.BargainTicketMessage;
import com.pxb7.mall.workorder.infra.constant.RMQConstant;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.annotation.RocketMQMessageListener;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.message.MessageId;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.springframework.stereotype.Service;

/**
 * 还价工单变更消息
 */
@Slf4j
@Service
@RocketMQMessageListener(
        consumerGroup = RMQConstant.BARGAIN_TICKET_GROUP,
        topic = RMQConstant.BARGAIN_ACCEPTANCE_CUSTOMER_CHANGE,
        tag = RMQConstant.ALL_TAG)
public class BargainTicketChangeConsumer implements RocketMQListenerExt<BargainTicketMessage> {
    @Resource
    private BargainTicketAppService bargainTicketAppService;
    @Override
    public ConsumeResult consume(MessageView messageView, BargainTicketMessage message) {
        MessageId messageId = messageView.getMessageId();
        String receiveId = message.getReceiveId();
        String buyerUserId = message.getBuyerUserId();
        log.info("监听到还价工单状态变更消息: messageId = {},message = {}", messageId, JSON.toJSONString(message));
        try {
            bargainTicketAppService.syncBargainTicket(message);
            return ConsumeResult.SUCCESS;
        } catch (Exception e) {
            log.warn("error in consumed from promotion_acceptance_customer_change: buyerUserId = {} receiveId {}", buyerUserId, receiveId, e);
            return ConsumeResult.SUCCESS;
        }
    }

}

