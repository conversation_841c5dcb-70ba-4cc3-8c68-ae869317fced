package com.pxb7.mall.workorder.adapter.web;



import com.alibaba.cola.dto.MultiResponse;
import com.pxb7.mall.response.PxPageResponse;
import com.pxb7.mall.workorder.app.model.*;
import com.pxb7.mall.workorder.app.service.RemindPlanAppService;
import jakarta.annotation.Resource;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

/**
 * 管理后台-预警计划
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/web/remind-plan/")
public class WebRemindPlanController {

    @Resource
    private RemindPlanAppService remindPlanAppService;

    /**
     * 新增
     */
    @PostMapping("insert")
    public SingleResponse<Long> insert(@RequestBody @Validated RemindPlanReqDTO.AddDTO param) {
        return SingleResponse.of(remindPlanAppService.insert(param));
    }

    /**
     * 修改
     */
    @PostMapping("update")
    public SingleResponse<Boolean> update(@RequestBody @Validated RemindPlanReqDTO.UpdateDTO param) {
        return SingleResponse.of(remindPlanAppService.update(param));
    }

    /**
     * 详情
     */
    @RequestMapping("detail")
    public SingleResponse<RemindPlanRespDTO.DetailDTO> detail(@RequestParam(value = "remindPlanId") String remindPlanId) {
        return SingleResponse.of(remindPlanAppService.findById(Long.valueOf(remindPlanId)));
    }

    /**
     * 删除
     */
    @PostMapping("del")
    public SingleResponse<Boolean> del(@RequestBody @Validated RemindPlanReqDTO.DelDTO param) {
        return SingleResponse.of(remindPlanAppService.deleteById(param));
    }

    /**
     * 分页
     */
    @PostMapping("page")
    public PxPageResponse<RemindPlanPageDetailDTO> page(@RequestBody @Validated RemindPlanReqDTO.PageDTO param) {
        Page<RemindPlanPageDetailDTO> page = remindPlanAppService.page(param);
        return PxPageResponse.of(page.getRecords(), (int) page.getTotal(), (int) page.getSize(), (int) page.getCurrent());
    }

    /**
     * 获取可选游戏列表
     * @param param
     * @return
     */
    @PostMapping("optional-game-list")
    public MultiResponse<OptionalGameRespDTO> getGameList(@RequestBody @Validated OptionalGameReqDTO param) {
        List<OptionalGameRespDTO> optionalGameDetailRespDTOS = remindPlanAppService.getOptaionalGames(param);
        return MultiResponse.of(optionalGameDetailRespDTOS);
    }

}

