package com.pxb7.mall.workorder.domain.remind.task;

import com.alibaba.cola.extension.BizScenario;
import com.alibaba.cola.extension.ExtensionExecutor;
import com.alibaba.fastjson2.JSON;
import com.pxb7.mall.workorder.client.enums.BizTypeEnum;
import com.pxb7.mall.workorder.client.enums.RemindMethodEnum;
import com.pxb7.mall.workorder.client.enums.RemindObjectEnum;
import com.pxb7.mall.workorder.client.message.RemindPlanMessage;
import com.pxb7.mall.workorder.domain.mapping.RemindPlanGameConfigDomainMapping;
import com.pxb7.mall.workorder.domain.model.*;
import com.pxb7.mall.workorder.domain.remind.extension.RemindBizExtPt;
import com.pxb7.mall.workorder.domain.remind.extension.context.RemindBizContext;
import com.pxb7.mall.workorder.domain.service.RemindPlanGameConfigDomainService;
import com.pxb7.mall.workorder.domain.service.RemindPlanRuleDomainService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * 消息处理任务
 * <AUTHOR>
 * @date 2025/4/9 16:02
 */

@Slf4j
@Component
public class RemindMessageTask {

    @Resource
    private RemindPlanRuleDomainService remindPlanRuleDomainService;

    @Resource
    private RemindPlanGameConfigDomainService remindPlanGameConfigDomainService;

    @Resource
    private ExtensionExecutor extensionExecutor;

    /**
     * 处理提醒消息发送
     * @param remindPlanMessage
     * @param remindRecord
     * @return
     */
    public Boolean handleRemindMessage(
            RemindPlanMessage remindPlanMessage, BaseRemindRecordBO remindRecord) {
        BizTypeEnum bizTypeEnum = BizTypeEnum.getByType(remindPlanMessage.getBizType());
        if (Objects.isNull(bizTypeEnum)) {
            log.error("Fail to get remind record type. remindRecord:{}", remindRecord);
            return Boolean.FALSE;
        }

        RemindPlanGameConfigRespBO.DetailBO remindPlanGameConfig =
                remindPlanGameConfigDomainService.findById(remindRecord.getGameConfigId());
        if (Objects.isNull(remindPlanGameConfig)) {
            log.error("Fail to find remind plan game config by gameConfigId. gameConfigId:{}", remindRecord.getGameConfigId());
            return Boolean.FALSE;
        }
        RemindPlanGameConfigBO remindPlanGameConfigBO =
                RemindPlanGameConfigDomainMapping.INSTANCE.remindPlanGameConfigPO2BO(remindPlanGameConfig);

        //提醒方式配置,[{"method":"feishu","objects":[{"object":"customercare"},{"object":"group","webhook","111"}]},
        // {"method":"im","objects":[{"object":"customercare"}]}]
        RemindPlanRuleRespBO.DetailBO planRule = remindPlanRuleDomainService.findById(remindPlanMessage.getPlanRuleId());
        TimeConfigBO remindTimeConfig = JSON.parseObject(planRule.getRemindTimeConfig(), TimeConfigBO.class);


        if (Objects.isNull(planRule)) {
            log.error("Fail to find remind plan rule by planRuleId. planRuleId:{}", remindPlanMessage.getPlanRuleId());
            return Boolean.FALSE;
        }
        List<RemindMethodConfigBO> remindMethodConfigs = JSON.parseArray(planRule.getRemindMethodConfig(), RemindMethodConfigBO.class);
        if (CollectionUtils.isEmpty(remindMethodConfigs)) {
            log.error("Fail to get remind method config list by planRuleId. planRuleId:{}", remindPlanMessage.getPlanRuleId());
            return Boolean.FALSE;
        }
        for (RemindMethodConfigBO remindMethodConfig : remindMethodConfigs) {
            RemindMethodEnum remindMethod = RemindMethodEnum.getByCode(remindMethodConfig.getMethod());
            if (Objects.isNull(remindMethod)) {
                log.error("Fail to get remind method enum by code. code:{}", remindMethodConfig.getMethod());
                continue;
            }
            List<RemindMethodConfigBO.RemindObjectBO> remindObjectBOList = remindMethodConfig.getObjects();
            if (CollectionUtils.isEmpty(remindObjectBOList)) {
                log.error("Fail to get remind object list by code. code:{}", remindMethodConfig.getMethod());
                continue;
            }
            for (RemindMethodConfigBO.RemindObjectBO remindObjectBO : remindObjectBOList) {
                RemindObjectEnum remindObject = RemindObjectEnum.getByCode(remindObjectBO.getObject());
                if (Objects.isNull(remindObject)) {
                    log.error("Fail to get remind object enum by code. code:{}", remindObjectBO.getObject());
                    continue;
                }
                //上下文对象
                RemindBizContext remindBizContext = new RemindBizContext();
                remindBizContext.setRemindRecord(remindRecord);
                remindBizContext.setRemindPlanMessage(remindPlanMessage);
                remindBizContext.setRemindPlanGameConfig(remindPlanGameConfigBO);
                remindBizContext.setRemindObjectBO(remindObjectBO);
                remindBizContext.setTimeConfigBO(remindTimeConfig);

                BizScenario bizScenario = BizScenario.valueOf(bizTypeEnum.getCode(), remindMethod.getCode(), remindObject.getCode());
                extensionExecutor.executeVoid(RemindBizExtPt.class, bizScenario,
                        componentExecutor -> componentExecutor.executeRemindTask(remindBizContext));
            }
        }
        return Boolean.TRUE;
    }

}
