package com.pxb7.mall.workorder.app.service;

import com.alibaba.cola.exception.BizException;

import java.time.LocalDateTime;
import java.util.*;

import java.util.function.Function;
import java.util.stream.Collectors;

import com.alibaba.cola.exception.Assert;
import com.pxb7.mall.auth.c.util.AdminUserUtil;
import com.pxb7.mall.auth.dto.AdminUserDTO;
import com.pxb7.mall.workorder.app.model.*;
import com.pxb7.mall.workorder.app.util.*;
import com.pxb7.mall.workorder.domain.model.*;
import com.pxb7.mall.workorder.infra.aop.ClusterRedisLock;
import com.pxb7.mall.workorder.infra.enums.RemindPlanBusinessTypeEnum;
import com.pxb7.mall.workorder.infra.enums.RemindPlanServiceTypeEnum;
import com.pxb7.mall.workorder.infra.enums.RemindPlanWorkOrderStatusEnum;
import com.pxb7.mall.workorder.app.mapping.NotDisturbPeriodDomainMapping;
import com.pxb7.mall.workorder.app.mapping.RemindPlanGameConfigAppMapping;
import com.pxb7.mall.workorder.app.mapping.RemindPlanRuleAppMapping;
import com.pxb7.mall.workorder.domain.service.RemindPlanGameConfigDomainService;
import com.pxb7.mall.workorder.domain.service.RemindSubPlanDomainService;
import com.pxb7.mall.workorder.infra.model.SysUserRespPO;
import com.pxb7.mall.workorder.infra.repository.gateway.dubbo.user.SysUserGateway;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.pxb7.mall.workorder.app.mapping.RemindPlanAppMapping;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pxb7.mall.workorder.domain.service.RemindPlanDomainService;
import org.springframework.util.CollectionUtils;

import static com.pxb7.mall.workorder.client.enums.BizErrorCodeEnum.*;

/**
 * 提醒服务预警计划app服务
 *
 * <AUTHOR>
 * @since 2025-03-24 21:12:17
 */
@Service
public class RemindPlanAppService {

    @Resource
    private RemindPlanDomainService remindPlanDomainService;

    @Resource
    private RemindSubPlanDomainService remindSubPlanDomainService;

    @Resource
    private RemindPlanGameConfigDomainService remindPlanGameConfigDomainService;

    @Resource
    private SysUserGateway sysUserGateway;

    @ClusterRedisLock(prefix = "remind_plan_insert_lock", value = "")
    public Long insert(RemindPlanReqDTO.AddDTO param) {
        AdminUserDTO adminUser = AdminUserUtil.getAdminUser();
        Assert.isTrue(adminUser != null && StringUtils.isNotBlank(adminUser.getUserId()), USER_INFO_NOT_FOUND.getErrCode(), USER_INFO_NOT_FOUND.getErrDesc());

        RemindPlanServiceTypeEnum serviceTypeEnum = RemindPlanServiceTypeEnum.getEnum(param.getServiceType());
        Assert.isTrue(serviceTypeEnum != null, PARAM_ERROR.getErrCode(), "业务类型暂不支持");
        List<Integer> businessTypes = param.getBusinessTypes();
        List<Integer> workOrderStatuses = param.getWorkOrderStatuses();
        List<Integer> memberships = param.getMemberships();
        if (RemindPlanServiceTypeEnum.DELIVERY_PRODUCT.equals(serviceTypeEnum)) {
            Assert.notEmpty(businessTypes, PARAM_ERROR.getErrCode(), "交易类型不能为空");
            for (Integer businessType : businessTypes) {
                RemindPlanBusinessTypeEnum businessTypeEnum = RemindPlanBusinessTypeEnum.getEnum(businessType);
                Assert.notNull(businessTypeEnum , PARAM_ERROR.getErrCode(), "交易类型暂不支持,输入值为："+businessType);
            }
        } else if (RemindPlanServiceTypeEnum.WORK_ORDER.equals(serviceTypeEnum)) {
            Assert.notEmpty(workOrderStatuses, PARAM_ERROR.getErrCode(), "工单状态不能为空");
            for (Integer workOrderStatus : workOrderStatuses) {
                RemindPlanWorkOrderStatusEnum workOrderStatusEnum = RemindPlanWorkOrderStatusEnum.getEnum(workOrderStatus);
                Assert.notNull(workOrderStatusEnum , PARAM_ERROR.getErrCode(), "工单状态暂不支持,输入值为："+workOrderStatus);
            }
            Assert.notEmpty(memberships, PARAM_ERROR.getErrCode(), "订单来源不能为空");
        }
        //判断计划名称是否重复
        Boolean existPlanName = remindPlanDomainService.existPlanName(param.getPlanName(),param.getServiceType());
        Assert.isFalse(existPlanName, PARAM_ERROR.getErrCode(), "预警计划名称已存在");

        PlanRuleOrGameConfigChecker.checkPlanRuleSorted(param.getPlanRules());
        PlanRuleOrGameConfigChecker.compareGameConfigTime(param.getPlanGameConfigs());
        PlanRuleOrGameConfigChecker.comparePlanRuleAndGameConfigTime(param.getPlanRules(),param.getPlanGameConfigs());
        RemindPlanBO remindPlanBO = RemindPlanBuildUtil.buildFromAddDTO(param, adminUser);
        this.checkRemindPlanGameConfigBO(remindPlanBO,remindPlanBO.getAllRemindPlanGameConfigs());
        List<RemindPlanOperateRecordBO> remindPlanOperateRecordBOS =
            RemindPlanOperateRecordBuildUtil.buildAddOperateRecordList(remindPlanBO, adminUser);
        return remindPlanDomainService.saveAllInfo(remindPlanBO,remindPlanOperateRecordBOS);
    }

    private void checkRemindPlanGameConfigBO(RemindPlanBO remindPlanBO,List<RemindPlanGameConfigBO> addRemindPlanGameConfigBOs) {
        if (CollectionUtils.isEmpty(addRemindPlanGameConfigBOs)){
            return;
        }
        QuerySelectedGameBO selectedGameBO = new QuerySelectedGameBO();
        selectedGameBO.setServiceType(remindPlanBO.getServiceType());
        if (RemindPlanServiceTypeEnum.DELIVERY_PRODUCT.getValue().equals(remindPlanBO.getServiceType())){
            List<Integer> tempBusinessTypes =remindPlanBO.getRemindSubPlans().stream().map(RemindSubPlanBO::getBusinessType).filter(Objects::nonNull).distinct().toList();
            selectedGameBO.setBusinessTypes(tempBusinessTypes);
        }else if (RemindPlanServiceTypeEnum.WORK_ORDER.getValue().equals(remindPlanBO.getServiceType())){
            List<Integer> tempWorkOrderStatuses =remindPlanBO.getRemindSubPlans().stream().map(RemindSubPlanBO::getWorkOrderStatus).filter(Objects::nonNull).distinct().toList();
            List<Integer> tempOnShelfTypes = remindPlanBO.getRemindSubPlans().stream().map(RemindSubPlanBO::getOnShelfType).filter(Objects::nonNull).distinct().toList();
            List<Integer> tempMemberships = remindPlanBO.getRemindSubPlans().stream().map(RemindSubPlanBO::getMembership).filter(Objects::nonNull).distinct().toList();
            selectedGameBO.setWorkOrderStatuses(tempWorkOrderStatuses);
            selectedGameBO.setOnShelfTypes(tempOnShelfTypes);
            selectedGameBO.setMemberships(tempMemberships);
        }
        List<RemindPlanGameConfigBO> selectedPlanGameConfigList = remindPlanDomainService.getSelectedPlanGameConfigList(selectedGameBO);
        Map<String, RemindPlanGameConfigBO> gameIdToConfigMap = selectedPlanGameConfigList.stream()
            .collect(Collectors.toMap(RemindPlanGameConfigBO::getGameId, Function.identity(), (l, r) -> l));

        for (RemindPlanGameConfigBO addPlanGameConfig : addRemindPlanGameConfigBOs) {
            RemindPlanGameConfigBO remindPlanGameConfigBO = gameIdToConfigMap.get(addPlanGameConfig.getGameId());
            if (null != remindPlanGameConfigBO) {
                throw new BizException(PARAM_ERROR.getErrCode(),
                    "新增预警设置失败:" + remindPlanGameConfigBO.getGameName() + "已被其他预警计划(ID:"+remindPlanGameConfigBO.getRemindPlanId()+")选中");
            }
        }
    }

    @ClusterRedisLock(prefix = "remind_plan_update_lock", value = "")
    public boolean update(RemindPlanReqDTO.UpdateDTO param) {
        AdminUserDTO adminUser = AdminUserUtil.getAdminUser();
        Assert.isTrue(adminUser != null && StringUtils.isNotBlank(adminUser.getUserId()), USER_INFO_NOT_FOUND.getErrCode(), USER_INFO_NOT_FOUND.getErrDesc());

        RemindPlanServiceTypeEnum serviceTypeEnum = RemindPlanServiceTypeEnum.getEnum(param.getServiceType());
        Assert.isTrue(serviceTypeEnum != null, PARAM_ERROR.getErrCode(), "业务类型暂不支持");
        List<Integer> businessTypes = param.getBusinessTypes();
        List<Integer> workOrderStatuses = param.getWorkOrderStatuses();
        List<Integer> memberships = param.getMemberships();
        if (RemindPlanServiceTypeEnum.DELIVERY_PRODUCT.equals(serviceTypeEnum)) {
            Assert.notEmpty(businessTypes, PARAM_ERROR.getErrCode(), "交易类型不能为空");
            for (Integer businessType : businessTypes) {
                RemindPlanBusinessTypeEnum businessTypeEnum = RemindPlanBusinessTypeEnum.getEnum(businessType);
                Assert.notNull(businessTypeEnum , PARAM_ERROR.getErrCode(), "交易类型暂不支持,输入值为："+businessType);
            }
        } else if (RemindPlanServiceTypeEnum.WORK_ORDER.equals(serviceTypeEnum)) {
            Assert.notEmpty(workOrderStatuses, PARAM_ERROR.getErrCode(), "工单状态不能为空");
            for (Integer workOrderStatus : workOrderStatuses) {
                RemindPlanWorkOrderStatusEnum workOrderStatusEnum = RemindPlanWorkOrderStatusEnum.getEnum(workOrderStatus);
                Assert.notNull(workOrderStatusEnum , PARAM_ERROR.getErrCode(), "工单状态暂不支持,输入值为："+workOrderStatus);
            }
            Assert.notEmpty(memberships, PARAM_ERROR.getErrCode(), "订单来源不能为空");
        }
        List<PlanRuleDTO> planRuleDTOS = param.getPlanRules();
        PlanRuleOrGameConfigChecker.checkPlanRuleSorted(planRuleDTOS);
        PlanRuleOrGameConfigChecker.compareGameConfigTime(param.getPlanGameConfigs());
        PlanRuleOrGameConfigChecker.comparePlanRuleAndGameConfigTime(param.getPlanRules(),param.getPlanGameConfigs());

        RemindPlanBO remindPlanBO = remindPlanDomainService.getRemindPlanAllInfo(param.getId());
        Assert.notNull(remindPlanBO, REMIND_PLAN_NOT_FOUND.getErrCode(), REMIND_PLAN_NOT_FOUND.getErrDesc());
        RemindPlanBO originalRemindPlanBO = new RemindPlanBO();
        BeanUtils.copyProperties(remindPlanBO, originalRemindPlanBO);
        remindPlanBO.setPlanName(param.getPlanName());
        remindPlanBO.setNotDisturbPeriod(NotDisturbPeriodDomainMapping.INSTANCE.notDisturbPeriodDTO2BO(param.getNotDisturbPeriod()));
        remindPlanBO.setUpdateUserId(adminUser.getUserId());
        remindPlanBO.setUpdateTime(LocalDateTime.now());

        //判断计划名称是否重复
        if (!param.getPlanName().equals(originalRemindPlanBO.getPlanName())){
            Boolean existPlanName = remindPlanDomainService.existPlanName(param.getPlanName(), originalRemindPlanBO.getServiceType());
            Assert.isFalse(existPlanName, PARAM_ERROR.getErrCode(), "预警计划名称已存在");
        }

        // 预警-游戏配置：
        // 新增了哪些？删除了哪些？ 修改了哪些？
        RemindPlanGameConfigRefreshBO remindPlanGameConfigRefreshBO = RemindPlanGameConfigBuildUtil.buildRemindPlanGameConfigRefreshBO(param.getPlanGameConfigs(), remindPlanBO,adminUser);
        this.checkRemindPlanGameConfigBO(remindPlanBO,remindPlanGameConfigRefreshBO.getNeedAddPlanGameConfigs());

        // 新增了哪些？删除了哪些？ 修改了哪些？
        RemindPlanRuleRefreshBO remindPlanRuleRefreshBO =
            RemindPlanRuleBuildUtil.buildRemindPlanRuleRefreshBO(planRuleDTOS, remindPlanBO, adminUser);

        RemindPlanUpdateContextBO remindPlanUpdateContextBO = new RemindPlanUpdateContextBO();
        remindPlanUpdateContextBO.setOriginRemindPlan(originalRemindPlanBO);
        remindPlanUpdateContextBO.setRemindPlan(remindPlanBO);
        remindPlanUpdateContextBO.setRemindPlanRuleRefresh(remindPlanRuleRefreshBO);
        remindPlanUpdateContextBO.setRemindPlanGameConfigRefresh(remindPlanGameConfigRefreshBO);
        remindPlanUpdateContextBO.setOperateRecords(RemindPlanOperateRecordBuildUtil.buildUpdateOperateRecordList(remindPlanUpdateContextBO,adminUser));
        return remindPlanDomainService.updateAllInfo(remindPlanUpdateContextBO);
    }



    public RemindPlanRespDTO.DetailDTO findById(Long remindPlanId) {
        AdminUserDTO adminUser = AdminUserUtil.getAdminUser();
        Assert.isTrue(adminUser != null && StringUtils.isNotBlank(adminUser.getUserId()), USER_INFO_NOT_FOUND.getErrCode(), USER_INFO_NOT_FOUND.getErrDesc());

        RemindPlanBO remindPlanBO = remindPlanDomainService.getRemindPlanAllInfo(remindPlanId);
        Assert.notNull(remindPlanBO, REMIND_PLAN_NOT_FOUND.getErrCode(), REMIND_PLAN_NOT_FOUND.getErrDesc());

        List<RemindPlanGameConfigBO> allRemindPlanGameConfigBOS = Optional.of(remindPlanBO).map(RemindPlanBO::getAllRemindPlanGameConfigs).orElse(new ArrayList<>());

        Integer serviceType = remindPlanBO.getServiceType();
        List<RemindSubPlanBO> remindSubPlans = remindPlanBO.getRemindSubPlans();
        RemindPlanRespDTO.DetailDTO detailDTO = new RemindPlanRespDTO.DetailDTO();
        detailDTO.setId(remindPlanBO.getId());
        detailDTO.setPlanName(remindPlanBO.getPlanName());
        detailDTO.setServiceType(serviceType);
        detailDTO.setNotDisturbPeriod(NotDisturbPeriodDomainMapping.INSTANCE.notDisturbPeriodBO2DTO(remindPlanBO.getNotDisturbPeriod()));
        if (RemindPlanServiceTypeEnum.DELIVERY_PRODUCT.getValue().equals(serviceType)){
            List<Integer> businessTypes =remindSubPlans.stream().map(RemindSubPlanBO::getBusinessType).filter(Objects::nonNull).distinct().toList();
            detailDTO.setBusinessTypes(businessTypes);
        }else if (RemindPlanServiceTypeEnum.WORK_ORDER.getValue().equals(serviceType)){
            List<Integer> workOrderStatuses =remindSubPlans.stream().map(RemindSubPlanBO::getWorkOrderStatus).filter(Objects::nonNull).distinct().toList();
            List<Integer> onShelfTypes = remindSubPlans.stream().map(RemindSubPlanBO::getOnShelfType).filter(Objects::nonNull).distinct().toList();
            List<Integer> memberships = remindSubPlans.stream().map(RemindSubPlanBO::getMembership).filter(Objects::nonNull).distinct().toList();
            detailDTO.setWorkOrderStatuses(workOrderStatuses);
            detailDTO.setOnShelfTypes(onShelfTypes);
            detailDTO.setMemberships(memberships);
        }
        detailDTO.setPlanGameConfigs(RemindPlanGameConfigBuildUtil.gainDistinctPlanGameConfigDTOList(allRemindPlanGameConfigBOS,remindPlanBO));
        detailDTO.setPlanRules(RemindPlanRuleAppMapping.INSTANCE.remindPlanRuleBO2ListDTO(remindPlanBO.getRemindPlanRules()));
        return detailDTO;
    }



    public Page<RemindPlanPageDetailDTO> page(RemindPlanReqDTO.PageDTO param) {
        AdminUserDTO adminUser = AdminUserUtil.getAdminUser();
        Assert.isTrue(adminUser != null && StringUtils.isNotBlank(adminUser.getUserId()), USER_INFO_NOT_FOUND.getErrCode(), USER_INFO_NOT_FOUND.getErrDesc());

        RemindPlanReqBO.PageBO pageBO = RemindPlanAppMapping.INSTANCE.remindPlanDTO2PageBO(param);
        Page<RemindPlanBO> page = remindPlanDomainService.page(pageBO);
        List<RemindPlanBO> remindPlans = Optional.ofNullable(page).map(Page::getRecords).orElse(new ArrayList<>());
        List<Long> remindPlanIds = remindPlans.stream().map(RemindPlanBO::getId).toList();
        List<RemindSubPlanBO> remindSubPlanList = remindSubPlanDomainService.getRemindSubPlanList(remindPlanIds);
        List<RemindPlanGameConfigBO> remindPlanGameConfigList = remindPlanGameConfigDomainService.getRemindPlanGameConfigList(remindPlanIds);

        Set<String> userIds = remindPlans.stream().map(RemindPlanBO::getUpdateUserId).collect(Collectors.toSet());
        List<SysUserRespPO> sysUserInfoList = sysUserGateway.getSysUserInfoList(userIds);
        Map<String, SysUserRespPO> sysUserRespPOMap = sysUserInfoList.stream().collect(Collectors.toMap(SysUserRespPO::getUserId, Function.identity()));

        Page<RemindPlanPageDetailDTO> pageDTO = RemindPlanAppMapping.INSTANCE.remindPlanBO2PageDTO(page);
        // 构造返回的dto数据
        List<RemindPlanPageDetailDTO> detailDTOS = RemindPlanPageBuildUtil.buildPageDetailDTOList(remindPlans, remindSubPlanList, remindPlanGameConfigList,sysUserRespPOMap);
        pageDTO.setRecords(detailDTOS);
        return pageDTO;
    }



    /**
     * 根据ID删除提醒计划
     *
     * 此方法首先验证当前用户是否已登录并具有管理员权限，然后根据提供的参数获取提醒计划的详细信息
     * 如果提醒计划存在，则构建一个包含提醒计划及其关联子计划和配置的删除上下文对象，并执行删除操作
     *
     * @param param 包含要删除提醒计划ID的请求数据传输对象
     * @return 返回删除操作的成功与否
     */
    @ClusterRedisLock(prefix = "remind_plan_delete_", value = "#param.id")
    public boolean deleteById(RemindPlanReqDTO.DelDTO param) {
        // 获取当前登录的管理员用户信息
        AdminUserDTO adminUser = AdminUserUtil.getAdminUser();
        Assert.isTrue(adminUser != null && StringUtils.isNotBlank(adminUser.getUserId()), USER_INFO_NOT_FOUND.getErrCode(), USER_INFO_NOT_FOUND.getErrDesc());

        // 根据提供的ID获取提醒计划的详细信息
        RemindPlanBO remindPlanBO = remindPlanDomainService.getRemindPlanAllInfo(param.getId());
        Assert.notNull(remindPlanBO, REMIND_PLAN_NOT_FOUND.getErrCode(), REMIND_PLAN_NOT_FOUND.getErrDesc());

        // 创建提醒计划删除上下文对象
        RemindPlanDeleteContextBO remindPlanDeleteContextBO = new RemindPlanDeleteContextBO();
        // 设置提醒计划ID
        remindPlanDeleteContextBO.setRemindPlanId(remindPlanBO.getId());
        // 设置提醒子计划ID列表
        remindPlanDeleteContextBO.setRemindSubPlanIds(remindPlanBO.getRemindSubPlans().stream().map(RemindSubPlanBO::getId).toList());
        // 设置提醒计划游戏配置ID列表
        remindPlanDeleteContextBO.setRemindPlanGameConfigIds(remindPlanBO.getAllRemindPlanGameConfigs().stream().map(RemindPlanGameConfigBO::getId).toList());
        // 设置提醒计划规则ID列表
        remindPlanDeleteContextBO.setRemindPlanRuleIds(remindPlanBO.getRemindPlanRules().stream().map(RemindPlanRuleBO::getId).toList());
        // 构建并设置删除操作的操作记录列表
        remindPlanDeleteContextBO.setOperateRecords(RemindPlanOperateRecordBuildUtil.buildDeleteAllOperateRecordList(remindPlanBO, adminUser));
        // 调用领域服务执行删除操作，并返回操作结果
        return remindPlanDomainService.delete(remindPlanDeleteContextBO);
    }


    public List<OptionalGameRespDTO> getOptaionalGames(OptionalGameReqDTO param) {
        AdminUserDTO adminUser = AdminUserUtil.getAdminUser();
        Assert.isTrue(adminUser != null && StringUtils.isNotBlank(adminUser.getUserId()), USER_INFO_NOT_FOUND.getErrCode(), USER_INFO_NOT_FOUND.getErrDesc());


        Integer serviceType = param.getServiceType();
        RemindPlanServiceTypeEnum serviceTypeEnum = RemindPlanServiceTypeEnum.getEnum(serviceType);
        Assert.isTrue(serviceTypeEnum != null, PARAM_ERROR.getErrCode(), "服务类型暂不支持");
        if (RemindPlanServiceTypeEnum.DELIVERY_PRODUCT.equals(serviceTypeEnum)){
            Assert.notEmpty(param.getBusinessTypes(), PARAM_ERROR.getErrCode(), "业务类型不能为空");
        }else if (RemindPlanServiceTypeEnum.WORK_ORDER.equals(serviceTypeEnum)){
            List<Integer> workOrderStatuses = param.getWorkOrderStatuses();
            Assert.notEmpty(workOrderStatuses, PARAM_ERROR.getErrCode(), "工单状态不能为空");
            if (workOrderStatuses.contains(RemindPlanWorkOrderStatusEnum.WAIT_ACCEPT.getValue())) {
                Assert.isTrue(workOrderStatuses.size()==1, PARAM_ERROR.getErrCode(), "工单状态:待接单不支持和其他转态一起参与筛选");
            }
            List<Integer> memberships = param.getMemberships();
            Assert.notEmpty(memberships, PARAM_ERROR.getErrCode(), "订单来源不能为空");
        }
        OptionalGameReqBO optionalGameReqBO = RemindPlanGameConfigAppMapping.INSTANCE.optionalGameReqDTO2BO(param);
        List<OptionalGameRespBO> optaionalGames = remindPlanDomainService.getOptionalGames(optionalGameReqBO);
        return RemindPlanGameConfigAppMapping.INSTANCE.optionalGameRespBO2ListDTO(optaionalGames);
    }

}

