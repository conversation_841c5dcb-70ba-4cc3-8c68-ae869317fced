package com.pxb7.mall.workorder.domain.message;

import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * 还价工单消息
 */
@Data
@Accessors(chain = true)
@ToString
public class BargainTicketMessage implements Serializable {
    /**
     * 议价单id
     */
    private String bargainId;
    /**
     * 游戏id
     */
    private String gameId;

    /**
     * 游戏id
     */
    private String gameName;

    /**
     * 最终议价金额(分)
     */
    private Long bargainPrice;

    /**
     * 比例
     */
    private Double bargainRatio;
    /**
     * 议价状态
     */
    private Integer recordStatus;
    /**
     * 商品价格
     */
    private Long price;

    private String buyerUserId;
    /**
     * 最新议价时间
     */
    private LocalDateTime bargainTime;
    /**
     * 创建时间
     */
    private LocalDateTime bargainCreateTime;

    /**
     * 工单id
     */
    private String receiveId;

    /**
     * 商品编号
     */
    private String productUniqueNo;

    /**
     * 商品名称
     */
    private String productName;


    /**
     * 0:还价单创建 1:还价单更新 2:工单状态更新 3
     */
    private Integer eventType;
    /**
     * 10:未读 20:已读
     */
    private Integer readFlag;

    /**
     * 10:未成交 20:有成交
     */
    private Integer dealFlag;

}
