package com.pxb7.mall.workorder.infra.repository.gateway.dubbo.user;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.fastjson2.JSON;
import com.pxb7.mall.merchant.client.api.MerchantAccountServiceI;
import com.pxb7.mall.merchant.client.dto.response.merchant.MerchantAccountTypeRespDTO;
import com.pxb7.mall.user.admin.api.SysUserServiceI;
import com.pxb7.mall.user.admin.dto.request.sysuser.GetUserInfoReqDTO;
import com.pxb7.mall.user.admin.dto.response.sysuser.GetSysUserRespDTO;
import com.pxb7.mall.workorder.infra.enums.LocalOrRedisCacheEnum;
import com.pxb7.mall.workorder.infra.mapping.SysUserGateWayMapping;
import com.pxb7.mall.workorder.infra.model.SysUserRespPO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2025/4/1 20:45
 */

@Repository
@Slf4j
public class SysUserGateway {

    @DubboReference(check = false)
    private SysUserServiceI sysUserService;

    @DubboReference(check = false)
    private MerchantAccountServiceI merchantAccountService;

    /**
     * 根据用户ID获取客服信息
     * @param userIds
     * @return
     */
    public List<SysUserRespPO> getSysUserInfoList(Set<String> userIds) {
        try {
            if (CollectionUtils.isEmpty(userIds)) {
                return new ArrayList<>();
            }
            GetUserInfoReqDTO getUserInfoReqDTO = GetUserInfoReqDTO.builder().userIds(userIds).build();
            MultiResponse<GetSysUserRespDTO> response = sysUserService.getUserInfoByIds(getUserInfoReqDTO);
            if (response != null && response.isSuccess()) {
                return SysUserGateWayMapping.INSTANCE.dto2POList(response.getData());
            }
            log.error("getSysUserInfoList fail message,userId:{},{}", JSON.toJSONString(userIds),JSON.toJSONString(response));
            return new ArrayList<>();
        } catch (Exception e) {
            log.error("getSysUserInfoList error,userId:{}", JSON.toJSONString(userIds));
            return new ArrayList<>();
        }
    }

    /**
     * 根据用户ID查询用户信息
     * @param userId
     * @return
     */
    @Cacheable(value = LocalOrRedisCacheEnum.Constants.SYS_USER_BY_USER_ID,
            key = "#p0", sync = true, cacheManager = "localCacheManager")
    public SysUserRespPO getSysUserInfo(String userId) {
        List<SysUserRespPO> sysUserInfoList = this.getSysUserInfoList(Set.of(userId));
        if (CollectionUtils.isEmpty(sysUserInfoList)){
            return null;
        }
        return sysUserInfoList.get(0);
    }

    /**
     * 根据用户名精确查询用户信息
     * @param userName
     * @return
     */
    @Cacheable(value = LocalOrRedisCacheEnum.Constants.SYS_USER_BY_USER_NAME,
            key = "#p0", sync = true, cacheManager = "localCacheManager")
    public SysUserRespPO getSysUserByUserName(String userName) {
        if (StringUtils.isBlank(userName)) {
            return null;
        }
        try {
            SingleResponse<GetSysUserRespDTO> response = sysUserService.getSysUserByUserName(userName);
            if (response != null && response.isSuccess()) {
                return SysUserGateWayMapping.INSTANCE.dto2PO(response.getData());
            }
            log.error("getSysUserByUserName fail message,userName:{},{}", userName, JSON.toJSONString(response));
            return null;
        } catch (Exception e) {
            log.error("getSysUserByUserName error,userName:{}", userName);
            return null;
        }
     }

    /**
     * 通过sellerId查询卖家身份（散户，号商，3A）
     * @param sellerId
     * @return
     */
    public Integer getSellerMembership(String sellerId) {
        if (StringUtils.isBlank(sellerId)) {
            return null;
        }
        try {
            List<String> userIds = List.of(sellerId);
            SingleResponse<List<MerchantAccountTypeRespDTO>> response = merchantAccountService.queryAccountType(userIds);
            if (response != null
                    && response.isSuccess()
                    && !CollectionUtils.isEmpty(response.getData())) {
                return response.getData().get(0).getMerchantType();
            }
            log.error("getSellerMembership fail message,sellerId:{},{}", sellerId, JSON.toJSONString(response));
            return null;
        } catch (Exception e) {
            log.error("getSellerMembership error,sellerId:{}", sellerId);
            return null;
        }
    }

}
