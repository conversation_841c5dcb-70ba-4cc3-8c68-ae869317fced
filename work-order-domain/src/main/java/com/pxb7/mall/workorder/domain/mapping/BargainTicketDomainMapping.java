package com.pxb7.mall.workorder.domain.mapping;

import com.pxb7.mall.workorder.domain.model.BargainTicketBO;
import com.pxb7.mall.workorder.domain.model.BargainTicketReqBO;
import com.pxb7.mall.workorder.infra.model.BargainTicketReqPO;
import com.pxb7.mall.workorder.infra.repository.es.entity.BargainTicketDoc;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface BargainTicketDomainMapping {

    BargainTicketDomainMapping INSTANCE = Mappers.getMapper(BargainTicketDomainMapping.class);

    BargainTicketReqPO.PagePO convertBo2Po(BargainTicketReqBO.PageBO source);

    List<BargainTicketBO> convertDoc2BoList(List<BargainTicketDoc> list);

    BargainTicketBO convertDoc2Bo(BargainTicketDoc doc);
}


