package com.pxb7.mall.workorder.app.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pxb7.mall.trade.order.client.message.OrderItemStatusChangeMQDTO;
import com.pxb7.mall.workorder.client.enums.BizTypeEnum;
import com.pxb7.mall.workorder.domain.model.RemindPlanRetryTaskReqBO;
import com.pxb7.mall.workorder.domain.model.RemindPlanRetryTaskRespBO;
import com.pxb7.mall.workorder.domain.service.RemindPlanRetryTaskDomainService;
import com.pxb7.mall.workorder.infra.constant.CommonConstants;
import com.pxb7.mall.workorder.infra.util.TimeUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/5/6 21:19
 */

@Slf4j
@Service
public class RemindPlanRetryTaskAppService {

    @Resource
    private RemindPlanRetryTaskDomainService retryTaskDomainService;

    @Resource
    private RemindDeliveryProductAppService deliveryProductAppService;


    /**
     * 保存重试记录
     * @param orderStatusChangeDTO
     */
    public void saveRetryTask(OrderItemStatusChangeMQDTO orderStatusChangeDTO) {
        String orderItemId = orderStatusChangeDTO.getOrderItemId();
        log.warn("交付房间号为空,待重试,orderItemId:{}", orderItemId);
        RemindPlanRetryTaskReqBO.AddBO retryTask = new RemindPlanRetryTaskReqBO.AddBO();
        retryTask.setBizId(orderItemId);
        retryTask.setRecordInfo(JSON.toJSONString(orderStatusChangeDTO));
        retryTask.setServiceType(BizTypeEnum.DELIVERY_PRODUCT.getType());
        retryTaskDomainService.insert(retryTask);
    }

    /**
     * 执行重试任务
     */
    public void executeRetryTask() {
        log.info("执行生成提醒计划异常重试任务，开始:{}", LocalDateTime.now());

        //分页查询符合条件的提醒计划记录
        //游标id
        long minId = 0;
        long pageSize = CommonConstants.QUERY_PAGE_SIZE;
        while (true) {
            RemindPlanRetryTaskReqBO.PageBO param = new RemindPlanRetryTaskReqBO.PageBO();
            param.setId(minId);
            param.setStatus(0);
            param.setPageIndex(1L);
            param.setPageSize(pageSize);

            Page<RemindPlanRetryTaskRespBO.DetailBO> page = retryTaskDomainService.page(param);
            List<RemindPlanRetryTaskRespBO.DetailBO> pageRecords = page.getRecords();
            if (CollectionUtils.isEmpty(pageRecords)) {
                break;
            }
            minId = pageRecords.get(pageRecords.size() - 1).getId();
            //处理重试任务
            handleRetryTask(pageRecords);
            log.info("执行生成提醒计划异常重试任务，pageCurrent:{}，pageSize:{}", page.getCurrent(), page.getSize());
        }
        log.info("执行生成提醒计划异常重试任务，结束:{}", LocalDateTime.now());
    }

    /**
     * 处理重试任务
     * @param pageRecords
     */
    public void handleRetryTask(List<RemindPlanRetryTaskRespBO.DetailBO> pageRecords) {
        List<RemindPlanRetryTaskReqBO.UpdateBO> updateBOList = new ArrayList<>();
        for (RemindPlanRetryTaskRespBO.DetailBO record : pageRecords) {
            if (Objects.equals(record.getServiceType(), BizTypeEnum.DELIVERY_PRODUCT.getType())) {
                RemindPlanRetryTaskReqBO.UpdateBO updateBO = retryGenerateRemindPlan(record);
                updateBOList.add(updateBO);
            }
        }
        //更新状态
        retryTaskDomainService.batchUpdate(updateBOList);
    }

    /**
     * 重试生成提醒计划
     * @param record
     * @return
     */
    public RemindPlanRetryTaskReqBO.UpdateBO retryGenerateRemindPlan(RemindPlanRetryTaskRespBO.DetailBO record) {
        RemindPlanRetryTaskReqBO.UpdateBO updateBO = new RemindPlanRetryTaskReqBO.UpdateBO();
        updateBO.setId(record.getId());
        updateBO.setRetryTimes(record.getRetryTimes() + 1);
        try {
            deliveryProductAppService.retryGenerateRemindPlan(record.getBizId());
            updateBO.setStatus(1);
        } catch (Exception e) {
            //最多重试一天
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime lastDateTime = TimeUtils.addSpecificTime(record.getCreateTime(), 24, 0, 0);
            if (now.isAfter(lastDateTime)) {
                updateBO.setStatus(2);
            } else {
                updateBO.setStatus(0);
            }
        }
        return updateBO;
    }

}
