package com.pxb7.mall.workorder.domain.model;

import java.time.*;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.ToString;


/**
 * 提醒服务预警计划(RemindPlan)实体类
 *
 * <AUTHOR>
 * @since 2025-03-24 21:11:14
 */
public class RemindPlanRespBO {


    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class DetailBO {
        private Long id;
        private String planName;
        private Integer serviceType;
        private NotDisturbPeriodBO notDisturbPeriod;
        private LocalDateTime createTime;
        private LocalDateTime updateTime;
        private String createUserId;
        private String updateUserId;
        private Boolean deleted;
    }
}

