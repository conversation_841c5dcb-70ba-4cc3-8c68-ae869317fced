package com.pxb7.mall.workorder.app.mapping;

import com.pxb7.mall.workorder.app.model.*;
import com.pxb7.mall.workorder.domain.model.*;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;


@Mapper
public interface RemindDeliveryProductAppMapping {

    RemindDeliveryProductAppMapping INSTANCE = Mappers.getMapper(RemindDeliveryProductAppMapping.class);


    RemindDeliveryProductReqBO.AddBO remindDeliveryProductDTO2AddBO(RemindDeliveryProductReqDTO.AddDTO source);

    RemindDeliveryProductReqBO.UpdateBO remindDeliveryProductDTO2UpdateBO(RemindDeliveryProductReqDTO.UpdateDTO source);

    RemindDeliveryProductReqBO.DelBO remindDeliveryProductDTO2DelBO(RemindDeliveryProductReqDTO.DelDTO source);

    RemindDeliveryProductReqBO.SearchBO remindDeliveryProductDTO2SearchBO(RemindDeliveryProductReqDTO.SearchDTO source);

    RemindDeliveryProductReqBO.PageBO remindDeliveryProductDTO2PageBO(RemindDeliveryProductReqDTO.PageDTO source);

    RemindDeliveryProductRespDTO.DetailDTO remindDeliveryProductBO2DetailDTO(RemindDeliveryProductRespBO.DetailBO source);

    List<RemindDeliveryProductRespDTO.DetailDTO> remindDeliveryProductBO2ListDTO(List<RemindDeliveryProductRespBO.DetailBO> source);

    Page<DeliveryProductPageDetailDTO> remindDeliveryProductBO2PageDTO(Page<RemindDeliveryProductBO> source);

    DeliveryProductStatisticDataSearchBO statisticParam2BO(DeliveryProductStatisticDataSearchDTO param);

    List<DeliveryProductStatisticDataDTO> statisticResul2DTOList(List<DeliveryProductStatisticDataBO> list);
}


