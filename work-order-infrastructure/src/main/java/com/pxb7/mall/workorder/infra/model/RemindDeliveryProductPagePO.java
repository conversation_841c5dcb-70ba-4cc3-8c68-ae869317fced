package com.pxb7.mall.workorder.infra.model;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@ToString
public class RemindDeliveryProductPagePO {

    /**
     * 商品id
     */
    private String productId;

    /**
     * 商品编码
     */
    private String productCode;

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 订单明细号
     */
    private String orderItemId;

    /**
     * 群组ID （房间ID）
     */
    private String groupId;

    /**
     * 交付客服
     */
    private String deliveryCustomerCare;



    /**
     * 页码，从1开始
     */
    private Integer pageIndex;
    /**
     * 每页数量
     */
    private Integer pageSize;
}
