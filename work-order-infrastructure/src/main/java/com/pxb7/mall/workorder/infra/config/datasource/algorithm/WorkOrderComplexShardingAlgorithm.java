package com.pxb7.mall.workorder.infra.config.datasource.algorithm;

import com.alibaba.fastjson2.JSON;
import org.apache.commons.lang3.StringUtils;
import org.apache.shardingsphere.infra.exception.core.ShardingSpherePreconditions;
import org.apache.shardingsphere.infra.exception.generic.UnsupportedSQLOperationException;
import org.apache.shardingsphere.infra.spi.annotation.SingletonSPI;
import org.apache.shardingsphere.sharding.api.sharding.complex.ComplexKeysShardingAlgorithm;
import org.apache.shardingsphere.sharding.api.sharding.complex.ComplexKeysShardingValue;
import org.springframework.util.CollectionUtils;

import java.util.*;

@SingletonSPI
public class WorkOrderComplexShardingAlgorithm implements ComplexKeysShardingAlgorithm<Comparable<?>> {

    private static final int SPILT_NUM = 4;

    private static final String DB_NAME_KEY = "DB_NAME";

    private String dbName = "readwrite_ds";

    private static final String ALLOW_RANGE_QUERY_KEY = "allow-range-query-with-inline-sharding";

    private boolean allowRangeQuery;

    @Override
    public Collection<String> doSharding(Collection availableTargetNames, ComplexKeysShardingValue shardingValue) {
        // 1. 检查分片键值
        boolean shardingValueExists = CollectionUtils.isEmpty(shardingValue.getColumnNameAndShardingValuesMap());
        ShardingSpherePreconditions.checkState(!shardingValueExists, () -> new UnsupportedSQLOperationException("没有找到分区键的值, 会进行全库路由, 同时目前不支持分区键的范围查找，分片键为空，属性:" + JSON.toJSONString(shardingValue.getColumnNameAndShardingValuesMap())));

        Set<String> resultSet = new HashSet<>();

        // 2. 获取分片键、值
        Map<String, Collection<Comparable<?>>> columnNameAndShardingValuesMap = shardingValue.getColumnNameAndShardingValuesMap();

        int dbSize = availableTargetNames.size();

        // 这是范围的先不处理
//        Map columnNameAndRangeValuesMap = shardingValue.getColumnNameAndRangeValuesMap();
        columnNameAndShardingValuesMap.keySet().forEach(key -> {
            Collection<Comparable<?>> shardingValues = columnNameAndShardingValuesMap.get(key);

            if (!CollectionUtils.isEmpty(shardingValues)) {
                // 3. 获取分片键值, 可能是map, 可能是list
                shardingValues.stream().filter(Objects::nonNull)
                        .map(String::valueOf)
                        .filter(StringUtils::isNotBlank)
                        .filter(x -> !Objects.equals("0", x))  //有的id默认会是0
                        .forEach(resStr -> {

                            //  截取需要的数据
//                            String lastFourDigits = resStr.length() >= SPILT_NUM ? resStr.substring(resStr.length() - SPILT_NUM) : resStr;

//                            int resNumber = Integer.parseInt(lastFourDigits);

                            // 为啥要这样做, 1.0的 roomId后四位有可能不是纯数字
                            int resNumber = getLastFourDigits(resStr);

                            // Step 3: 进行 取模操作
                            String resultDB = dbName + (resNumber % dbSize);

                            if (availableTargetNames.contains(resultDB)) {
                                resultSet.add(resultDB);
                            }
                        });
            }
        });

        ShardingSpherePreconditions.checkState(!CollectionUtils.isEmpty(resultSet), () -> new UnsupportedSQLOperationException("没有找到分区键的值, 无法对应具体的数据源"));

        return resultSet;
    }


    @Override
    public void init(Properties props) {
        ComplexKeysShardingAlgorithm.super.init(props);
        this.dbName = this.getAlgorithmDBNAME(props);
        this.allowRangeQuery = this.isAllowRangeQuery(props);
    }

    // 数据源的前缀
    private String getAlgorithmDBNAME(Properties props) {
        String dbName = props.getProperty(DB_NAME_KEY);
        if (Objects.isNull(dbName)) {
            throw new IllegalArgumentException("自定义类的复合分片算法的DB_NUM必须要配置");
        }
        return dbName;
    }


    private boolean isAllowRangeQuery(Properties props) {
        return Boolean.parseBoolean(props.getOrDefault(ALLOW_RANGE_QUERY_KEY, Boolean.FALSE.toString()).toString());
    }

    /**
     * 获取字符串中最后4个数字组成的数值（忽略末尾的非数字字符）
     *
     * @param input 输入字符串
     * @return 最后4个数字组成的数值，如果没有数字则返回0
     */
    public static int getLastFourDigits(String input) {
        if (input == null || input.isBlank()) {
            return 0;
        }

        String lastFourDigits = input.length() >= SPILT_NUM ? input.substring(input.length() - SPILT_NUM) : input;

        int result = 0;
        int multiplier = 1;  // 位数乘数

        // 从最后一个数字开始向前遍历
        for (int i = lastFourDigits.length() -1; i >= 0 ; i--) {
            char ch = lastFourDigits.charAt(i);
            if (ch >= '0' && ch <= '9') {
                result += ch * multiplier;
                multiplier *= 10;
            }
        }


        return result;
    }

}
