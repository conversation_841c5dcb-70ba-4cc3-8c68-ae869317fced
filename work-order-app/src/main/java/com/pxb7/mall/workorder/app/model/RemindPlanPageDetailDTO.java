package com.pxb7.mall.workorder.app.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
@Accessors(chain = true)
@ToString
public class RemindPlanPageDetailDTO {

    /**
     * 预警计划Id
     */
    private Long id;

    /**
     * 预警计划名称
     */
    private String planName;

    /**
     * 服务类型: 1：账号交付 2:商品工单 3:售后工单 4:客诉工单
     */
    private Integer serviceType;

    /**
     * 工单类型，1:找回，2:纠纷，服务类型为售后工单有值
     */
    private Integer workOrderType;

    /**
     * 投诉级别：1:一级，2:二级，3:三级，4:四级，5:五级，6:六级
     */
    private Integer complaintLevel;

    /**
     * 业务类型，1:代售，2:中介，服务类型为账号交付有值
     */
    private List<Integer> businessTypes;

    /**
     *  工单状态：1:待接单，2:已接单，3:待跟进，服务类型为商品工单有值
     */
    private List<Integer> workOrderStatuses;

    /**
     * 上架方式：1:官方截图，2:自主截图，服务类型为商品工单且 不为“待接单”时有值
     */
    private List<Integer> onShelfTypes;

    /**
     * 游戏厂商名称列表
     */
    private List<Integer> makerNames;

    /**
     * 游戏名称列表
     */
    private List<String> gameNames;

    /**
     * 最新编辑人Id
     */
    private String updateUserId;

    /**
     * 最新编辑人名称
     */
    private String updateUserName;

    /**
     *  最近编辑时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;


}
