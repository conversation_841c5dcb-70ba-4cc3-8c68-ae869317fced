package com.pxb7.mall.workorder.infra.repository.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pxb7.mall.workorder.infra.repository.db.entity.BargainAcceptanceCustomer;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 客服接单信息表(BargainAcceptanceCustomer)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-04-18 17:22:02
 */
@Mapper
public interface BargainAcceptanceCustomerMapper extends BaseMapper<BargainAcceptanceCustomer> {
    /**
     * 批量新增数据
     *
     * @param entities List<BargainAcceptanceCustomer> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<BargainAcceptanceCustomer> entities);

}

