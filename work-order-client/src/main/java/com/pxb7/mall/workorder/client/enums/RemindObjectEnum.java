package com.pxb7.mall.workorder.client.enums;

import lombok.Getter;
import lombok.ToString;

/**
 * 提醒对象
 * <AUTHOR>
 */
@Getter
@ToString
public enum RemindObjectEnum {

    /**
     * 提醒对象
     */
    CUSTOMER_CARE(Constants.CUSTOMER_CARE, "交付客服"),
    FEISHU_GROUP(Constants.FEISHU_GROUP, "飞书群组"),
    ART_DESIGNER(Constants.ART_DESIGNER, "美工"),
    AUDIT_CUSTOMER_CARE(Constants.AUDIT_CUSTOMER_CARE, "审核客服"),
    FOLLOWER(Constants.FOLLOWER, "跟进人"),
    CUSTOM(Constants.CUSTOM, "自定义");


    private final String code;
    private final String desc;

    RemindObjectEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static RemindObjectEnum getByCode(String code) {
        for (RemindObjectEnum value : RemindObjectEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static class Constants {
        public static final String CUSTOMER_CARE = "customerCare";
        public static final String FEISHU_GROUP = "group";
        public static final String ART_DESIGNER = "artDesigner";
        public static final String AUDIT_CUSTOMER_CARE = "auditCustomerCare";
        public static final String FOLLOWER = "follower";
        public static final String CUSTOM = "custom";

    }

}
