package com.pxb7.mall.workorder.infra.mapping;

import com.pxb7.mall.workorder.infra.model.RemindAfterSaleReqPO;
import com.pxb7.mall.workorder.infra.repository.db.entity.RemindAfterSale;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2025/4/3 17:34
 */

@Mapper
public interface AfterSaleMapping {

    AfterSaleMapping INSTANCE = Mappers.getMapper(AfterSaleMapping.class);

    RemindAfterSale addDto2PO(RemindAfterSaleReqPO.AddPO source);

}
