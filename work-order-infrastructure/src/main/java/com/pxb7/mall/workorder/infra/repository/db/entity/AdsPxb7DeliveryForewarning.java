package com.pxb7.mall.workorder.infra.repository.db.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.Date;
import java.io.Serializable;

/**
 * 账号交付预警统计(AdsPxb7DeliveryForewarning)实体类
 *
 * <AUTHOR>
 * @since 2025-04-17 22:35:21
 */

@Data
@Accessors(chain = true)
@TableName(value = "ads_pxb7_delivery_forewarning")
@ToString
public class AdsPxb7DeliveryForewarning implements Serializable {

    private static final long serialVersionUID = -40036042274146376L;
    /**
     * 主键
     */
    private Long id;
    /**
     * 业务类型，1:代售，2:中介
     */
    private Integer businessType;
    /**
     * 创建交付日期
     */
    private String createDate;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 创建人
     */
    private String createUserId;
    /**
     * 交付客服
     */
    private String deliveryCustomerCare;
    /**
     * 交付记录数
     */
    private Long deliveryRemindCnt;
    /**
     * 交付记录已完结数
     */
    private Long deliveryRemindFinishCnt;
    /**
     * 交付即将超时记录数
     */
    private Long deliveryRemindTimeoutCnt;
    /**
     * 交付已超时记录数
     */
    private Long deliveryRemindTimeoutFinishCnt;
    /**
     * 交付中记录数
     */
    private Long deliveryRemindUnfinishedCnt;
    /**
     * 游戏id
     */
    private String gameId;
    /**
     * 是否删除 0:否, 1:是
     */
    private Integer isDeleted;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 更新人
     */
    private String updateUserId;

    public void setId(Long id) {
        this.id = id;
    }

    public void setBusinessType(Integer businessType) {
        this.businessType = businessType;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId;
    }

    public void setDeliveryCustomerCare(String deliveryCustomerCare) {
        this.deliveryCustomerCare = deliveryCustomerCare;
    }

    public void setDeliveryRemindCnt(Long deliveryRemindCnt) {
        this.deliveryRemindCnt = deliveryRemindCnt;
    }

    public void setDeliveryRemindFinishCnt(Long deliveryRemindFinishCnt) {
        this.deliveryRemindFinishCnt = deliveryRemindFinishCnt;
    }

    public void setDeliveryRemindTimeoutCnt(Long deliveryRemindTimeoutCnt) {
        this.deliveryRemindTimeoutCnt = deliveryRemindTimeoutCnt;
    }

    public void setDeliveryRemindTimeoutFinishCnt(Long deliveryRemindTimeoutFinishCnt) {
        this.deliveryRemindTimeoutFinishCnt = deliveryRemindTimeoutFinishCnt;
    }

    public void setDeliveryRemindUnfinishedCnt(Long deliveryRemindUnfinishedCnt) {
        this.deliveryRemindUnfinishedCnt = deliveryRemindUnfinishedCnt;
    }

    public void setGameId(String gameId) {
        this.gameId = gameId;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public void setUpdateUserId(String updateUserId) {
        this.updateUserId = updateUserId;
    }

}

