package com.pxb7.mall.workorder.infra.repository.db.mapper;

import com.pxb7.mall.workorder.infra.repository.db.entity.RemindAfterSale;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 售后工单预警记录(RemindAfterSale)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-04-24 23:31:56
 */
@Mapper
public interface RemindAfterSaleMapper extends BaseMapper<RemindAfterSale> {
    /**
     * 批量新增数据
     *
     * @param entities List<RemindAfterSale> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<RemindAfterSale> entities);

    /**
     * 批量修改超时状态
     * @param ids
     * @param status
     */
    int batchUpdateTimeoutStatusByIds(List<Long> ids, Integer status);

}

