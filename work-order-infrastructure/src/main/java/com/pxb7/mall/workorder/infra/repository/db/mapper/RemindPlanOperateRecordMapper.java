package com.pxb7.mall.workorder.infra.repository.db.mapper;

import com.pxb7.mall.workorder.infra.repository.db.entity.RemindPlanOperateRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 预警计划操作记录表(RemindPlanOperateRecord)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-03-31 20:36:21
 */
@Mapper
public interface RemindPlanOperateRecordMapper extends BaseMapper<RemindPlanOperateRecord> {
    /**
     * 批量新增数据
     *
     * @param entities List<RemindPlanOperateRecord> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<RemindPlanOperateRecord> entities);

}

