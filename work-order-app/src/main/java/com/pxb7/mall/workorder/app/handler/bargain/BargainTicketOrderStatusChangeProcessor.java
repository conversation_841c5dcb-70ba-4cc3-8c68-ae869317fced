package com.pxb7.mall.workorder.app.handler.bargain;

import com.pxb7.mall.workorder.client.enums.BargainTicketTradeStatusFlagEnum;
import com.pxb7.mall.workorder.domain.message.BargainTicketMessage;
import com.pxb7.mall.workorder.domain.model.BargainTicketCacheBO;
import com.pxb7.mall.workorder.infra.repository.db.entity.BargainAcceptanceCustomer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version : BargainInfoChangeProcessor.java, v 0.1 2025年04月21日 16:26 yang.xuexi Exp $
 */
@Component("BARGAIN_TICKET_ORDER_STATUS_CHANGE")
@Slf4j
public class BargainTicketOrderStatusChangeProcessor extends AbstractBargainTicketSyncProcessor {

    @Override
    protected void doProcess(BargainTicketMessage message, BargainAcceptanceCustomer byReceiveId) {
        //查询一下交易
        BargainTicketTradeStatusFlagEnum tradeStatusFlagEnum = bargainTicketDomainService.checkUserIfDealToday(byReceiveId.getBuyerUserId()) ?
                BargainTicketTradeStatusFlagEnum.DEAL : BargainTicketTradeStatusFlagEnum.NONE;
        bargainTicketDocEsRepository.updateTradeFlag(byReceiveId.getReceiveId(), tradeStatusFlagEnum.getCode());
    }
}
