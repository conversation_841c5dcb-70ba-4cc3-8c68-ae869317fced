package com.pxb7.mall.workorder.adapter.consumer;

import com.alibaba.fastjson.JSON;
import com.pxb7.mall.components.rocketmq.RocketMQListenerExt;
import com.pxb7.mall.product.client.enums.OptLogMsgTagEnum;
import com.pxb7.mall.workorder.app.service.RemindWorkOrderAppService;
import com.pxb7.mall.workorder.domain.message.ProductEventMessage;
import com.pxb7.mall.workorder.infra.constant.RMQConstant;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.annotation.RocketMQMessageListener;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 监听工单状态消息
 * <AUTHOR>
 */
@Slf4j
@Service
@RocketMQMessageListener(
        consumerGroup = RMQConstant.PRODUCT_EVENT_GROUP,
        topic = RMQConstant.PRODUCT_EVENT_TOPIC,
        tag = RMQConstant.PRODUCT_WORK_ORDER_TAG)
public class WorkOrderStatusMessageConsumer implements RocketMQListenerExt<ProductEventMessage> {

    @Resource
    private RemindWorkOrderAppService remindWorkOrderAppService;

    @Override
    public ConsumeResult consume(MessageView messageView, ProductEventMessage productEventMessage) {
        log.info("监听到工单状态变更消息: messageId = {},message = {}", messageView.getMessageId(), JSON.toJSONString(productEventMessage));
        String workOrderId = productEventMessage.getWorkOrderId();
        String optLogMsgTag = productEventMessage.getOptLogMsgTag();
        try {
            if (StringUtils.isBlank(workOrderId)) {
                return ConsumeResult.SUCCESS;
            }
            if (!Objects.equals(OptLogMsgTagEnum.PUB.getTag(), optLogMsgTag)
                    && !Objects.equals(OptLogMsgTagEnum.WORK_ORDER.getTag(), optLogMsgTag)) {
                return ConsumeResult.SUCCESS;
            }
            //生成预警执行计划
            remindWorkOrderAppService.generateRemindPlan(workOrderId);
            return ConsumeResult.SUCCESS;
        } catch (Exception e) {
            log.error("error in consumed from product_event_topic: orderId = {}", workOrderId, e);
            return ConsumeResult.FAILURE;
        }
    }

}

