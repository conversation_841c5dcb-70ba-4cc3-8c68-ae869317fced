package com.pxb7.mall.workorder.app.model;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.List;

@Getter
@Setter
@Accessors(chain = true)
@ToString
public class ComplaintStatisticDataSearchDTO {

    /**
     * 起始时间 精确到天
     */
    // @JsonFormat(pattern = "yyyy-MM-dd")
    // @NotNull(message = "起始时间不能为空")
    private String startDate;

    /**
     * 结束时间 精确到天
     */
    // @JsonFormat(pattern = "yyyy-MM-dd")
    // @NotNull(message = "结束时间不能为空")
    private String endDate;

    /**
     * 投诉渠道 1:IM 2支付宝 3闲鱼 4:12315 5消费宝 6连连支付 7电话 8反诈邮箱 9外部门升级 10黑猫投诉 11工商局 12工信部
     */
    private List<Integer> channels;

    /**
     * 客诉等级1-6级 列表
     */
    private List<Integer> complaintLevels;

    /**
     * 处理人ID 列表
     */
    private List<String> handleUserIds;

}
