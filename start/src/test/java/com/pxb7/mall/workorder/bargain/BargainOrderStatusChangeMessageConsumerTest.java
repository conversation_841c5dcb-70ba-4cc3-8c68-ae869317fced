package com.pxb7.mall.workorder.bargain;

import com.pxb7.mall.trade.order.client.message.OrderItemStatusChangeMQDTO;
import com.pxb7.mall.workorder.adapter.consumer.bargain.BargainOrderStatusChangeMessageConsumer;
import com.pxb7.mall.workorder.app.service.BargainTicketAppService;
import com.pxb7.mall.workorder.domain.model.BargainTicketCacheBO;
import com.pxb7.mall.workorder.infra.enums.OrderItemStatusEnum;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.message.MessageId;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@SpringBootTest
public class BargainOrderStatusChangeMessageConsumerTest {

    @InjectMocks
    private BargainOrderStatusChangeMessageConsumer consumer;

    @Mock
    private BargainTicketAppService bargainTicketAppService;

    @BeforeEach
    void setUp() {
        // 初始化注入的字段（如需要）
        ReflectionTestUtils.setField(consumer, "bargainTicketAppService", bargainTicketAppService);
    }

    @Test
    void testConsume_InvalidOrderId_ReturnsSuccess() {
        MessageView messageView = mock(MessageView.class);
        OrderItemStatusChangeMQDTO dto = new OrderItemStatusChangeMQDTO();
        dto.setOrderId(null); // 无效订单ID
        dto.setOrderItemId("123456");
        dto.setBuyerId("buyer123");

        ConsumeResult result = consumer.consume(messageView, dto);

        assertEquals(ConsumeResult.SUCCESS, result);
    }

    @Test
    void testConsume_InitOrderStatus_ReturnsSuccess() {
        MessageView messageView = mock(MessageView.class);
        OrderItemStatusChangeMQDTO dto = new OrderItemStatusChangeMQDTO();
        dto.setOrderId("order123");
        dto.setOrderItemId("item123");
        dto.setBuyerId("buyer123");
        dto.setOrderItemStatus(OrderItemStatusEnum.INIT.getValue()); // INIT 状态

        ConsumeResult result = consumer.consume(messageView, dto);

        assertEquals(ConsumeResult.SUCCESS, result);
    }

    @Test
    void testConsume_ValidMessage_ProcessesSuccessfully() {
        MessageView messageView = mock(MessageView.class);
        MessageId messageId = mock(MessageId.class);
        when(messageView.getMessageId()).thenReturn(messageId);

        OrderItemStatusChangeMQDTO dto = new OrderItemStatusChangeMQDTO();
        dto.setOrderId("order123");
        dto.setOrderItemId("item123");
        dto.setBuyerId("buyer123");
        dto.setOrderItemStatus(OrderItemStatusEnum.DEALING.getValue()); // 非 INIT/WAIT_PAY 状态

        BargainTicketCacheBO cacheBO = new BargainTicketCacheBO();
        cacheBO.setReceived("received123");

        when(bargainTicketAppService.getCacheReceived(any())).thenReturn("received123");

        ConsumeResult result = consumer.consume(messageView, dto);

        assertEquals(ConsumeResult.SUCCESS, result);
        verify(bargainTicketAppService, times(1)).syncBargainTicket(any());
    }

    @Test
    void testConsume_NullCache_ReturnsSuccess() {
        MessageView messageView = mock(MessageView.class);
        OrderItemStatusChangeMQDTO dto = new OrderItemStatusChangeMQDTO();
        dto.setOrderId("order123");
        dto.setOrderItemId("item123");
        dto.setBuyerId("buyer123");
        dto.setOrderItemStatus(OrderItemStatusEnum.DEALING.getValue());


        ConsumeResult result = consumer.consume(messageView, dto);

        assertEquals(ConsumeResult.SUCCESS, result);
    }

    @Test
    void testConsume_ExceptionThrown_LogsErrorAndReturnsFailure() {
        MessageView messageView = mock(MessageView.class);
        OrderItemStatusChangeMQDTO dto = new OrderItemStatusChangeMQDTO();
        dto.setOrderId("order123");
        dto.setOrderItemId("item123");
        dto.setBuyerId("buyer123");
        dto.setOrderItemStatus(OrderItemStatusEnum.DEALING.getValue());


        ConsumeResult result = consumer.consume(messageView, dto);

        assertEquals(ConsumeResult.SUCCESS, result);
    }
}
