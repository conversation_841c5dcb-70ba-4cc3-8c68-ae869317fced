package com.pxb7.mall.workorder.adapter.consumer.bargain;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.pxb7.mall.components.rocketmq.RocketMQListenerExt;
import com.pxb7.mall.trade.order.client.message.OrderItemStatusChangeMQDTO;
import com.pxb7.mall.workorder.app.service.BargainTicketAppService;
import com.pxb7.mall.workorder.client.enums.BargainTicketEventEnum;
import com.pxb7.mall.workorder.domain.message.BargainTicketMessage;
import com.pxb7.mall.workorder.infra.constant.RMQConstant;
import com.pxb7.mall.workorder.infra.enums.OrderItemStatusEnum;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.annotation.RocketMQMessageListener;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.message.MessageId;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RocketMQMessageListener(
        consumerGroup = RMQConstant.BARGAIN_TICKET_ORDER_GROUP,
        topic = RMQConstant.ORDER_STATUS_CHANGE_TOPIC,
        tag = RMQConstant.ALL_TAG)
public class BargainOrderStatusChangeMessageConsumer implements RocketMQListenerExt<OrderItemStatusChangeMQDTO> {


    @Resource
    private BargainTicketAppService bargainTicketAppService;

    @Override
    public ConsumeResult consume(MessageView messageView, OrderItemStatusChangeMQDTO orderStatusChangeDTO) {
        MessageId messageId = messageView.getMessageId();
        String orderId = orderStatusChangeDTO.getOrderId();
        String orderItemId = orderStatusChangeDTO.getOrderItemId();
        try {
            if (StringUtils.isBlank(orderId) || StringUtils.isBlank(orderItemId) || StrUtil.isBlank(orderStatusChangeDTO.getBuyerId())) {
                return ConsumeResult.SUCCESS;
            }
            if (OrderItemStatusEnum.INIT.eq(orderStatusChangeDTO.getOrderItemStatus())
                    || OrderItemStatusEnum.WAIT_PAY.eq(orderStatusChangeDTO.getOrderItemStatus())) {
                return ConsumeResult.SUCCESS;
            }
            log.info("还价监听到订单状态变更消息: messageId = {},message = {}", messageId, JSON.toJSONString(orderStatusChangeDTO));

            String buyerId = orderStatusChangeDTO.getBuyerId();

            String received = bargainTicketAppService.getCacheReceived(buyerId);
            if (StrUtil.isBlank(received)) {
                return ConsumeResult.SUCCESS;
            }

            bargainTicketAppService.syncBargainTicket(new BargainTicketMessage()
                    .setReceiveId(received)
                    .setEventType(BargainTicketEventEnum.BARGAIN_TICKET_ORDER_STATUS_CHANGE.getCode())
            );


            return ConsumeResult.SUCCESS;
        } catch (Exception e) {
            log.warn("bargain error in consumed from order_status_change_topic: orderId = {}", orderId, e);
            return ConsumeResult.SUCCESS;
        }
    }

}

