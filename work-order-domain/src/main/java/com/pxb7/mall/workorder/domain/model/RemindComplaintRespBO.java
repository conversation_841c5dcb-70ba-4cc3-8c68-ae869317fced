package com.pxb7.mall.workorder.domain.model;

import java.util.Date;
import java.time.*;
import java.math.BigDecimal;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.ToString;


/**
 * 客诉工单预警记录(RemindComplaint)实体类
 *
 * <AUTHOR>
 * @since 2025-04-24 23:32:52
 */
public class RemindComplaintRespBO {


    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class DetailBO {
        private Long id;
        private String remindId;
        private String workOrderId;
        private String workOrderTitle;
        private String complaintContent;
        private String groupId;
        private Integer completeStatus;
        private Integer timeOutStatus;
        private String handleUserId;
        private LocalDateTime expectCompleteTime;
        private LocalDateTime completeTime;
        private Long gameConfigId;
        private Long remindPlanId;
        private Long remindSubPlanId;
        private LocalDateTime createTime;
        private LocalDateTime updateTime;
        private String createUserId;
        private String updateUserId;
        private Boolean deleted;
    }
}

