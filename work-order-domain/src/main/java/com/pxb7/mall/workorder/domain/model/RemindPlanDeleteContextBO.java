package com.pxb7.mall.workorder.domain.model;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.List;

@Getter
@Setter
@Accessors(chain = true)
@ToString
public class RemindPlanDeleteContextBO {


    private Long remindPlanId;


    private List<Long> remindSubPlanIds;


    private List<Long> remindPlanGameConfigIds;


    private List<Long> remindPlanRuleIds;

    private List<RemindPlanOperateRecordBO> operateRecords;
}
