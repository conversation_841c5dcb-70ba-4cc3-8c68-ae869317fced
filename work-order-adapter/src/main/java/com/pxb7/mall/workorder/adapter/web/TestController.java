package com.pxb7.mall.workorder.adapter.web;

import com.alibaba.cola.dto.SingleResponse;
import com.pxb7.mall.trade.ofs.delivery.client.dto.model.DeliveryStatusChangeDTO;
import com.pxb7.mall.trade.order.client.message.OrderItemStatusChangeMQDTO;
import com.pxb7.mall.workorder.adapter.consumer.RemindPlanMessageConsumer;
import com.pxb7.mall.workorder.adapter.schedule.RemindExecuteTask;
import com.pxb7.mall.workorder.adapter.schedule.RemindTimeoutTask;
import com.pxb7.mall.workorder.app.service.RemindAfterSaleAppService;
import com.pxb7.mall.workorder.app.service.RemindComplaintAppService;
import com.pxb7.mall.workorder.app.service.RemindDeliveryProductAppService;
import com.pxb7.mall.workorder.app.service.RemindPlanRetryTaskAppService;
import com.pxb7.mall.workorder.app.service.RemindWorkOrderAppService;
import com.pxb7.mall.workorder.client.enums.DeliveryStatusEnum;
import com.pxb7.mall.workorder.client.message.RemindPlanMessage;
import com.pxb7.mall.workorder.domain.message.AssWorkOrderMessage;
import com.pxb7.mall.workorder.domain.message.ProductEventMessage;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2025/4/11 16:16
 */

@RestController
@RequestMapping("/web/test/")
public class TestController {

    @Resource
    private RemindExecuteTask remindExecuteTask;
    @Resource
    private RemindDeliveryProductAppService deliveryProductAppService;
    @Resource
    private RemindWorkOrderAppService workOrderAppService;
    @Resource
    private RemindAfterSaleAppService afterSaleAppService;
    @Resource
    private RemindComplaintAppService complaintAppService;
    @Resource
    private RemindTimeoutTask remindTimeoutTask;
    @Resource
    private RemindPlanMessageConsumer remindPlanMessageConsumer;

    @Resource
    private RemindPlanRetryTaskAppService retryTaskAppService;

    /**
     * 账号交付生成预警计划
     */
    @PostMapping("delivery-product/generate-remind-plan")
    public SingleResponse<Boolean> generateRemindPlan(@RequestBody OrderItemStatusChangeMQDTO orderStatusChangeDTO) {
        //生成预警执行计划
        deliveryProductAppService.generateRemindPlan(orderStatusChangeDTO);
        return SingleResponse.of(Boolean.TRUE);
    }

    /**
     * 商品工单生成预警计划
     */
    @PostMapping("work-order/generate-remind-plan")
    public SingleResponse<Boolean> generateRemindPlan(@RequestBody ProductEventMessage productEventMessage) {
        //生成预警执行计划
        workOrderAppService.generateRemindPlan(productEventMessage.getWorkOrderId());
        return SingleResponse.of(Boolean.TRUE);
    }

    /**
     * 售后工单(找回,纠纷)生成预警计划
     */
    @PostMapping("ass-work/generate-remind-plan")
    public SingleResponse<Boolean> dealAfterSaleWorkOrder(@RequestBody AssWorkOrderMessage workOrderMessage) {
        //生成预警执行计划
        afterSaleAppService.dealAfterSaleWorkOrder(workOrderMessage.getWorkOrderId(), workOrderMessage.getWorkOrderType());
        return SingleResponse.of(Boolean.TRUE);
    }

    /**
     * 客诉工单生成预警计划
     */
    @PostMapping("complaint-work/generate-remind-plan")
    public SingleResponse<Boolean> dealComplaintWorkOrder(@RequestBody AssWorkOrderMessage workOrderMessage) {
        //生成预警执行计划
        complaintAppService.dealComplaintWorkOrder(workOrderMessage.getWorkOrderId());
        return SingleResponse.of(Boolean.TRUE);
    }

    /**
     * 预警计划执行任务
     */
    @PostMapping("remind-task/execute")
    public SingleResponse<Boolean> processRemindTask() throws Exception {
        remindExecuteTask.process(null);
        return SingleResponse.of(Boolean.TRUE);
    }

    /**
     * 消费预警计划消息
     */
    @PostMapping("remind-task/message-consume")
    public SingleResponse<Boolean> processRemindTask(@RequestBody RemindPlanMessage remindPlanMessage) throws Exception {
        remindPlanMessageConsumer.consume(null, remindPlanMessage);
        return SingleResponse.of(Boolean.TRUE);
    }

    /**
     * 预警任务超时状态更新任务
     * @return
     * @throws Exception
     */
    @PostMapping("remind/timeout/execute")
    public SingleResponse<Boolean> processTimoutTask() throws Exception {
        remindTimeoutTask.process(null);
        return SingleResponse.of(Boolean.TRUE);
    }

    /**
     * 生成提醒计划异常重试
     * @return
     * @throws Exception
     */
    @PostMapping("remind-plan/retry/execute")
    public SingleResponse<Boolean> executeRetryTask() throws Exception {
        retryTaskAppService.executeRetryTask();
        return SingleResponse.of(Boolean.TRUE);
    }
    /**
     * 测试交付状态变更
     * @return
     * @throws Exception
     */
    @PostMapping("test_delivery_status_change")
    public SingleResponse<Boolean> testDeliveryStatusChange() throws Exception {
        DeliveryStatusChangeDTO deliveryStatusChangeDTO = new DeliveryStatusChangeDTO();
        deliveryStatusChangeDTO.setOrderItemId("ZH16608331755528524193");
        deliveryStatusChangeDTO.setStatus(DeliveryStatusEnum.DELIVERY.getCode());
        deliveryStatusChangeDTO.setOperatorId("138518423126048");
        deliveryStatusChangeDTO.setVersion(3);
        deliveryProductAppService.changeRemindPlanStatusByDeliverStatusChange(deliveryStatusChangeDTO);
        return SingleResponse.of(Boolean.TRUE);
    }

}
