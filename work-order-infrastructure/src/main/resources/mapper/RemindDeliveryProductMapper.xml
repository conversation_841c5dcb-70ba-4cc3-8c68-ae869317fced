<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pxb7.mall.workorder.infra.repository.db.mapper.RemindDeliveryProductMapper">
    <resultMap id="BaseResultMap"
               type="com.pxb7.mall.workorder.infra.repository.db.entity.RemindDeliveryProduct">
        <result column="id" jdbcType="BIGINT" property="id"/>
        <result column="remind_id" jdbcType="VARCHAR" property="remindId"/>
        <result column="product_id" jdbcType="VARCHAR" property="productId"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="game_id" jdbcType="VARCHAR" property="gameId"/>
        <result column="group_id" jdbcType="VARCHAR" property="groupId"/>
        <result column="order_id" jdbcType="VARCHAR" property="orderId"/>
        <result column="order_item_id" jdbcType="VARCHAR" property="orderItemId"/>
        <result column="complete_status" jdbcType="INTEGER" property="completeStatus"/>
        <result column="time_out_status" jdbcType="INTEGER" property="timeOutStatus"/>
        <result column="delivery_customer_care" jdbcType="VARCHAR" property="deliveryCustomerCare"/>
        <result column="expect_complete_time" jdbcType="TIMESTAMP" property="expectCompleteTime"/>
        <result column="im_count_down_time" jdbcType="TIMESTAMP" property="imCountDownTime"/>
        <result column="game_config_id" jdbcType="BIGINT" property="gameConfigId"/>
        <result column="remind_plan_id" jdbcType="BIGINT" property="remindPlanId"/>
        <result column="remind_sub_plan_id" jdbcType="BIGINT" property="remindSubPlanId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_user_id" jdbcType="VARCHAR" property="createUserId"/>
        <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId"/>
        <result column="is_deleted" jdbcType="BOOLEAN" property="deleted"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into
        remind_delivery_product(remind_id,product_id,product_code,game_id,group_id,order_id,order_item_id,complete_status,time_out_status,delivery_customer_care,expect_complete_time,im_count_down_time,game_config_id,remind_plan_id,remind_sub_plan_id,create_user_id,update_user_id)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.remindId},#{entity.productId},#{entity.productCode},#{entity.gameId},#{entity.groupId},#{entity.orderId},#{entity.orderItemId},#{entity.completeStatus},#{entity.timeOutStatus},#{entity.deliveryCustomerCare},#{entity.expectCompleteTime},#{entity.imCountDownTime},#{entity.gameConfigId},#{entity.remindPlanId},#{entity.remindSubPlanId},#{entity.createUserId},#{entity.updateUserId})
        </foreach>
    </insert>

    <update id="batchUpdateTimeoutStatusByIds">
        update remind_delivery_product
        set time_out_status = #{status}
        where id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>
</mapper>

