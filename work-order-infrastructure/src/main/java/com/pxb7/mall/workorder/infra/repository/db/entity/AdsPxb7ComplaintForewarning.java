package com.pxb7.mall.workorder.infra.repository.db.entity;

import java.io.Serializable;
import java.time.*;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import com.baomidou.mybatisplus.annotation.*;

/**
 * 客诉预警统计(AdsPxb7ComplaintForewarning)实体类
 *
 * <AUTHOR>
 * @since 2025-05-08 18:15:03
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "ads_pxb7_complaint_forewarning")
public class AdsPxb7ComplaintForewarning implements Serializable {
    private static final long serialVersionUID = 146500688226333446L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 客诉工单创建日期
     */
    @TableField(value = "create_date")
    private String createDate;
    /**
     * 投诉渠道 1:IM 2支付宝 3闲鱼 4:12315 5消费宝 6连连支付 7电话 8反诈邮箱 9外部门升级 10黑猫投诉 11工商局 12工信部
     */
    @TableField(value = "channel")
    private Integer channel;
    /**
     * 处理人员id
     */
    @TableField(value = "handle_user_id")
    private String handleUserId;
    /**
     * 投诉级别: 1 一级,2 二级,3 三级,4 四级,5 五级,6 六级
     */
    @TableField(value = "complaint_level")
    private Integer complaintLevel;
    /**
     * 客诉工单数量
     */
    @TableField(value = "complaint_work_order_cnt")
    private Long complaintWorkOrderCnt;
    /**
     * 已完结工单数量
     */
    @TableField(value = "complaint_complete_work_order_cnt")
    private Long complaintCompleteWorkOrderCnt;
    /**
     * 处理中工单数量
     */
    @TableField(value = "complaint_processing_work_order_cnt")
    private Long complaintProcessingWorkOrderCnt;
    /**
     * 即将超时工单数量
     */
    @TableField(value = "complaint_timeouting_work_order_cnt")
    private Long complaintTimeoutingWorkOrderCnt;
    /**
     * 已超时工单数量
     */
    @TableField(value = "complaint_timeouted_work_order_cnt")
    private Long complaintTimeoutedWorkOrderCnt;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;
    /**
     * 创建人
     */
    @TableField(value = "create_user_id")
    private String createUserId;
    /**
     * 更新人
     */
    @TableField(value = "update_user_id")
    private String updateUserId;
    /**
     * 是否删除 0:否, 1:是
     */
    @TableLogic
    @TableField(value = "is_deleted")
    private Boolean deleted;

}
