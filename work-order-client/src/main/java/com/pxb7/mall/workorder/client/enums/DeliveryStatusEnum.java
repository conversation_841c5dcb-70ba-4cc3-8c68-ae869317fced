package com.pxb7.mall.workorder.client.enums;

import lombok.Getter;

@Getter
public enum DeliveryStatusEnum {
    // 交付状态 0: 交付中 1:交付完成 2:交付暂停 4:交付终止 5:交付完结(人工)
    NOT_DELIVERY(0, "未交付"),
    DELIVERY(1, "交付中"),
    PAUSE(2, "交付暂停"),
    TERMINATE(4, "交付终止"),
    FINISH(5, "交付完结(人工)"),
    ;

    private Integer code;
    private String desc;

    DeliveryStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }   
}
