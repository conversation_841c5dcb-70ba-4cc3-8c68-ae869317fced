package com.pxb7.mall.workorder.app.util;

import com.alibaba.cola.exception.Assert;
import com.alibaba.cola.exception.BizException;
import com.pxb7.mall.workorder.app.model.AssPlanGameConfigDTO;
import com.pxb7.mall.workorder.app.model.ComplaintPlanGameConfigDTO;
import com.pxb7.mall.workorder.app.model.PlanGameConfigDTO;
import com.pxb7.mall.workorder.app.model.PlanRuleDTO;
import com.pxb7.mall.workorder.app.model.TimeConfigDTO;
import com.pxb7.mall.workorder.infra.enums.ComplaintChannelEnum;
import org.apache.commons.lang3.StringUtils;

import java.util.Comparator;
import java.util.List;

import static com.pxb7.mall.workorder.client.enums.BizErrorCodeEnum.PARAM_ERROR;

public class PlanRuleOrGameConfigChecker {

    private static String gainNodeName(Integer nodeNumber) {
        if (nodeNumber == null) {
            return "";
        }
        switch (nodeNumber) {
            case 11:
                return "完结时间之前（第1次）";
            case 12:
                return "完结时间之前（第2次）";
            case 13:
                return "完结时间之前（第3次）";
            case 14:
                return "完结时间之前（第4次）";
            case 15:
                return "完结时间之前（第5次）";
            case 16:
                return "完结时间之前（第6次）";
            case 17:
                return "完结时间之前（第7次）";
            case 18:
                return "完结时间之前（第8次）";
            case 19:
                return "完结时间之前（第9次）";
            case 21:
                return "完结时间之后（第1次）";
            case 22:
                return "完结时间之后（第2次）";
            case 23:
                return "完结时间之后（第3次）";
            case 24:
                return "完结时间之后（第4次）";
            case 25:
                return "完结时间之后（第5次）";
            case 26:
                return "完结时间之后（第6次）";
            case 27:
                return "完结时间之后（第7次）";
            case 28:
                return "完结时间之后（第8次）";
            case 29:
                return "完结时间之后（第9次）";
        }
        return "";
    }

    /**
     * 检查列表是否按顺序排列（之前的节点从大到小；之后的节点从小到大）
     *
     * @param planRuleDTOS 待检查的列表
     * @return
     */
    public static void checkPlanRuleSorted(List<PlanRuleDTO> planRuleDTOS) {


        Assert.notEmpty(planRuleDTOS, PARAM_ERROR.getErrCode(), "提醒规则不能为空");
        for (PlanRuleDTO planRuleDTO : planRuleDTOS) {
            Assert.notNull(planRuleDTO.getNodeNumber(), PARAM_ERROR.getErrCode(), "提醒时间节点不能为空");
            if (planRuleDTO.getNodeNumber() < 11 || planRuleDTO.getNodeNumber() > 29) {
                throw new BizException(PARAM_ERROR.getErrCode(), "提醒时间节点值非法:不在允许范围内");
            }
            if (planRuleDTO.getRemindTimeConfig().isSet() == 0) {
                throw new BizException(PARAM_ERROR.getErrCode(), "请填写正确的提醒时间");
            }
        }

        if (planRuleDTOS.isEmpty() || planRuleDTOS.size() == 1) {
            return;
        }
        List<PlanRuleDTO> preList = planRuleDTOS.stream()
            .filter(a -> a.getNodeNumber() != null && a.getNodeNumber() > 10 && a.getNodeNumber() < 20)
            .sorted(Comparator.comparing(PlanRuleDTO::getNodeNumber)).toList();

        List<PlanRuleDTO> postList = planRuleDTOS.stream()
            .filter(a -> a.getNodeNumber() != null && a.getNodeNumber() > 20 && a.getNodeNumber() < 30)
            .sorted(Comparator.comparing(PlanRuleDTO::getNodeNumber)).toList();

        Integer preStartNum= 11;
        for (int i = 0; i < preList.size(); i++) {
            PlanRuleDTO current = preList.get(i);
            if (!preStartNum.equals(current.getNodeNumber())) {
                throw new BizException(PARAM_ERROR.getErrCode(), "未按照正常顺序配置提醒时间节点："+gainNodeName(preStartNum));
            }
            preStartNum++;
        }

        Integer postStartNum= 21;
        for (int i = 0; i < postList.size(); i++) {
            PlanRuleDTO current = postList.get(i);
            if (!postStartNum.equals(current.getNodeNumber())) {
                throw new BizException(PARAM_ERROR.getErrCode(), "未按照正常顺序配置提醒时间节点："+gainNodeName(postStartNum));
            }
            postStartNum++;
        }
        
        for (int i = 0; i < preList.size() - 1; i++) {
            PlanRuleDTO current = preList.get(i);
            TimeConfigDTO currentRemindTimeConfig = current.getRemindTimeConfig();
            String errMessage = "";
            for (int n = i + 1; n < preList.size(); n++) {
                PlanRuleDTO next = preList.get(n);
                TimeConfigDTO nextRemindTimeConfig = next.getRemindTimeConfig();
                if (currentRemindTimeConfig.compareTo(nextRemindTimeConfig) < 1) {
                    if (StringUtils.isBlank(errMessage)) {
                        errMessage =
                            gainNodeName(current.getNodeNumber()) + "需要大于" + gainNodeName(next.getNodeNumber());
                    } else {
                        errMessage = errMessage + "且需要大于" + gainNodeName(next.getNodeNumber());
                    }
                }
            }
            if (StringUtils.isNotBlank(errMessage)) {
                throw new BizException(PARAM_ERROR.getErrCode(), errMessage);
            }
        }

        for (int i = 0; i < postList.size() - 1; i++) {
            PlanRuleDTO current = postList.get(i);
            TimeConfigDTO currentRemindTimeConfig = current.getRemindTimeConfig();
            String errMessage = "";
            for (int n = i + 1; n < postList.size(); n++) {
                PlanRuleDTO next = postList.get(n);
                TimeConfigDTO nextRemindTimeConfig = next.getRemindTimeConfig();
                if (currentRemindTimeConfig.compareTo(nextRemindTimeConfig) > -1) {
                    if (StringUtils.isBlank(errMessage)) {
                        errMessage = gainNodeName(current.getNodeNumber()) + "需要小于" + gainNodeName(next.getNodeNumber());
                    } else {
                        errMessage = errMessage + "且需要小于" + gainNodeName(next.getNodeNumber());
                    }
                }
            }
            if (StringUtils.isNotBlank(errMessage)) {
                throw new BizException(PARAM_ERROR.getErrCode(), errMessage);
            }
        }
    }


    public static void compareGameConfigTime(List<PlanGameConfigDTO> planGameConfigDTOS) {
        Assert.notEmpty(planGameConfigDTOS, PARAM_ERROR.getErrCode(), "预警设置不能为空");
        for (PlanGameConfigDTO planGameConfigDTO : planGameConfigDTOS) {
            if (planGameConfigDTO.getExpectCompleteTimeConfig()==null || planGameConfigDTO.getImCountDownTimeConfig()==null) {
                continue;
            }
            if (planGameConfigDTO.getImCountDownTimeConfig().compareTo(planGameConfigDTO.getExpectCompleteTimeConfig())>0) {
                throw new BizException(PARAM_ERROR.getErrCode(),
                    "im客服端倒计时要小于预期完成时间，游戏：" + planGameConfigDTO.getGameName());
            }
        }
    }

    public static void comparePlanRuleAndGameConfigTime(List<PlanRuleDTO> planRuleDTOS,
        List<PlanGameConfigDTO> planGameConfigDTOS) {
        Assert.notEmpty(planRuleDTOS, PARAM_ERROR.getErrCode(), "提醒规则不能为空");
        Assert.notEmpty(planGameConfigDTOS, PARAM_ERROR.getErrCode(), "预警设置不能为空");

        List<PlanRuleDTO> preList = planRuleDTOS.stream()
            .filter(a -> a.getNodeNumber() != null && a.getNodeNumber() > 10 && a.getNodeNumber() < 20)
            .sorted(Comparator.comparing(PlanRuleDTO::getNodeNumber)).toList();
        if (preList.isEmpty()) {
            return;
        }
        PlanRuleDTO planRuleDTO = preList.get(0);

        PlanGameConfigDTO minExpectCompleteGameConfig =
            planGameConfigDTOS.stream().filter(a->a.getExpectCompleteTimeConfig()!=null).min(Comparator.comparing(PlanGameConfigDTO::getExpectCompleteTimeConfig))
                .orElse(null);

        PlanGameConfigDTO minCoutDownGameConfig =
            planGameConfigDTOS.stream().filter(a->a.getImCountDownTimeConfig()!=null).min(Comparator.comparing(PlanGameConfigDTO::getImCountDownTimeConfig))
                .orElse(null);
        Assert.notNull(minExpectCompleteGameConfig, PARAM_ERROR.getErrCode(), "预警设置异常无法找到最小预期完成时间");
        //Assert.notNull(minCoutDownGameConfig, PARAM_ERROR.getErrCode(), "预警设置异常无法找到最小im客服端倒计时时间");

        String nodeName = gainNodeName(planRuleDTO.getNodeNumber());

        /*if (planRuleDTO.getRemindTimeConfig().compareTo(minCoutDownGameConfig.getImCountDownTimeConfig()) > -1) {
            throw new BizException(PARAM_ERROR.getErrCode(),
                nodeName + "时间节点值需要小于预警设置中最小的im客服端倒计时值，游戏：" + minCoutDownGameConfig.getGameName());
        }*/
        if (planRuleDTO.getRemindTimeConfig()
            .compareTo(minExpectCompleteGameConfig.getExpectCompleteTimeConfig()) > -1) {
            throw new BizException(PARAM_ERROR.getErrCode(),
                nodeName + "时间节点值需要小于预警设置中最小的预期完成时间，游戏：" + minExpectCompleteGameConfig.getGameName());
        }

    }

    public static void assComparePlanRuleAndGameConfigTime(List<PlanRuleDTO> planRuleDTOS,
        List<AssPlanGameConfigDTO> planGameConfigDTOS) {
        Assert.notEmpty(planRuleDTOS, PARAM_ERROR.getErrCode(), "提醒规则不能为空");
        Assert.notEmpty(planGameConfigDTOS, PARAM_ERROR.getErrCode(), "预警设置不能为空");

        List<PlanRuleDTO> preList = planRuleDTOS.stream()
            .filter(a -> a.getNodeNumber() != null && a.getNodeNumber() > 10 && a.getNodeNumber() < 20)
            .sorted(Comparator.comparing(PlanRuleDTO::getNodeNumber)).toList();
        if (preList.isEmpty()) {
            return;
        }
        PlanRuleDTO planRuleDTO = preList.get(0);

        AssPlanGameConfigDTO minExpectCompleteGameConfig =
            planGameConfigDTOS.stream().filter(a->a.getExpectCompleteTimeConfig()!=null).min(Comparator.comparing(AssPlanGameConfigDTO::getExpectCompleteTimeConfig))
                .orElse(null);

        Assert.notNull(minExpectCompleteGameConfig, PARAM_ERROR.getErrCode(), "预警设置异常无法找到最小预期完成时间");

        String nodeName = gainNodeName(planRuleDTO.getNodeNumber());

        if (planRuleDTO.getRemindTimeConfig()
            .compareTo(minExpectCompleteGameConfig.getExpectCompleteTimeConfig()) > -1) {
            throw new BizException(PARAM_ERROR.getErrCode(),
                nodeName + "时间节点值需要小于预警设置中最小的预期完成时间，游戏：" + minExpectCompleteGameConfig.getGameName());
        }

    }

    public static void complaintComparePlanRuleAndGameConfigTime(List<PlanRuleDTO> planRuleDTOS, List<ComplaintPlanGameConfigDTO> planGameConfigDTOS) {
        Assert.notEmpty(planRuleDTOS, PARAM_ERROR.getErrCode(), "提醒规则不能为空");
        Assert.notEmpty(planGameConfigDTOS, PARAM_ERROR.getErrCode(), "预警设置不能为空");

        List<PlanRuleDTO> preList = planRuleDTOS.stream()
            .filter(a -> a.getNodeNumber() != null && a.getNodeNumber() > 10 && a.getNodeNumber() < 20)
            .sorted(Comparator.comparing(PlanRuleDTO::getNodeNumber)).toList();
        if (preList.isEmpty()) {
            return;
        }
        PlanRuleDTO planRuleDTO = preList.get(0);

        ComplaintPlanGameConfigDTO minExpectCompleteGameConfig =
            planGameConfigDTOS.stream().filter(a->a.getExpectCompleteTimeConfig()!=null && a.getExpectCompleteTimeConfig().isSet() > 0).min(Comparator.comparing(ComplaintPlanGameConfigDTO::getExpectCompleteTimeConfig))
                .orElse(null);

        Assert.notNull(minExpectCompleteGameConfig, PARAM_ERROR.getErrCode(), "预警设置异常无法找到最小预期完成时间");

        String nodeName = gainNodeName(planRuleDTO.getNodeNumber());

        if (planRuleDTO.getRemindTimeConfig()
            .compareTo(minExpectCompleteGameConfig.getExpectCompleteTimeConfig()) > -1) {
            throw new BizException(PARAM_ERROR.getErrCode(),
                nodeName + "时间节点值需要小于预警设置中最小的预期完成时间，渠道：" + ComplaintChannelEnum.getLabel(minExpectCompleteGameConfig.getChannel()));
        }

    }

    public static void assCompareGameConfigTime(List<AssPlanGameConfigDTO> planGameConfigDTOS) {
        Assert.notEmpty(planGameConfigDTOS, PARAM_ERROR.getErrCode(), "预警设置不能为空");
        for (AssPlanGameConfigDTO planGameConfigDTO : planGameConfigDTOS) {
            if (planGameConfigDTO.getExpectCompleteTimeConfig()==null || planGameConfigDTO.getImCountDownTimeConfig()==null) {
                continue;
            }
            if (planGameConfigDTO.getImCountDownTimeConfig().compareTo(planGameConfigDTO.getExpectCompleteTimeConfig())>0) {
                throw new BizException(PARAM_ERROR.getErrCode(),
                    "im客服端倒计时要小于预期完成时间，游戏：" + planGameConfigDTO.getGameName());
            }
        }
    }

    public static void complaintCompareGameConfigTime(List<ComplaintPlanGameConfigDTO> planGameConfigDTOS) {
        Assert.notEmpty(planGameConfigDTOS, PARAM_ERROR.getErrCode(), "预警设置不能为空");
        for (ComplaintPlanGameConfigDTO planGameConfigDTO : planGameConfigDTOS) {
            if (planGameConfigDTO.getExpectCompleteTimeConfig() == null
                || planGameConfigDTO.getImCountDownTimeConfig() == null) {
                continue;
            }
            if (planGameConfigDTO.getImCountDownTimeConfig()
                .compareTo(planGameConfigDTO.getExpectCompleteTimeConfig()) > 0) {
                throw new BizException(PARAM_ERROR.getErrCode(),
                    "im客服端倒计时要小于预期完成时间，渠道：" + ComplaintChannelEnum.getLabel(planGameConfigDTO.getChannel()));
            }
        }
    }
}
