package com.pxb7.mall.workorder.domain.remind.extension.workorder;

import com.alibaba.cola.extension.Extensions;
import com.pxb7.mall.workorder.client.constant.MessageTemplateConstant;
import com.pxb7.mall.workorder.client.enums.BizTypeEnum;
import com.pxb7.mall.workorder.client.enums.RemindMethodEnum;
import com.pxb7.mall.workorder.client.enums.RemindObjectEnum;
import com.pxb7.mall.workorder.client.message.RemindPlanMessage;
import com.pxb7.mall.workorder.domain.model.RemindMethodConfigBO;
import com.pxb7.mall.workorder.domain.model.RemindWorkOrderBO;
import com.pxb7.mall.workorder.domain.remind.extension.BaseMessageService;
import com.pxb7.mall.workorder.domain.remind.extension.RemindBizExtPt;
import com.pxb7.mall.workorder.domain.remind.extension.context.RemindBizContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

import static com.pxb7.mall.workorder.infra.constant.CommonConstants.SYS_DEFAULT_USER_0;

/**
 * 商品工单飞书消息提醒，私聊
 * <AUTHOR>
 * @date 2025/4/2 14:56
 */

@Slf4j
@Extensions(
        bizId = {BizTypeEnum.Constants.WORK_ORDER},
        useCase = {RemindMethodEnum.Constants.FEISHU},
        scenario = {RemindObjectEnum.Constants.ART_DESIGNER,
                RemindObjectEnum.Constants.AUDIT_CUSTOMER_CARE,
                RemindObjectEnum.Constants.FOLLOWER})
public class WoFeishuMsgBizExtPt extends BaseMessageService implements RemindBizExtPt {

    @Override
    public void executeRemindTask(RemindBizContext context) {
        RemindWorkOrderBO remindWorkOrder = (RemindWorkOrderBO) context.getRemindRecord();
        RemindPlanMessage remindPlanMessage = context.getRemindPlanMessage();
        //获取提醒对象
        RemindMethodConfigBO.RemindObjectBO remindObjectBO = context.getRemindObjectBO();
        RemindObjectEnum remindObject = RemindObjectEnum.getByCode(remindObjectBO.getObject());
        String systemUserId = "";
        switch (Objects.requireNonNull(remindObject)) {
            case ART_DESIGNER -> systemUserId = remindWorkOrder.getArtDesignerId();
            case AUDIT_CUSTOMER_CARE -> systemUserId = remindWorkOrder.getAuditUserId();
            case FOLLOWER -> systemUserId = remindWorkOrder.getFollowerId();
            default -> log.error("Fail to get remind object by code. code:{}", remindObjectBO.getObject());
        }
        if (StringUtils.isBlank(systemUserId)
                || Objects.equals(systemUserId, SYS_DEFAULT_USER_0)) {
            log.warn("Fail to get remind user id. planRecordId:{}", remindPlanMessage.getPlanRecordId());
            return;
        }
        //获取商品工单消息内容
        String content = getWorkOrderMessageContent(context, RemindMethodEnum.FEISHU);
        if (StringUtils.isBlank(content)) {
            log.error("Fail to get message content. planRecordId:{}", remindPlanMessage.getPlanRecordId());
            return;
        }
        //发送飞书私聊消息
        sendFeishuMessage(MessageTemplateConstant.WORK_ORDER_REMIND_TITLE,
                content, systemUserId, remindPlanMessage.getPlanRecordId());
    }

}
