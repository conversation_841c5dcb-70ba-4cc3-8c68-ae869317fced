package com.pxb7.mall.workorder.infra.repository.db.entity;

import java.io.Serializable;
import java.time.*;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import com.baomidou.mybatisplus.annotation.*;

/**
 * 售后找回纠纷预警统计(AdsPxb7AfterSaleForewarning)实体类
 *
 * <AUTHOR>
 * @since 2025-05-08 18:13:53
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "ads_pxb7_after_sale_forewarning")
public class AdsPxb7AfterSaleForewarning implements Serializable {
    private static final long serialVersionUID = 682800661934090604L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 工单创建日期
     */
    @TableField(value = "create_date")
    private String createDate;
    /**
     * 处理人id
     */
    @TableField(value = "process_user_id")
    private String processUserId;
    /**
     * 游戏id
     */
    @TableField(value = "game_id")
    private String gameId;
    /**
     * 工单类型:1 找回,2 纠纷
     */
    @TableField(value = "work_order_type")
    private Integer workOrderType;
    /**
     * 1:散户工单,2:号商工单,3:3A工单
     */
    @TableField(value = "membership")
    private Integer membership;
    /**
     * 工单数量
     */
    @TableField(value = "work_order_cnt")
    private Long workOrderCnt;
    /**
     * 完结工单数量
     */
    @TableField(value = "complete_work_order_cnt")
    private Long completeWorkOrderCnt;
    /**
     * 处理中工单数量
     */
    @TableField(value = "processing_work_order_cnt")
    private Long processingWorkOrderCnt;
    /**
     * 即将超时工单数量
     */
    @TableField(value = "timeouting_work_order_cnt")
    private Long timeoutingWorkOrderCnt;
    /**
     * 已超时工单数量
     */
    @TableField(value = "timeouted_work_order_cnt")
    private Long timeoutedWorkOrderCnt;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;
    /**
     * 创建人
     */
    @TableField(value = "create_user_id")
    private String createUserId;
    /**
     * 更新人
     */
    @TableField(value = "update_user_id")
    private String updateUserId;
    /**
     * 是否删除 0:否, 1:是
     */
    @TableLogic
    @TableField(value = "is_deleted")
    private Boolean deleted;

}
