package com.pxb7.mall.workorder.app.mapping;

import com.pxb7.mall.workorder.app.model.ComplaintPageDetailDTO;
import com.pxb7.mall.workorder.app.model.ComplaintStatisticDataDTO;
import com.pxb7.mall.workorder.app.model.ComplaintStatisticDataSearchDTO;
import com.pxb7.mall.workorder.app.model.RemindComplaintReqDTO;
import com.pxb7.mall.workorder.app.model.RemindComplaintRespDTO;
import com.pxb7.mall.workorder.domain.model.ComplaintStatisticDataBO;
import com.pxb7.mall.workorder.domain.model.ComplaintStatisticDataSearchBO;
import com.pxb7.mall.workorder.domain.model.RemindComplaintBO;
import com.pxb7.mall.workorder.domain.model.RemindComplaintReqBO;
import com.pxb7.mall.workorder.domain.model.RemindComplaintRespBO;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;


@Mapper
public interface RemindComplaintAppMapping {

    RemindComplaintAppMapping INSTANCE = Mappers.getMapper(RemindComplaintAppMapping.class);


    RemindComplaintReqBO.AddBO remindComplaintDTO2AddBO(RemindComplaintReqDTO.AddDTO source);

    RemindComplaintReqBO.UpdateBO remindComplaintDTO2UpdateBO(RemindComplaintReqDTO.UpdateDTO source);

    RemindComplaintReqBO.DelBO remindComplaintDTO2DelBO(RemindComplaintReqDTO.DelDTO source);

    RemindComplaintReqBO.SearchBO remindComplaintDTO2SearchBO(RemindComplaintReqDTO.SearchDTO source);

    RemindComplaintReqBO.PageBO remindComplaintDTO2PageBO(RemindComplaintReqDTO.PageDTO source);

    RemindComplaintRespDTO.DetailDTO remindComplaintBO2DetailDTO(RemindComplaintRespBO.DetailBO source);

    List<RemindComplaintRespDTO.DetailDTO> remindComplaintBO2ListDTO(List<RemindComplaintRespBO.DetailBO> source);

    Page<ComplaintPageDetailDTO> remindComplaintBO2PageDTO(Page<RemindComplaintBO> source);

    ComplaintStatisticDataSearchBO statisticParam2BO(ComplaintStatisticDataSearchDTO param);

    List<ComplaintStatisticDataDTO> statisticResul2DTOList(List<ComplaintStatisticDataBO> list);
}


