package com.pxb7.mall.workorder.infra.repository.es.mapper;

import com.pxb7.mall.workorder.infra.repository.es.entity.RemindDeliveryProductDoc;
import org.springframework.data.elasticsearch.repository.ElasticsearchRepository;

import java.io.Serializable;

public interface RemindDeliveryProductDocRepository extends ElasticsearchRepository<RemindDeliveryProductDoc, Serializable> {

    //SearchPage<RemindDeliveryProductDoc> pageQueryDeliveryProduct(RemindDeliveryProductReqPO.PagePO deliveryProductPagePO);
}
