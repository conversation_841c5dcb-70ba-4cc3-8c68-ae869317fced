package com.pxb7.mall.workorder.adapter.schedule;

import com.pxb7.mall.workorder.client.enums.BizTypeEnum;
import com.pxb7.mall.workorder.domain.service.BaseRemindDomainService;
import com.pxb7.mall.workorder.domain.service.RemindDomainServiceFactory;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.shardingsphere.infra.hint.HintManager;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import tech.powerjob.worker.core.processor.ProcessResult;
import tech.powerjob.worker.core.processor.TaskContext;
import tech.powerjob.worker.core.processor.sdk.BasicProcessor;

import java.time.LocalDateTime;
import java.util.List;

/**
 *  预警任务超时状态更新任务
 * <AUTHOR>
 * @date 2025/3/28 20:05
 */

@Component
@Slf4j
public class RemindTimeoutTask implements BasicProcessor {

    /**
     *  分库数据库实例
     */
    @Value("${remind.service.datasources}")
    private String datasourceStr;

    @Resource
    private RemindDomainServiceFactory remindDomainServiceFactory;

    @Override
    public ProcessResult process(TaskContext taskContext) throws Exception {
        log.info("预警任务超时状态更新任务:{}", LocalDateTime.now());
        List<String> databaseInstances = List.of(datasourceStr.split(","));
        //扫描所有分库中符合条件的账号交付记录
        for (String databaseInstance : databaseInstances) {
            processSharding(databaseInstance);
        }

        return new ProcessResult(true, "预警任务超时状态更新任务成功！");
    }

    /**
     * 分片处理
     * @param databaseName
     */
    private void processSharding(String databaseName) {
        log.info("start to execute update timeout status task, databaseName:{}", databaseName);
        try (HintManager hintManager = HintManager.getInstance()) {
            // 指定数据源(Sharding-JDBC 会路由到指定的数据源)
            hintManager.setDataSourceName(databaseName);
            for (BizTypeEnum bizType : BizTypeEnum.values()) {
                BaseRemindDomainService remindService =
                        remindDomainServiceFactory.getRemindService(bizType);
                remindService.executeUpdateTimeoutStatusTask(databaseName);
            }
        }
        log.info("start to execute update timeout status task, databaseName:{}", databaseName);
    }


}
