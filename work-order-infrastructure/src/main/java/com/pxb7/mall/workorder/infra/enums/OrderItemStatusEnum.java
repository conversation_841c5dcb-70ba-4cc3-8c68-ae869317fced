package com.pxb7.mall.workorder.infra.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum OrderItemStatusEnum {

    INIT(0, "创建中", "init"),
    WAIT_PAY(1, "待支付", "waitPay"),
    DEALING(2, "交易中", "dealing"),
    PER_SETTLEMENT(3, "待结算", "perSettlement"),
    DEAL_SUCCESS(4, "已完成", "dealSuccess"),
    DEAL_CANCEL(5, "已取消", "dealCancel"),
    REFUND_CANCEL(6, "退款已取消", "refundCancel"),
    ;

    private final Integer value;
    private final String label;
    private final String bizCode;

    static Map<Integer, OrderItemStatusEnum> map = new HashMap<>();

    static {
        for (OrderItemStatusEnum orderItemStatusEnum : OrderItemStatusEnum.values()) {
            map.put(orderItemStatusEnum.value, orderItemStatusEnum);
        }
    }

    public boolean eq(Integer value) {
        return this.getValue().equals(value);
    }

    public static OrderItemStatusEnum convert(Integer code) {
        OrderItemStatusEnum type = map.get(code);
        if (type != null) {
            return type;
        } else {
            throw new RuntimeException("当前订单操作状态无法处理");
        }
    }

    public static String getLabel(Integer value) {
        return Arrays.stream(OrderItemStatusEnum.values()).filter(e -> e.getValue().equals(value))
                .map(OrderItemStatusEnum::getLabel).findAny().orElse(null);
    }

    /**
     * 是否可以支付
     */
    public static boolean canPay(Integer value) {
        return WAIT_PAY.eq(value) || DEALING.eq(value);
    }

    /**
     * 是否可以创建补收款
     */
    public static boolean canSaveReceiptVoucher(Integer value) {
        return DEALING.eq(value) || PER_SETTLEMENT.eq(value) || DEAL_SUCCESS.eq(value);
    }
}
