package com.pxb7.mall.workorder.domain.remind.extension.common;

import com.alibaba.cola.extension.Extensions;
import com.pxb7.mall.im.client.dto.request.SendReminderMsgReqDTO;
import com.pxb7.mall.workorder.client.enums.BizTypeEnum;
import com.pxb7.mall.workorder.client.enums.RemindMethodEnum;
import com.pxb7.mall.workorder.client.enums.RemindObjectEnum;
import com.pxb7.mall.workorder.client.message.RemindPlanMessage;
import com.pxb7.mall.workorder.domain.model.*;
import com.pxb7.mall.workorder.domain.remind.extension.BaseMessageService;
import com.pxb7.mall.workorder.domain.remind.extension.RemindBizExtPt;
import com.pxb7.mall.workorder.domain.remind.extension.context.RemindBizContext;
import com.pxb7.mall.workorder.infra.enums.RemindPlanWorkOrderTypeEnum;
import com.pxb7.mall.workorder.infra.repository.gateway.dubbo.im.ImMsgGateway;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

import static com.pxb7.mall.workorder.infra.constant.CommonConstants.SYS_DEFAULT_USER_0;

/**
 * IM消息提醒，私聊
 * <AUTHOR>
 * @date 2025/4/2 14:56
 */

@Slf4j
@Extensions(
        bizId = {BizTypeEnum.Constants.DELIVERY_PRODUCT,
                BizTypeEnum.Constants.AFTER_SALE,
                BizTypeEnum.Constants.COMPLAINT},
        useCase = {RemindMethodEnum.Constants.IM},
        scenario = {RemindObjectEnum.Constants.CUSTOMER_CARE})
public class ImMsgBizExtPt extends BaseMessageService implements RemindBizExtPt {


    @Resource
    private ImMsgGateway imMsgGateway;

    @Override
    public void executeRemindTask(RemindBizContext context) {
        RemindPlanMessage remindPlanMessage = context.getRemindPlanMessage();
        BaseRemindRecordBO remindRecord = context.getRemindRecord();

        RemindSubPlanRespBO.DetailBO remindSubPlan =
                subPlanDomainService.findById(remindPlanMessage.getRemindSubPlanId());

        //获取提醒对象
        String userId;
        String groupId;
        if (remindRecord instanceof RemindDeliveryProductBO remindDeliveryProduct) {
            userId = remindDeliveryProduct.getDeliveryCustomerCare();
            groupId = remindDeliveryProduct.getGroupId();
        } else if (remindRecord instanceof RemindAfterSaleBO remindAfterSale) {
            userId = RemindPlanWorkOrderTypeEnum.RETRIEVE.eq(remindSubPlan.getWorkOrderType()) ?
                    remindAfterSale.getRetrieveUserId() : remindAfterSale.getDisputeUserId();
            groupId = remindAfterSale.getGroupId();
        } else if (remindRecord instanceof RemindComplaintBO remindComplaint) {
            userId = remindComplaint.getHandleUserId();
            groupId = remindComplaint.getGroupId();
        } else {
            // 添加默认处理分支，防止未来类型扩展导致逻辑静默失败
            throw new IllegalArgumentException("Unsupported remindRecord type: " + remindRecord.getClass().getName());
        }

        if (StringUtils.isBlank(userId)
                || Objects.equals(userId, SYS_DEFAULT_USER_0)) {
            log.error("Fail to get systemUserId. planRecordId:{}", remindPlanMessage.getPlanRecordId());
            return;
        }
        if (StringUtils.isBlank(groupId)) {
            log.error("Fail to get groupId. planRecordId:{}", remindPlanMessage.getPlanRecordId());
            return;
        }
        String content = getRemindMessageContent(context);
        if (StringUtils.isBlank(content)) {
            log.error("Fail to get message content. planRecordId:{}", remindPlanMessage.getPlanRecordId());
            return;
        }
        //向给客服发送催一催消息
        SendReminderMsgReqDTO msgReqDTO = new SendReminderMsgReqDTO();
        msgReqDTO.setGroupId(groupId);
        msgReqDTO.setContent(content);
        msgReqDTO.setUserId(userId);
        msgReqDTO.setVisibleUserId(userId);

        imMsgGateway.sendRemindMsg(msgReqDTO);
    }

}
