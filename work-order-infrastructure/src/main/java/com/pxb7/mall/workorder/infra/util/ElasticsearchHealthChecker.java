package com.pxb7.mall.workorder.infra.util;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch.cluster.HealthResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

/**
 * Elasticsearch健康检查工具
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class ElasticsearchHealthChecker {

    @Resource
    private ElasticsearchClient elasticsearchClient;

    /**
     * 检查Elasticsearch集群健康状态
     * 
     * @return true if healthy, false otherwise
     */
    public boolean isHealthy() {
        try {
            HealthResponse health = elasticsearchClient.cluster().health();
            String status = health.status().jsonValue();
            
            log.debug("Elasticsearch cluster status: {}", status);
            
            // green或yellow状态都认为是健康的
            return "green".equals(status) || "yellow".equals(status);
            
        } catch (Exception e) {
            log.error("Failed to check Elasticsearch health", e);
            return false;
        }
    }

    /**
     * 检查指定索引是否存在
     * 
     * @param indexName 索引名称
     * @return true if exists, false otherwise
     */
    public boolean indexExists(String indexName) {
        try {
            return elasticsearchClient.indices().exists(r -> r.index(indexName)).value();
        } catch (Exception e) {
            log.error("Failed to check if index exists: {}", indexName, e);
            return false;
        }
    }

    /**
     * 获取索引的映射信息
     * 
     * @param indexName 索引名称
     */
    public void logIndexMapping(String indexName) {
        try {
            var mappingResponse = elasticsearchClient.indices().getMapping(r -> r.index(indexName));
            log.info("Index mapping for {}: {}", indexName, mappingResponse);
        } catch (Exception e) {
            log.error("Failed to get mapping for index: {}", indexName, e);
        }
    }
}
