package com.pxb7.mall.workorder.infra.util;

/**
 * 工具类
 *
 * <AUTHOR>
 * @date 2024/08/27 14:52
 **/

import org.springframework.util.NumberUtils;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * 工具类
 *
 * <AUTHOR>
 **/
public class NumberUtil {

    public static final BigDecimal ZERO_BIGDECIMAL = new BigDecimal("0");

    public static <T extends Number> boolean isPositive(T value) {
        return value != null && value.longValue() > 0;
    }

    public static <T extends Number> boolean isNotPositive(T value) {
        return value == null || value.longValue() < 1;
    }

    public static boolean isPositive(Long value) {
        return value != null && value > 0;
    }

    public static boolean isPositive(Integer value) {
        return value != null && value > 0;
    }

    public static <T extends Number> boolean allPositive(T... values) {
        for (T value : values) {
            if (isNotPositive(value)) {
                return false;
            }
        }
        return true;
    }

    public static <T extends Number> boolean anyNotPositive(T... values) {
        for (T value : values) {
            if (isNotPositive(value)) {
                return true;
            }
        }
        return false;
    }

    public static <T extends Number> boolean anyPositive(T... values) {
        for (T value : values) {
            if (isPositive(value)) {
                return true;
            }
        }
        return false;
    }

    public static int parseIntQuietly(Object value) {
        return parseIntQuietly(value, 0);
    }

    public static int parseIntQuietly(Object value, int def) {
        if (value != null) {
            if (value instanceof Number) {
                return ((Number)value).intValue();
            }
            try {
                return Integer.parseInt(value.toString());
            } catch (Throwable e) {
            }
        }
        return def;
    }

    public static long parseLongQuietly(Object value, long def) {
        if (value != null) {
            if (value instanceof Number) {
                return ((Number)value).longValue();
            }
            try {
                return Long.parseLong(value.toString());
            } catch (Throwable e) {
            }
        }

        return def;
    }

    public static boolean isNotPositive(Integer value) {
        return !isPositive(value);
    }

    public static boolean isNotPositive(Long value) {
        return !isPositive(value);
    }

    public static int intValue(Integer value) {
        return intValue(value, 0);
    }

    public static int intValue(Integer value, int def) {
        return value != null ? value : def;
    }

    public static boolean isNotId(Integer id) {
        return !isId(id);
    }

    public static boolean isId(Integer id) {
        return id != null && id > 0;
    }

    public static boolean in(int value, int[] ints) {
        if (ints != null && ints.length > 0) {
            for (int i : ints) {
                if (i == value) {
                    return true;
                }
            }
        }
        return false;
    }

    public static boolean between(int value, int min, int max) {
        return value >= min && value <= max;
    }

    public static boolean between(long value, long min, long max) {
        return value >= min && value <= max;
    }

    public static double parseDoubleQuietly(Object value) {
        return parseDoubleQuietly(value, 0);
    }

    public static double parseDoubleQuietly(Object value, double def) {
        if (value != null) {
            if (value instanceof Number) {
                return ((Number)value).doubleValue();
            }
            try {
                return parseNumber(value.toString(), Double.class);
            } catch (IllegalArgumentException e) {
                /* do nothing. */
            }
        }
        return def;
    }

    public static <T extends Number> T parseNumber(String value, Class<T> targetClass) {
        return NumberUtils.parseNumber(value, targetClass);
    }

    public static int getIntByPosition(int value, int index) {
        if (value <= 0) {
            return 0;
        }

        if (index <= 0) {
            index = 1;
        }

        String strValue = String.valueOf(value);
        if (strValue.length() < index) {
            return 0;
        }

        return Integer.parseInt("" + strValue.charAt(strValue.length() - index));
    }

    public static int setIntByPosition(int source, int index, int value) {
        if (index <= 0) {
            index = 1;
        }

        if (value <= 9 && value >= 0) {
            StringBuilder buff = new StringBuilder(String.valueOf(source));
            if (buff.length() >= index) {
                buff.setCharAt(buff.length() - index, Integer.valueOf(value).toString().charAt(0));
            } else {
                int maxIndex = index - buff.length() - 1;
                for (int i = 0; i < maxIndex; i++) {
                    buff.insert(0, "0");
                }

                buff.insert(0, value);
            }

            return Integer.parseInt(buff.toString());
        }
        return source;
    }

    /**
     * <p>
     * Checks whether the <code>String</code> contains only digit characters.
     * </p>
     * <p>
     * <p>
     * 判断参数value是否仅包含数字字符，null或空字符串返回false，负数(如'-3')、小数(如'3.14')返回false.
     * </p>
     * <p>
     * <p>
     * 判断是否是合法数字，请使用isParsable{@code isParsable} 方法.
     * </p>
     * <p>
     * <p>
     * <code>Null</code> and empty String will return <code>false</code>.
     * </p>
     *
     * @param value the <code>String</code> to check
     * @return <code>true</code> if str contains only Unicode numeric
     */
    public static boolean is(String value) {
        if (value != null && value.length() > 0) {
            char[] chars = value.toCharArray();
            for (char aChar : chars) {
                if (!Character.isDigit(aChar)) {
                    return false;
                }
            }
            return true;
        }
        return false;
    }

    /**
     * 判断 BigDecimal 数据是否 大于 0
     */
    public static boolean gtZero(BigDecimal value) {
        if (Objects.isNull(value)) {
            return false;
        }
        return value.compareTo(ZERO_BIGDECIMAL) > 0;
    }

    /**
     * 判断 BigDecimal 数据是否 小于 0
     */
    public static boolean ltZero(BigDecimal value) {
        if (Objects.isNull(value)) {
            return false;
        }
        return value.compareTo(ZERO_BIGDECIMAL) < 0;
    }

    /**
     * 判断 BigDecimal 数据是否 等于 0
     */
    public static boolean eqZero(BigDecimal value) {
        if (Objects.isNull(value)) {
            return false;
        }
        return value.compareTo(ZERO_BIGDECIMAL) == 0;
    }

    /**
     * 判断 两个BigDecimal 数据是否 相等
     */
    public static boolean eq(BigDecimal value1, BigDecimal value2) {
        if (Objects.isNull(value1) || Objects.isNull(value2)) {
            return false;
        }
        return value1.compareTo(value2) == 0;
    }

    /**
     * 判断 A > B
     */
    public static boolean gt(BigDecimal value1, BigDecimal value2) {
        if (Objects.isNull(value1) || Objects.isNull(value2)) {
            return false;
        }
        return value1.compareTo(value2) > 0;
    }

    public static boolean equals(Integer value1, Integer value2) {
        if (Objects.isNull(value1) || Objects.isNull(value2)) {
            return false;
        }
        return value1.equals(value2);
    }

    public static long merge(int hi, int lo) {
        return (((long)hi) << 32) | (long)lo;
    }

    public static int hi(long value) {
        return (int)(value >> 32);
    }

    public static int lo(long value) {
        return (int)(value & -1);
    }
}
