package com.pxb7.mall.workorder.app.mapping;

import com.pxb7.mall.workorder.app.model.TimeConfigDTO;
import com.pxb7.mall.workorder.domain.model.TimeConfigBO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface TimeConfigDomainMapping {

    TimeConfigDomainMapping INSTANCE = Mappers.getMapper(TimeConfigDomainMapping.class);


    TimeConfigBO timeConfigDTO2BO(TimeConfigDTO source);


    List<TimeConfigBO> timeConfigDTO2ListBO(List<TimeConfigDTO> source);


    TimeConfigDTO timeConfigBO2DTO(TimeConfigBO source);


    List<TimeConfigBO> timeConfigBO2ListDTO(List<TimeConfigDTO> source);
}


