package com.pxb7.mall.workorder.domain.message;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/4/25 17:40
 */

@Data
public class AssWorkOrderMessage implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 售后工单ID
     */
    private String workOrderId;
    /**
     * 房间ID
     */
    private String roomId;

    /**
     * 订单id
     */
    private String orderItemId;

    /**
     * 工单类型 1:找回 2:纠纷 3:投诉
     */
    private Integer workOrderType;

    /**
     * 售后工单状态 1:待处理 2:已完结 3:关闭
     *
     */
    private Integer workOrderStatus;


}
