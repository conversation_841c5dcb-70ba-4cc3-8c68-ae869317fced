package com.pxb7.mall.workorder.domain.model;

import java.util.Date;
import java.time.*;
import java.math.BigDecimal;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.ToString;


/**
 * 商品工单预警记录(RemindWorkOrder)实体类
 *
 * <AUTHOR>
 * @since 2025-04-07 11:59:37
 */
public class RemindWorkOrderRespBO {


    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class DetailBO {
        private Long id;
        private String remindId;
        private String workOrderId;
        private Integer workOrderStatus;
        private String productId;
        private String productCode;
        private String gameId;
        private Integer completeStatus;
        private Integer timeOutStatus;
        private String artDesignerId;
        private String followerId;
        private String auditUserId;
        private LocalDateTime expectCompleteTime;
        private LocalDateTime completeTime;
        private LocalDateTime followedUpInflowTime;
        private Long gameConfigId;
        private Long remindPlanId;
        private Long remindSubPlanId;
        private LocalDateTime createTime;
        private LocalDateTime updateTime;
        private String createUserId;
        private String updateUserId;
        private Boolean deleted;
    }
}

