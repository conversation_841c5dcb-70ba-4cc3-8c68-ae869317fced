package com.pxb7.mall.workorder.app.service;


import static com.pxb7.mall.workorder.client.enums.BizErrorCodeEnum.PARAM_ERROR;
import static com.pxb7.mall.workorder.client.enums.BizErrorCodeEnum.USER_INFO_NOT_FOUND;

import com.alibaba.cola.exception.Assert;
import com.alibaba.cola.exception.BizException;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pxb7.mall.auth.c.util.AdminUserUtil;
import com.pxb7.mall.auth.dto.AdminUserDTO;
import com.pxb7.mall.trade.ofs.delivery.client.dto.model.DeliveryStatusChangeDTO;
import com.pxb7.mall.trade.order.client.dto.response.order.RoomOrderDetailsRespDTO;
import com.pxb7.mall.trade.order.client.dto.response.order.dubbo.OrderInfoDubboRespDTO;
import com.pxb7.mall.trade.order.client.enums.order.OrderServiceModeEnum;
import com.pxb7.mall.trade.order.client.message.OrderItemStatusChangeMQDTO;
import com.pxb7.mall.workorder.app.mapping.RemindDeliveryProductAppMapping;
import com.pxb7.mall.workorder.app.mapping.TimeoutInfoDomainMapping;
import com.pxb7.mall.workorder.app.model.DeliveryProductPageDetailDTO;
import com.pxb7.mall.workorder.app.model.DeliveryProductStatisticDataDTO;
import com.pxb7.mall.workorder.app.model.DeliveryProductStatisticDataSearchDTO;
import com.pxb7.mall.workorder.app.model.RemindDeliveryProductReqDTO;
import com.pxb7.mall.workorder.app.model.TimeoutInfoRespDTO;
import com.pxb7.mall.workorder.app.util.RemindPlanPageBuildUtil;
import com.pxb7.mall.workorder.app.util.TimeTipUtil;
import com.pxb7.mall.workorder.client.enums.BizTypeEnum;
import com.pxb7.mall.workorder.client.enums.CompleteStatusEnum;
import com.pxb7.mall.workorder.client.enums.DeliveryStatusEnum;
import com.pxb7.mall.workorder.client.enums.PlanExecuteStatusEnum;
import com.pxb7.mall.workorder.client.enums.TimeoutStatusEnum;
import com.pxb7.mall.workorder.domain.model.DeliveryProductStatisticDataBO;
import com.pxb7.mall.workorder.domain.model.DeliveryProductStatisticDataSearchBO;
import com.pxb7.mall.workorder.domain.model.RemindDeliveryProductBO;
import com.pxb7.mall.workorder.domain.model.RemindDeliveryProductReqBO;
import com.pxb7.mall.workorder.domain.model.RemindDeliveryProductRespBO;
import com.pxb7.mall.workorder.domain.model.RemindPlanGameConfigBO;
import com.pxb7.mall.workorder.domain.model.RemindPlanRecordBO;
import com.pxb7.mall.workorder.domain.model.TimeConfigBO;
import com.pxb7.mall.workorder.domain.model.TimeoutInfoRespBO;
import com.pxb7.mall.workorder.domain.service.BaseRemindDomainService;
import com.pxb7.mall.workorder.domain.service.RemindDeliveryProductDomainService;
import com.pxb7.mall.workorder.domain.service.RemindDomainServiceFactory;
import com.pxb7.mall.workorder.domain.service.RemindPlanGameConfigDomainService;
import com.pxb7.mall.workorder.domain.service.RemindPlanRecordDomainService;
import com.pxb7.mall.workorder.infra.aop.ClusterRedisLock;
import com.pxb7.mall.workorder.infra.constant.RedisKeyConstants;
import com.pxb7.mall.workorder.infra.enums.OrderItemStatusEnum;
import com.pxb7.mall.workorder.infra.exception.RetryException;
import com.pxb7.mall.workorder.infra.model.GameBasePO;
import com.pxb7.mall.workorder.infra.model.SysUserRespPO;
import com.pxb7.mall.workorder.infra.repository.gateway.dubbo.order.OrderInfoGateway;
import com.pxb7.mall.workorder.infra.repository.gateway.dubbo.product.GameGateway;
import com.pxb7.mall.workorder.infra.repository.gateway.dubbo.user.SysUserGateway;
import com.pxb7.mall.workorder.infra.util.RedissonUtils;
import jakarta.annotation.Resource;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

/**
 * 账号交付预警记录app服务
 *
 * <AUTHOR>
 * @since 2025-03-27 20:08:53
 */
@Slf4j
@Service
public class RemindDeliveryProductAppService {

    @Resource
    private RemindDeliveryProductDomainService deliveryProductDomainService;

    @Resource
    private RemindPlanRecordDomainService planRecordDomainService;

    @Resource
    private RemindPlanGameConfigDomainService gameConfigDomainService;

    @Resource
    private RemindPlanRetryTaskAppService retryTaskAppService;

    @Resource
    private OrderInfoGateway orderInfoGateway;

    @Resource
    private SysUserGateway sysUserGateway;

    @Resource
    private GameGateway gameGateway;

    @Resource
    private RemindDomainServiceFactory remindDomainServiceFactory;

    public Page<DeliveryProductPageDetailDTO> page(RemindDeliveryProductReqDTO.PageDTO param) {
        AdminUserDTO adminUser = AdminUserUtil.getAdminUser();
        Assert.isTrue(adminUser != null && StringUtils.isNotBlank(adminUser.getUserId()), USER_INFO_NOT_FOUND.getErrCode(), USER_INFO_NOT_FOUND.getErrDesc());

        RemindDeliveryProductReqBO.PageBO pageBO = RemindDeliveryProductAppMapping.INSTANCE.remindDeliveryProductDTO2PageBO(param);
        Page<RemindDeliveryProductBO> page = deliveryProductDomainService.pageEs(pageBO);

        Page<DeliveryProductPageDetailDTO> pageDTO = RemindDeliveryProductAppMapping.INSTANCE.remindDeliveryProductBO2PageDTO(page);
        // 提取并处理工作订单列表中的用户ID，用于后续查询用户信息
        List<DeliveryProductPageDetailDTO> records = Optional.of(pageDTO).map(Page::getRecords).orElse(new ArrayList<>());

        Set<String> userIds = RemindPlanPageBuildUtil.extractUserIdsFromDeliveryProduct(records);
        List<SysUserRespPO> sysUserInfoList = sysUserGateway.getSysUserInfoList(userIds);
        Map<String, SysUserRespPO> sysUserRespPOMap = sysUserInfoList.stream().collect(Collectors.toMap(SysUserRespPO::getUserId, Function.identity()));

        List<String> gameIds = records.stream().map(DeliveryProductPageDetailDTO::getGameId).toList();
        List<GameBasePO> gameBasePOList = gameGateway.getGameInfoByIds(gameIds);
        Map<String, GameBasePO> gameBasePOMap = gameBasePOList.stream().collect(Collectors.toMap(GameBasePO::getGameId, Function.identity()));


        // 遍历工作订单列表，设置用户名称信息、提示信息等
        records.forEach(a -> {
            if (StringUtils.isNotBlank(a.getDeliveryCustomerCare())){
                String userName = Optional.of(sysUserRespPOMap).map(s -> s.get(a.getDeliveryCustomerCare()))
                    .map(SysUserRespPO::getUserName).orElse(null);
                a.setDeliveryCustomerCareName(userName);
            }
            if (StringUtils.isNotBlank(a.getGameId())){
                String userName = Optional.of(gameBasePOMap).map(s -> s.get(a.getGameId()))
                    .map(GameBasePO::getGameName).orElse(null);
                a.setGameName(userName);
            }
            a.setDurationTip(TimeTipUtil.getDurationTip(null == a.getPayTime() ? a.getCreateTime() : a.getPayTime(),
                null == a.getCompleteTime() ? LocalDateTime.now() : a.getCompleteTime()));
            a.setCountDownTip(TimeTipUtil.getCountDownTip(null == a.getCompleteTime()?LocalDateTime.now() : a.getCompleteTime(),
                a.getExpectCompleteTime()));
        });

        // 返回设置有用户名称信息的工作订单分页列表
        return pageDTO.setRecords(records);
    }

    /**
     * 根据订单状态变化生成提醒计划
     *
     * @param orderStatusChangeDTO 订单项状态变化的消息队列数据
     */
    @Transactional(rollbackFor = Exception.class)
    @ClusterRedisLock(prefix = "delivery_product_plan_", value = "#orderStatusChangeDTO.orderItemId")
    public void generateRemindPlan(OrderItemStatusChangeMQDTO orderStatusChangeDTO) {
        //获取订单信息
        String deliveryRoomId = orderStatusChangeDTO.getDeliveryRoomId();
        if (StringUtils.isBlank(deliveryRoomId)) {
            retryTaskAppService.saveRetryTask(orderStatusChangeDTO);
            return;
        }
        generateRemindPlan(deliveryRoomId);
    }

    /**
     * 重试生成提醒计划
     * @param orderItemId
     */
    @Transactional(rollbackFor = Exception.class)
    @ClusterRedisLock(prefix = "delivery_product_plan_", value = "#orderItemId")
    public void retryGenerateRemindPlan(String orderItemId) {
        OrderInfoDubboRespDTO orderInfo = orderInfoGateway.getOrderInfo(orderItemId);
        if (Objects.isNull(orderInfo)) {
            log.warn("根据orderItemId:[{}] 查询订单信息为空", orderItemId);
            throw new RetryException();
        }
        String deliveryRoomId = orderInfo.getDeliveryRoomId();
        if (StringUtils.isBlank(deliveryRoomId)) {
            throw new RetryException();
        }
        generateRemindPlan(deliveryRoomId);
    }

    /**
     * 生成提醒计划
     * @param deliveryRoomId
     */
    private void generateRemindPlan(String deliveryRoomId) {
        RoomOrderDetailsRespDTO orderInfoRespDTO = orderInfoGateway.getRoomOrderDetails(deliveryRoomId);
        if (Objects.isNull(orderInfoRespDTO)) {
            log.warn("Fail to query order info by order item id. orderItemId:{}", deliveryRoomId);
            return;
        }

        //游戏id
        String gameId = orderInfoRespDTO.getGameId();
        if (StringUtils.isBlank(gameId)) {
            log.warn("游戏id不能为空, orderId:{}", orderInfoRespDTO.getOrderItemId());
            return;
        }
        //交易模式 1代售 2中介 3诚心卖 4金币回收 5金币出售，6充值
        Integer serviceMode = orderInfoRespDTO.getServiceMode();
        if (!OrderServiceModeEnum.AGENT_SALE.eq(serviceMode)
                && !OrderServiceModeEnum.AGENCY_SALE.eq(serviceMode)) {
            log.warn("不支持该订单交易模式, orderId:{}, serviceMode:{}", orderInfoRespDTO.getOrderItemId(), serviceMode);
            return;
        }
        //使用gameId查询适配的预警计划
        RemindPlanGameConfigBO remindPlanGameConfig = gameConfigDomainService.getRemindPlanGameConfig(gameId, serviceMode);
        if (Objects.isNull(remindPlanGameConfig)) {
            log.warn("游戏id对应的预警计划不存在, gameId:{}, businessType:{}", gameId, serviceMode);
            return;
        }
        TimeConfigBO expectCompleteTimeConfig = remindPlanGameConfig.getExpectCompleteTimeConfig();
        if (Objects.isNull(expectCompleteTimeConfig)
                || (Objects.isNull(expectCompleteTimeConfig.getHours()) && Objects.isNull(expectCompleteTimeConfig.getMinutes()))) {
            log.warn("游戏id对应的预警计划预期完结时间没有配置, gameId:{}, remindPlanId:{}", gameId, remindPlanGameConfig.getRemindPlanId());
            return;
        }
        //生成账号交付预警记录
        RemindDeliveryProductReqBO.AddBO remindDeliveryProduct =
                deliveryProductDomainService.generateRemindDeliveryProduct(orderInfoRespDTO, remindPlanGameConfig);
        if (Objects.isNull(remindDeliveryProduct)) {
            log.warn("订单对应的账号交付预警记录已存在, orderItemId:{}", orderInfoRespDTO.getOrderItemId());
            return;
        }
        //生成账号交付预警执行计划记录
        generateDeliveryProductPlanRecords(remindDeliveryProduct);
    }

    /**
     * 生成账号交付预警执行计划记录
     * @param remindDeliveryProduct
     */
    private void generateDeliveryProductPlanRecords(RemindDeliveryProductReqBO.AddBO remindDeliveryProduct) {
        RemindPlanRecordBO remindPlanRecordBO = new RemindPlanRecordBO()
                .setRecordIdPrefix("DPP")
                .setBizType(BizTypeEnum.DELIVERY_PRODUCT.getType())
                .setBizId(remindDeliveryProduct.getOrderItemId())
                .setRemindId(remindDeliveryProduct.getRemindId())
                .setRemindPlanId(remindDeliveryProduct.getRemindPlanId())
                .setRemindSubPlanId(remindDeliveryProduct.getRemindSubPlanId())
                .setExpectCompleteTime(remindDeliveryProduct.getExpectCompleteTime());
        //生成预警执行计划记录
        planRecordDomainService.generatePlanRecords(remindPlanRecordBO);
    }

    /**
     * 根据订单项状态变化更新提醒计划的状态
     *
     * @param orderStatusChangeDTO 订单项状态变化的消息队列数据
     */
    @Transactional(rollbackFor = Exception.class)
    @ClusterRedisLock(prefix = "delivery_product_plan", value = "#orderStatusChangeDTO.orderItemId")
    public void changeRemindPlanStatus(OrderItemStatusChangeMQDTO orderStatusChangeDTO) {
        Integer completeStatus = null;
        if (OrderItemStatusEnum.DEAL_SUCCESS.eq(orderStatusChangeDTO.getOrderItemStatus())) {
            completeStatus = CompleteStatusEnum.DONE.getCode();
        }

        if (OrderItemStatusEnum.DEAL_CANCEL.eq(orderStatusChangeDTO.getOrderItemStatus())
                ||  OrderItemStatusEnum.REFUND_CANCEL.eq(orderStatusChangeDTO.getOrderItemStatus())) {
            completeStatus = CompleteStatusEnum.CANCELED.getCode();
        }
        if (Objects.isNull(completeStatus)) {
            return;
        }
        //更新账号交付预警记录状态
        RemindDeliveryProductReqBO.UpdateBO updateBO = new RemindDeliveryProductReqBO.UpdateBO();
        updateBO.setOrderItemId(orderStatusChangeDTO.getOrderItemId());
        updateBO.setCompleteStatus(completeStatus);
        updateBO.setCompleteTime(LocalDateTime.now());
        deliveryProductDomainService.updateByOrderItemId(updateBO);

        //作废账号交付预警执行计划
        planRecordDomainService.invalidPlanRecord(
                BizTypeEnum.DELIVERY_PRODUCT.getType(), orderStatusChangeDTO.getOrderItemId());

        //删除redis群组超时状态
        RemindDeliveryProductRespBO.DetailBO deliveryProduct =
                deliveryProductDomainService.findByOrderItemId(orderStatusChangeDTO.getOrderItemId(), null);
        if (Objects.nonNull(deliveryProduct)) {
            String groupTimoutKey = String.format(RedisKeyConstants.TIMEOUT_GROUP_KEY,
                    deliveryProduct.getGroupId(), deliveryProduct.getDeliveryCustomerCare());
            RedissonUtils.deleteObject(groupTimoutKey);
        }
    }

    /**
     * 更新订单关联的交付状态
     *
     * @param deliveryStatusChangeDTO 交付项状态变化的消息队列数据
     */
    @Transactional(rollbackFor = Exception.class)
    @ClusterRedisLock(prefix = "delivery_product_plan_stop", value = "#deliveryStatusChangeDTO.orderItemId")
    public void changeRemindPlanStatusByDeliverStatusChange(DeliveryStatusChangeDTO deliveryStatusChangeDTO) {
        // 获取信息
        RemindDeliveryProductRespBO.DetailBO deliveryProduct = deliveryProductDomainService
                .findByOrderItemId(deliveryStatusChangeDTO.getOrderItemId(), null);
        if (Objects.isNull(deliveryProduct)) {
            log.warn("根据orderItemId:[{}]查询交付产品信息为空", deliveryStatusChangeDTO.getOrderItemId());
            return;
        }
        // 更新账号交付预警记录状态
        RemindDeliveryProductReqBO.UpdateBO updateBO = new RemindDeliveryProductReqBO.UpdateBO();
        updateBO.setOrderItemId(deliveryStatusChangeDTO.getOrderItemId());
        updateBO.setDeliveryStatus(deliveryStatusChangeDTO.getStatus());
        long seconds = 0;
        if (DeliveryStatusEnum.PAUSE.getCode().equals(deliveryStatusChangeDTO.getStatus())) {
            // 同时记录暂停开始时间
            updateBO.setPauseTime(LocalDateTime.now());
        } else if (DeliveryStatusEnum.DELIVERY.getCode().equals(deliveryStatusChangeDTO.getStatus())
                || DeliveryStatusEnum.NOT_DELIVERY.getCode().equals(deliveryStatusChangeDTO.getStatus())) {
            // 移除原有的暂停开始时间
            updateBO.setPauseTime(null);
            // 计算差值，当前时间-暂停开始时间
            LocalDateTime pauseTime = deliveryProduct.getPauseTime();
            if (Objects.nonNull(pauseTime)) {
                seconds = Duration.between(pauseTime, LocalDateTime.now()).toSeconds();
                // 相加 当前时间+差值
                updateBO.setExpectCompleteTime(deliveryProduct.getExpectCompleteTime().plusSeconds(seconds));
            }
        }

        boolean updateByOrderItemId = deliveryProductDomainService.updateByOrderItemId(updateBO);
        if (updateByOrderItemId) {
            // 更新账号交付预警执行计划状态
            if (DeliveryStatusEnum.PAUSE.getCode().equals(deliveryStatusChangeDTO.getStatus())) {
                planRecordDomainService.batchUpdateStatusByRemindIds(
                        List.of(deliveryProduct.getRemindId()), PlanExecuteStatusEnum.PAUSE, null);
            } else if (DeliveryStatusEnum.DELIVERY.getCode().equals(deliveryStatusChangeDTO.getStatus()) ||
                    DeliveryStatusEnum.NOT_DELIVERY.getCode().equals(deliveryStatusChangeDTO.getStatus())) {
                planRecordDomainService.batchUpdateStatusByRemindIds(
                        List.of(deliveryProduct.getRemindId()), PlanExecuteStatusEnum.WILL_EXECUTE,
                        seconds);
            }
        }
    }

    /**
     * 查询群组中交付超时信息
     * @param groupId 群组id
     * @param customerCareId 客服id
     * @return
     */
    public TimeoutInfoRespDTO getTimeoutInfo(String groupId, String customerCareId) {
        TimeoutInfoRespBO timeoutInfoRespBO = new TimeoutInfoRespBO();
        for (BizTypeEnum bizType : BizTypeEnum.values()) {
            BaseRemindDomainService remindService =
                    remindDomainServiceFactory.getRemindService(bizType);
            timeoutInfoRespBO = remindService.getTimeoutInfo(groupId, customerCareId);
            if (!Objects.equals(timeoutInfoRespBO.getTimeoutType(), TimeoutStatusEnum.NONE.getCode())) {
                break;
            }
        }
        return TimeoutInfoDomainMapping.INSTANCE.timeoutInfoBO2DTO(timeoutInfoRespBO);
    }

    public List<DeliveryProductStatisticDataDTO> getStatisticList(DeliveryProductStatisticDataSearchDTO param) {

        AdminUserDTO adminUser = AdminUserUtil.getAdminUser();
        Assert.isTrue(adminUser != null && StringUtils.isNotBlank(adminUser.getUserId()), USER_INFO_NOT_FOUND.getErrCode(), USER_INFO_NOT_FOUND.getErrDesc());


        if (StringUtils.isBlank(param.getStartDate())|| StringUtils.isBlank(param.getEndDate())){
            LocalDateTime nowDateTime = LocalDateTime.now();
            LocalDateTime starDateTime = nowDateTime.minusDays(31);
            param.setStartDate(starDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            param.setEndDate(nowDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        }else{
            LocalDate startDate;
            LocalDate endDate;
            try {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                startDate = LocalDate.parse(param.getStartDate(), formatter);
                endDate = LocalDate.parse(param.getEndDate(), formatter);
            } catch (Exception e) {
                throw new BizException(PARAM_ERROR.getErrCode(), "日期格式不正确");
            }
            Assert.isTrue(!startDate.isAfter(endDate), PARAM_ERROR.getErrCode(), "创建房间开始时间要早于创建房间结束时间");
            long daysBetween = Math.abs(ChronoUnit.DAYS.between(startDate, endDate));
            Assert.isTrue(daysBetween <= 31, PARAM_ERROR.getErrCode(), "筛选时间的间隔不能超过31天");
        }

        DeliveryProductStatisticDataSearchBO searchBO = RemindDeliveryProductAppMapping.INSTANCE.statisticParam2BO(param);
        List<DeliveryProductStatisticDataBO> list = deliveryProductDomainService.getStatisticList(searchBO);

        if (!CollectionUtils.isEmpty(list)){
            DeliveryProductStatisticDataBO totalStatisticDataBO = new DeliveryProductStatisticDataBO();
            totalStatisticDataBO.setDate("汇总");
            totalStatisticDataBO.setProcessTotalCount(list.stream().filter(a->a.getProcessTotalCount()!=null).mapToLong(DeliveryProductStatisticDataBO::getProcessTotalCount).sum());
            totalStatisticDataBO.setCompleteCount(list.stream().filter(a->a.getCompleteCount()!=null).mapToLong(DeliveryProductStatisticDataBO::getCompleteCount).sum());
            totalStatisticDataBO.setProcessingCount(list.stream().filter(a->a.getProcessingCount()!=null).mapToLong(DeliveryProductStatisticDataBO::getProcessingCount).sum());
            totalStatisticDataBO.setWillTimeoutCount(list.stream().filter(a->a.getWillTimeoutCount()!=null).mapToLong(DeliveryProductStatisticDataBO::getWillTimeoutCount).sum());
            totalStatisticDataBO.setTimeoutCount(list.stream().filter(a->a.getTimeoutCount()!=null).mapToLong(DeliveryProductStatisticDataBO::getTimeoutCount).sum());
            totalStatisticDataBO.setBusinessType(null);
            /*totalStatisticDataBO.setDeliveryCustomerCareId("");
            totalStatisticDataBO.setDeliveryCustomerCareName("");
            totalStatisticDataBO.setGameId("");
            totalStatisticDataBO.setGameName("");*/
            list.add(totalStatisticDataBO);

            List<String> gameIds = list.stream().map(DeliveryProductStatisticDataBO::getGameId).toList();
            List<GameBasePO> gameBasePOList = gameGateway.getGameInfoByIds(gameIds);
            Map<String, GameBasePO> gameBasePOMap = gameBasePOList.stream().collect(Collectors.toMap(GameBasePO::getGameId, Function.identity()));


            Set<String> userIds = RemindPlanPageBuildUtil.extractUserIdsFromDeliveryStatisticList(list);
            List<SysUserRespPO> sysUserInfoList = sysUserGateway.getSysUserInfoList(userIds);
            Map<String, SysUserRespPO> sysUserRespPOMap = sysUserInfoList.stream().collect(Collectors.toMap(SysUserRespPO::getUserId, Function.identity()));

            for (DeliveryProductStatisticDataBO deliveryProductStatisticDataBO : list) {
                String gameId = deliveryProductStatisticDataBO.getGameId();
                if (StringUtils.isNotBlank(gameId)) {
                    GameBasePO gameBasePO = gameBasePOMap.get(gameId);
                    deliveryProductStatisticDataBO.setGameName(null == gameBasePO ? null : gameBasePO.getGameName());
                }
                String deliveryCustomerCareId = deliveryProductStatisticDataBO.getDeliveryCustomerCareId();
                if (StringUtils.isNotBlank(deliveryCustomerCareId)){
                    SysUserRespPO sysUserRespPO = sysUserRespPOMap.get(deliveryCustomerCareId);
                    deliveryProductStatisticDataBO.setDeliveryCustomerCareName(null==sysUserRespPO?null:sysUserRespPO.getUserName());
                }
            }

        }

        return RemindDeliveryProductAppMapping.INSTANCE.statisticResul2DTOList(list);
    }

    /**
     * 更新账号交付最新房间和客服
     * @param orderItemId
     */
    public void updateGroupAndCustomerCare(String orderItemId) {
        OrderInfoDubboRespDTO orderInfo = orderInfoGateway.getOrderInfo(orderItemId);
        if (Objects.isNull(orderInfo)) {
            log.warn("根据orderItemId:[{}]查询订单信息为空", orderItemId);
            return;
        }
        RemindDeliveryProductRespBO.DetailBO deliveryProduct =
                deliveryProductDomainService.findByOrderItemId(orderItemId, null);
        if (Objects.isNull(deliveryProduct)) {
            log.warn("根据orderItemId:[{}]查询交付产品信息为空", orderItemId);
            return;
        }
        //更新交付信息
        RemindDeliveryProductReqBO.UpdateBO updateBO = new RemindDeliveryProductReqBO.UpdateBO();
        updateBO.setOrderItemId(orderItemId);
        updateBO.setGroupId(orderInfo.getDeliveryRoomId());
        updateBO.setDeliveryCustomerCare(orderInfo.getDeliveryCustomerId());
        deliveryProductDomainService.updateByOrderItemId(updateBO);

        //删除旧的群组超时状态
        String groupTimoutKey = String.format(RedisKeyConstants.TIMEOUT_GROUP_KEY,
                deliveryProduct.getGroupId(), deliveryProduct.getDeliveryCustomerCare());

        Integer timeOutStatus = RedissonUtils.getCacheObject(groupTimoutKey);
        if (Objects.isNull(timeOutStatus)) {
            return;
        }
        RedissonUtils.deleteObject(groupTimoutKey);

        //设置新的群组超时状态
        String newGroupTimoutKey = String.format(RedisKeyConstants.TIMEOUT_GROUP_KEY,
                orderInfo.getDeliveryRoomId(), orderInfo.getDeliveryCustomerId());
        RedissonUtils.setObjectIfAbsent(newGroupTimoutKey, timeOutStatus, Duration.ofDays(180L));
    }



     /**
     * 更新账号交付最新房间和客服
     * @param orderItemId
     */
    public void updateGroupDeliveryCustomerCare(String orderItemId, String newDeliveryCustomerCareId, String groupId) {
        RemindDeliveryProductRespBO.DetailBO deliveryProduct =
                deliveryProductDomainService.findByOrderItemId(orderItemId, null);
        if (Objects.isNull(deliveryProduct)) {
            log.warn("根据orderItemId查询交付产品信息为空，orderItemId:{}", orderItemId);
            return;
        }
           //更新交付信息
        RemindDeliveryProductReqBO.UpdateBO updateBO = new RemindDeliveryProductReqBO.UpdateBO();
        updateBO.setOrderItemId(orderItemId);
        updateBO.setGroupId(groupId);
        updateBO.setDeliveryCustomerCare(newDeliveryCustomerCareId);
        deliveryProductDomainService.updateByOrderItemId(updateBO);

        //删除旧的群组超时状态
        String groupTimoutKey = String.format(RedisKeyConstants.TIMEOUT_GROUP_KEY,
                deliveryProduct.getGroupId(), deliveryProduct.getDeliveryCustomerCare());

        Integer timeOutStatus = RedissonUtils.getCacheObject(groupTimoutKey);
        if (Objects.isNull(timeOutStatus)) {
            return;
        }
        RedissonUtils.deleteObject(groupTimoutKey);

        //设置新的群组超时状态
        String newGroupTimoutKey = String.format(RedisKeyConstants.TIMEOUT_GROUP_KEY,
                groupId, newDeliveryCustomerCareId);
        RedissonUtils.setObjectIfAbsent(newGroupTimoutKey, timeOutStatus, Duration.ofDays(180L));
    }

}

