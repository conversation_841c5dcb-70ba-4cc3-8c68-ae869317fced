package com.pxb7.mall.workorder.infra.repository.db;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.pxb7.mall.workorder.infra.model.RemindAfterSaleReqPO;
import com.pxb7.mall.workorder.infra.repository.db.entity.RemindAfterSale;

import java.util.List;

/**
 * 售后工单预警记录(RemindAfterSale)表服务接口
 *
 * <AUTHOR>
 * @since 2025-04-24 23:31:56
 */
public interface RemindAfterSaleRepository extends IService<RemindAfterSale> {

    boolean insert(RemindAfterSaleReqPO.AddPO param);

    boolean updateByWorkOrderId(RemindAfterSaleReqPO.UpdatePO param);

    RemindAfterSale findByWorkOrderId(String workOrderId, String remindId);

    Page<RemindAfterSale> page(RemindAfterSaleReqPO.PagePO pagePO);

    boolean batchUpdateTimeoutStatusByIds(List<Long> ids, Integer status);

    RemindAfterSale getByRemindId(String remindId);
}

