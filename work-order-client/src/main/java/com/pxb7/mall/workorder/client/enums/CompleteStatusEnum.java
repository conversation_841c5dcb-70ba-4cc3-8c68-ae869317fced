package com.pxb7.mall.workorder.client.enums;

import lombok.Getter;
import lombok.ToString;

/**
 * 完结状态
 * <AUTHOR>
 */
@Getter
@ToString
public enum CompleteStatusEnum {

    /**
     * 完结状态,1:未完结,2:已完结,3:终止
     */
    IN_PROCESS(1, "未完结"),
    DONE(2, "已完结"),
    CANCELED(3, "终止");

    private final Integer code;
    private final String desc;

    CompleteStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public boolean eq(Integer code) {
        return this.getCode().equals(code);
    }

}
