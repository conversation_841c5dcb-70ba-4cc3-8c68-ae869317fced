package com.pxb7.mall.workorder.app.model;

import java.util.Date;
import java.time.*;
import java.math.BigDecimal;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.ToString;


/**
 * 商品工单预警记录(RemindWorkOrder)实体类
 *
 * <AUTHOR>
 * @since 2025-04-07 12:00:06
 */
public class RemindWorkOrderRespDTO {


    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class DetailDTO {
        private Long id;
        private String remindId;
        private String workOrderId;
        private Integer workOrderStatus;
        private String productId;
        private Integer completeStatus;
        private Integer timeOutStatus;
        private String artDesignerId;
        private String followerId;
        private String auditUserId;
        private LocalDateTime expectCompleteTime;
        private Long gameConfigId;
        private Long remindPlanId;
        private Long remindSubPlanId;
    }
}

