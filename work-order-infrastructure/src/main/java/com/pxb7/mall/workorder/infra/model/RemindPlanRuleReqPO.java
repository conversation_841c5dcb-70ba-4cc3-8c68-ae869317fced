package com.pxb7.mall.workorder.infra.model;

import java.util.Date;
import java.time.*;
import java.math.BigDecimal;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.ToString;


/**
 * 预警计划提醒规则配置(RemindPlanRule)实体类
 *
 * <AUTHOR>
 * @since 2025-03-24 21:09:56
 */
public class RemindPlanRuleReqPO {

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class AddPO {

        private Long id;

        private Integer nodeNumber;


        private String remindTimeConfig;


        private String remindMethodConfig;


        private Long remindPlanId;


        private String createUserId;


        private String updateUserId;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class UpdatePO {


        private Long id;


        private Integer nodeNumber;


        private String remindTimeConfig;


        private String remindMethodConfig;


        private Long remindPlanId;


        private String createUserId;


        private String updateUserId;


    }


    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class DelPO {
        private Long id;
    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class SearchPO {

        private Integer nodeNumber;


        private String remindTimeConfig;


        private String remindMethodConfig;


        private Long remindPlanId;

        private String createUserId;


        private String updateUserId;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class PagePO {


        private Integer nodeNumber;


        private String remindTimeConfig;


        private String remindMethodConfig;


        private Long remindPlanId;


        private String createUserId;


        private String updateUserId;


        /**
         * 页码，从1开始
         */
        private Integer pageIndex;
        /**
         * 每页数量
         */
        private Integer pageSize;

    }

}

