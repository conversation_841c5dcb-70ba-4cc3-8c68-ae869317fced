package com.pxb7.mall.workorder.adapter.web;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pxb7.mall.response.PxPageResponse;
import com.pxb7.mall.response.PxResponse;
import com.pxb7.mall.workorder.app.model.AfterSalePageDetailDTO;
import com.pxb7.mall.workorder.app.model.AfterSaleStatisticDataDTO;
import com.pxb7.mall.workorder.app.model.AfterSaleStatisticDataSearchDTO;
import com.pxb7.mall.workorder.app.model.RemindAfterSaleReqDTO;
import com.pxb7.mall.workorder.app.service.RemindAfterSaleAppService;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 售后工单预警
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/web/after/sale/")
public class RemindAfterSaleController {

    @Resource
    private RemindAfterSaleAppService remindAfterSaleAppService;

    /**
     * 管理后台-售后工单预警明细分页
     */
    @PostMapping("/page")
    public PxPageResponse<AfterSalePageDetailDTO> page(@RequestBody @Validated RemindAfterSaleReqDTO.PageDTO param) {
        Page<AfterSalePageDetailDTO> page = remindAfterSaleAppService.page(param);
        return PxPageResponse.of(page.getRecords(), (int)page.getTotal(), (int)page.getSize(), (int)page.getCurrent());
    }

    /**
     * 管理后台-售后工单预警统计列表
     */
    @PostMapping("/statistic/list")
    public PxResponse<List<AfterSaleStatisticDataDTO>>
        getStatisticList(@RequestBody @Validated AfterSaleStatisticDataSearchDTO param) {
        return PxResponse.ok(remindAfterSaleAppService.getStatisticList(param));
    }

    /**
     * 数据更新时间
     */
    @GetMapping("/date")
    public PxResponse<LocalDateTime> getUpdateTime() {
        return PxResponse.ok(remindAfterSaleAppService.getUpdateTime());
    }
}
