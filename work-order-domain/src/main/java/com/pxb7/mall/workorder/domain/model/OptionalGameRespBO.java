package com.pxb7.mall.workorder.domain.model;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@ToString
public class OptionalGameRespBO {

    /**
     * 游戏业务ID
     */
    private String gameId;
    /**
     * 游戏名称
     */
    private String gameName;


    /**
     * 游戏别名
     */
    private String gameAlias;

    /**
     * 厂商 1 网易系  2 腾讯系  3 米哈游  4其他
     */
    private Integer maker;

    /**
     * 之前已选中
     */
    private Boolean selected;

    /**
     * 被选中的 提醒计划id
     */
    private Long selectedRemindPlanId;
}
