package com.pxb7.mall.workorder.infra.repository.gateway.dubbo.common;

import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.fastjson2.JSON;
import com.pxb7.mall.common.client.api.message.SystemUserMessageServiceI;
import com.pxb7.mall.common.client.request.message.SysUserFeishuMsgReqDTO;
import com.pxb7.mall.common.client.request.message.SysUserInternalMsgReqDTO;
import com.pxb7.mall.common.client.request.message.SysUserToastMsgReqDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @date 2025/4/1 16:08
 */

@Repository
@Slf4j
public class MessageGateway {

    @DubboReference
    private SystemUserMessageServiceI systemUserMessageService;

    /**
     * 发送飞书消息(私聊或群聊)
     * @param msgReqDTO
     * @return
     */
    public String sendFeishuMessage(SysUserFeishuMsgReqDTO msgReqDTO) {
        try {
            SingleResponse<String> response = systemUserMessageService.sendFeishuMessage(msgReqDTO);
            if (response != null && response.isSuccess()) {
                return response.getData();
            }
            log.error("Fail to send message to the systemUserMessageService#sendFeishuMessage,msgReqDTO:{}", JSON.toJSONString(msgReqDTO));
            return null;
        } catch (Exception e) {
            log.error("Fail to send message to the systemUserMessageService#sendFeishuMessage,msgReqDTO:{}", JSON.toJSONString(msgReqDTO));
            return null;
        }
    }

    /**
     * 向后台系统用户发送弹窗消息
     * @param msgReqDTO
     * @return
     */
    public String sendToastMessage(SysUserToastMsgReqDTO msgReqDTO) {
        try {
            SingleResponse<String> response = systemUserMessageService.sendToastMessage(msgReqDTO);
            if (response != null && response.isSuccess()) {
                return response.getData();
            }
            log.error("Fail to send message to the systemUserMessageService#sendToastMessage,msgReqDTO:{}", JSON.toJSONString(msgReqDTO));
            return null;
        } catch (Exception e) {
            log.error("Fail to send message to the systemUserMessageService#sendToastMessage,msgReqDTO:{}", JSON.toJSONString(msgReqDTO));
            return null;
        }
    }

    /**
     * 向后台系统用户发送站内信
     * @param msgReqDTO
     * @return
     */
    public String sendInternalMessage(SysUserInternalMsgReqDTO msgReqDTO) {
        try {
            SingleResponse<String> response = systemUserMessageService.sendInternalMessage(msgReqDTO);
            if (response != null && response.isSuccess()) {
                return response.getData();
            }
            log.error("Fail to send message to the systemUserMessageService#sendInternalMessage,msgReqDTO:{}", JSON.toJSONString(msgReqDTO));
            return null;
        } catch (Exception e) {
            log.error("Fail to send message to the systemUserMessageService#sendInternalMessage,msgReqDTO:{}", JSON.toJSONString(msgReqDTO));
            return null;
        }
    }

}
