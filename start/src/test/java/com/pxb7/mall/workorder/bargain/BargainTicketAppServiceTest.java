package com.pxb7.mall.workorder.bargain;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pxb7.mall.auth.c.util.AdminUserUtil;
import com.pxb7.mall.response.PxPageResponse;
import com.pxb7.mall.workorder.app.mapping.BargainTicketAppMapping;
import com.pxb7.mall.workorder.app.model.BargainTicketDetailDTO;
import com.pxb7.mall.workorder.app.model.BargainTicketPageDetailDTO;
import com.pxb7.mall.workorder.app.model.BargainTicketReqDTO;
import com.pxb7.mall.workorder.app.model.BargainTicketRespDTO;
import com.pxb7.mall.workorder.app.service.BargainTicketAppService;
import com.pxb7.mall.workorder.domain.model.BargainTicketBO;
import com.pxb7.mall.workorder.domain.service.BargainTicketDomainService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.*;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@SpringBootTest
public class BargainTicketAppServiceTest {

    @InjectMocks
    private BargainTicketAppService bargainTicketAppService;

    @Mock
    private BargainTicketDomainService bargainTicketDomainService;

    @Mock
    private AdminUserUtil adminUserUtil;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testPage_FilteredByTabSource2_WhenUserIdBlank() {
        BargainTicketReqDTO.PageDTO pageDTO = new BargainTicketReqDTO.PageDTO();
        pageDTO.setPageTabSource(2);
        pageDTO.setPageSize(10);
        pageDTO.setPageIndex(1);

        // 用户ID为空
        try (MockedStatic<AdminUserUtil> utilities = mockStatic(AdminUserUtil.class)) {
            utilities.when(AdminUserUtil::getUserId).thenReturn(null);

            PxPageResponse<BargainTicketPageDetailDTO> result = bargainTicketAppService.page(pageDTO);

            assertNotNull(result);
            assertTrue(result.getData().isEmpty());
        }
    }

    @Test
    void testPage_ValidRequest_ReturnsPagedData() {
        BargainTicketReqDTO.PageDTO pageDTO = new BargainTicketReqDTO.PageDTO();
        pageDTO.setPageTabSource(1); // 不触发过滤逻辑

        Page<BargainTicketBO> domainPage = new Page<>();
        domainPage.setCurrent(1);
        domainPage.setSize(10);
        domainPage.setTotal(1);
        domainPage.setRecords(Collections.singletonList(new BargainTicketBO()));

        when(bargainTicketDomainService.pageEs(any())).thenReturn(domainPage);

        PxPageResponse<BargainTicketPageDetailDTO> result = bargainTicketAppService.page(pageDTO);

        assertNotNull(result);
        assertEquals(1, result.getTotalCount());
        assertEquals(10, result.getPageSize());
        assertEquals(1, result.getPageIndex());
        assertFalse(result.getData().isEmpty());
    }

    @Test
    void testGetDetailByReceiveId_ReturnsDetailDTO() {
        String receiveId = "ticket123";
        BargainTicketBO bo = new BargainTicketBO();
        when(bargainTicketDomainService.findByReceiveId(receiveId)).thenReturn(bo);

        BargainTicketDetailDTO result = bargainTicketAppService.getDetailByReceiveId(receiveId);

        assertNotNull(result);
    }

    @Test
    void testDetail_ReturnsBargainTicketRespDTO() {
        String receiveId = "ticket123";
        BargainTicketBO bo = new BargainTicketBO();
        when(bargainTicketDomainService.findByReceiveId(receiveId)).thenReturn(bo);

        BargainTicketDetailDTO detailDTO = BargainTicketAppMapping.INSTANCE.convertTicketBo2DetailDto(bo);
        when(bargainTicketDomainService.findByReceiveId(receiveId)).thenReturn(bo);

        BargainTicketRespDTO result = bargainTicketAppService.detail(receiveId);

        assertNotNull(result);
        assertNotNull(result.getBuyerInfo());
    }

    @Test
    void testQueryRecentlySevenDaysDetailListBy_ReturnsRecentList() {
        String buyerId = "buyer123";

        BargainTicketBO bo = new BargainTicketBO();
        List<BargainTicketBO> records = Collections.singletonList(bo);

        Page<BargainTicketBO> page = new Page<>();
        page.setRecords(records);

        when(bargainTicketDomainService.pageEs(any())).thenReturn(page);

        List<BargainTicketDetailDTO> result = bargainTicketAppService.queryRecentlySevenDaysDetailListBy(buyerId, LocalDateTime.now());

        assertNotNull(result);
        assertEquals(1, result.size());
    }

}
