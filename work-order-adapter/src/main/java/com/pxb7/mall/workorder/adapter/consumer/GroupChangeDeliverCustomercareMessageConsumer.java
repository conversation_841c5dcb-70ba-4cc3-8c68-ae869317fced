package com.pxb7.mall.workorder.adapter.consumer;

import com.alibaba.fastjson.JSON;
import com.pxb7.mall.components.rocketmq.RocketMQListenerExt;
import com.pxb7.mall.im.client.dto.request.msg.UpdateOrderBindDeliveryCustomerCareMsgReqDTO;
import com.pxb7.mall.workorder.app.service.RemindDeliveryProductAppService;
import com.pxb7.mall.workorder.infra.constant.RMQConstant;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.annotation.RocketMQMessageListener;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.springframework.stereotype.Service;

/**
 * 监听房间更换交付客服(转接场景)
 * <AUTHOR>
 */
@Slf4j
@Service
@RocketMQMessageListener(
        consumerGroup = RMQConstant.UPDATE_ORDER_BIND_DELIVERY_CUSTOMER_CARE_TOPIC,
        topic = RMQConstant.UPDATE_ORDER_BIND_DELIVERY_CUSTOMER_CARE_TOPIC,
        tag = RMQConstant.ALL_TAG)
public class GroupChangeDeliverCustomercareMessageConsumer implements RocketMQListenerExt<UpdateOrderBindDeliveryCustomerCareMsgReqDTO> {

    @Resource
    private RemindDeliveryProductAppService remindDeliveryProductAppService;

    @Override
    public ConsumeResult consume(MessageView messageView, UpdateOrderBindDeliveryCustomerCareMsgReqDTO dto) {
        log.info("监听到群组更换交付客服消息: messageId = {},message = {}", messageView.getMessageId(), JSON.toJSONString(dto));
        String orderId = dto.getOrderId();
        String oldDeliveryCustomerCareId = dto.getOriginalDeliveryCustomerCareId();
        String newDeliveryCustomerCareId = dto.getNewDeliveryCustomerCareId();
        String groupId = dto.getGroupId();
        try {
            if (StringUtils.isBlank(orderId) || StringUtils.isBlank(oldDeliveryCustomerCareId) || StringUtils.isBlank(newDeliveryCustomerCareId) || StringUtils.isBlank(groupId)) {
                return ConsumeResult.SUCCESS;
            }
            //更新交付信息
            remindDeliveryProductAppService.updateGroupDeliveryCustomerCare(orderId, newDeliveryCustomerCareId, groupId);
            return ConsumeResult.SUCCESS;
        } catch (Exception e) {
            log.error("error in consumed from update_order_bind_delivery_customer_care_topic: orderId = {}", orderId, e);
            return ConsumeResult.FAILURE;
        }
    }
}

