dataSources:
  promotion_db:
    url: **********************************************************************************************************************************************************************************************************************************
    username: pxb7_promotion
    password: pxb7_test#cLtxR##m
    driverClassName: com.mysql.cj.jdbc.Driver
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    validationTimeout: 5000
    connectionTimeout: 30000
    maximumPoolSize: 500
    minimumIdle: 1
    idleTimeout: 600000
    leakDetectionThreshold: 0
    keepAliveTime: 30000
  db_single:
    url: ***************************************************************************************************************************************************************************************
    username: pxb7_work_order
    password: pxb7_test#cLtxR##m
    driverClassName: com.mysql.cj.jdbc.Driver
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    validationTimeout: 5000
    connectionTimeout: 30000
    minIdle: 10
    maximumPoolSize: 30
    idleTimeout: 60000
    leakDetectionThreshold: 0
    keepaliveTime: 30000
    connectionTestQuery: SELECT 1
  bigdata_single:
    url: **************************************************************************************************************************************************************************
    username: bigdata
    password: pxb7#bigd#PsRSkqLzeV
    driverClassName: com.mysql.cj.jdbc.Driver
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    validationTimeout: 5000
    connectionTimeout: 30000
    minIdle: 10
    maximumPoolSize: 30
    idleTimeout: 60000
    leakDetectionThreshold: 0
    keepaliveTime: 30000
    connectionTestQuery: SELECT 1
  db0:
    url: ******************************************************************************************************************************************************************************************
    username: pxb7_work_order
    password: pxb7_test#cLtxR##m
    driverClassName: com.mysql.cj.jdbc.Driver
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    validationTimeout: 5000
    connectionTimeout: 30000
    minIdle: 10
    maximumPoolSize: 30
    idleTimeout: 60000
    leakDetectionThreshold: 0
    keepaliveTime: 30000
    connectionTestQuery: SELECT 1
  db1:
    url: ******************************************************************************************************************************************************************************************
    username: pxb7_work_order
    password: pxb7_test#cLtxR##m
    driverClassName: com.mysql.cj.jdbc.Driver
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    validationTimeout: 5000
    connectionTimeout: 30000
    minIdle: 10
    maximumPoolSize: 30
    idleTimeout: 60000
    leakDetectionThreshold: 0
    keepaliveTime: 30000
    connectionTestQuery: SELECT 1
  db2:
    url: ******************************************************************************************************************************************************************************************
    username: pxb7_work_order
    password: pxb7_test#cLtxR##m
    driverClassName: com.mysql.cj.jdbc.Driver
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    validationTimeout: 5000
    connectionTimeout: 30000
    minIdle: 10
    maximumPoolSize: 30
    idleTimeout: 60000
    leakDetectionThreshold: 0
    keepaliveTime: 30000
    connectionTestQuery: SELECT 1
  db3:
    url: ******************************************************************************************************************************************************************************************
    username: pxb7_work_order
    password: pxb7_test#cLtxR##m
    driverClassName: com.mysql.cj.jdbc.Driver
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    validationTimeout: 5000
    connectionTimeout: 30000
    minIdle: 10
    maximumPoolSize: 30
    idleTimeout: 60000
    leakDetectionThreshold: 0
    keepaliveTime: 30000
    connectionTestQuery: SELECT 1
  db4:
    url: ******************************************************************************************************************************************************************************************
    username: pxb7_work_order
    password: pxb7_test#cLtxR##m
    driverClassName: com.mysql.cj.jdbc.Driver
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    validationTimeout: 5000
    connectionTimeout: 30000
    minIdle: 10
    maximumPoolSize: 30
    idleTimeout: 60000
    leakDetectionThreshold: 0
    keepaliveTime: 30000
    connectionTestQuery: SELECT 1
  db5:
    url: ******************************************************************************************************************************************************************************************
    username: pxb7_work_order
    password: pxb7_test#cLtxR##m
    driverClassName: com.mysql.cj.jdbc.Driver
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    validationTimeout: 5000
    connectionTimeout: 30000
    minIdle: 10
    maximumPoolSize: 30
    idleTimeout: 60000
    leakDetectionThreshold: 0
    keepaliveTime: 30000
    connectionTestQuery: SELECT 1
  db6:
    url: ******************************************************************************************************************************************************************************************
    username: pxb7_work_order
    password: pxb7_test#cLtxR##m
    driverClassName: com.mysql.cj.jdbc.Driver
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    validationTimeout: 5000
    connectionTimeout: 30000
    minIdle: 10
    maximumPoolSize: 30
    idleTimeout: 60000
    leakDetectionThreshold: 0
    keepaliveTime: 30000
    connectionTestQuery: SELECT 1
  db7:
    url: ******************************************************************************************************************************************************************************************
    username: pxb7_work_order
    password: pxb7_test#cLtxR##m
    driverClassName: com.mysql.cj.jdbc.Driver
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    validationTimeout: 5000
    connectionTimeout: 30000
    minIdle: 10
    maximumPoolSize: 30
    idleTimeout: 60000
    leakDetectionThreshold: 0
    keepaliveTime: 30000
    connectionTestQuery: SELECT 1
  db8:
    url: ******************************************************************************************************************************************************************************************
    username: pxb7_work_order
    password: pxb7_test#cLtxR##m
    driverClassName: com.mysql.cj.jdbc.Driver
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    validationTimeout: 5000
    connectionTimeout: 30000
    minIdle: 10
    maximumPoolSize: 30
    idleTimeout: 60000
    leakDetectionThreshold: 0
    keepaliveTime: 30000
    connectionTestQuery: SELECT 1
  db9:
    url: ******************************************************************************************************************************************************************************************
    username: pxb7_work_order
    password: pxb7_test#cLtxR##m
    driverClassName: com.mysql.cj.jdbc.Driver
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    validationTimeout: 5000
    connectionTimeout: 30000
    minIdle: 10
    maximumPoolSize: 30
    idleTimeout: 60000
    leakDetectionThreshold: 0
    keepaliveTime: 30000
    connectionTestQuery: SELECT 1
  db10:
    url: ******************************************************************************************************************************************************************************************
    username: pxb7_work_order
    password: pxb7_test#cLtxR##m
    driverClassName: com.mysql.cj.jdbc.Driver
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    validationTimeout: 5000
    connectionTimeout: 30000
    minIdle: 10
    maximumPoolSize: 30
    idleTimeout: 60000
    leakDetectionThreshold: 0
    keepaliveTime: 30000
    connectionTestQuery: SELECT 1
  db11:
    url: ******************************************************************************************************************************************************************************************
    username: pxb7_work_order
    password: pxb7_test#cLtxR##m
    driverClassName: com.mysql.cj.jdbc.Driver
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    validationTimeout: 5000
    connectionTimeout: 30000
    minIdle: 10
    maximumPoolSize: 30
    idleTimeout: 60000
    leakDetectionThreshold: 0
    keepaliveTime: 30000
    connectionTestQuery: SELECT 1
  db12:
    url: ******************************************************************************************************************************************************************************************
    username: pxb7_work_order
    password: pxb7_test#cLtxR##m
    driverClassName: com.mysql.cj.jdbc.Driver
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    validationTimeout: 5000
    connectionTimeout: 30000
    minIdle: 10
    maximumPoolSize: 30
    idleTimeout: 60000
    leakDetectionThreshold: 0
    keepaliveTime: 30000
    connectionTestQuery: SELECT 1
  db13:
    url: ******************************************************************************************************************************************************************************************
    username: pxb7_work_order
    password: pxb7_test#cLtxR##m
    driverClassName: com.mysql.cj.jdbc.Driver
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    validationTimeout: 5000
    connectionTimeout: 30000
    minIdle: 10
    maximumPoolSize: 30
    idleTimeout: 60000
    leakDetectionThreshold: 0
    keepaliveTime: 30000
    connectionTestQuery: SELECT 1
  db14:
    url: ******************************************************************************************************************************************************************************************
    username: pxb7_work_order
    password: pxb7_test#cLtxR##m
    driverClassName: com.mysql.cj.jdbc.Driver
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    validationTimeout: 5000
    connectionTimeout: 30000
    minIdle: 10
    maximumPoolSize: 30
    idleTimeout: 60000
    leakDetectionThreshold: 0
    keepaliveTime: 30000
    connectionTestQuery: SELECT 1
  db15:
    url: ******************************************************************************************************************************************************************************************
    username: pxb7_work_order
    password: pxb7_test#cLtxR##m
    driverClassName: com.mysql.cj.jdbc.Driver
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    validationTimeout: 5000
    connectionTimeout: 30000
    minIdle: 10
    maximumPoolSize: 30
    idleTimeout: 60000
    leakDetectionThreshold: 0
    keepaliveTime: 30000
    connectionTestQuery: SELECT 1
mode:
  type: Standalone


rules:
  - !SINGLE
    tables:
      - "db_single.*"
      - "bigdata_single.*"
      - "promotion_db.*"
  - !SHARDING
    tables:
      remind_delivery_product:
        actualDataNodes: db$->{0..15}.remind_delivery_product
        databaseStrategy:
          complex: #  standard 标准、complex 复合、hint. none
            shardingColumns: order_item_id,remind_id
            shardingAlgorithmName: custom_complex_sharding_algorithm
      remind_work_order:
        actualDataNodes: db$->{0..15}.remind_work_order
        databaseStrategy:
          complex:
            shardingColumns: work_order_id,remind_id
            shardingAlgorithmName: custom_complex_sharding_algorithm
      remind_after_sale:
        actualDataNodes: db$->{0..15}.remind_after_sale
        databaseStrategy:
          complex:
            shardingColumns: work_order_id,remind_id
            shardingAlgorithmName: custom_complex_sharding_algorithm
      remind_complaint:
        actualDataNodes: db$->{0..15}.remind_complaint
        databaseStrategy:
          complex:
            shardingColumns: work_order_id,remind_id
            shardingAlgorithmName: custom_complex_sharding_algorithm
      remind_plan_record:
        actualDataNodes: db$->{0..15}.remind_plan_record
        databaseStrategy:
          complex:
            shardingColumns: biz_id,remind_id
            shardingAlgorithmName: custom_complex_sharding_algorithm
#        bindingTables:
#          - remind_delivery_product, remind_plan_record
#          - remind_work_order, remind_plan_record
    shardingAlgorithms:
#      plan_record_inline:
#        type: INLINE
#        props:
#          algorithm-expression: db$->{Integer.parseInt(biz_id[-4..-1]) % 16} # 分片算法表达式
#          allow-range-query-with-inline-sharding: true # 允许范围查询
      custom_complex_sharding_algorithm:
        type: CLASS_BASED
        props:
          strategy: COMPLEX  #  STANDARD 标准、COMPLEX 复合、HINT
          algorithmClassName: com.pxb7.mall.workorder.infra.config.datasource.algorithm.WorkOrderComplexShardingAlgorithm # 自定义类
          DB_NAME: db  # db名称
          allow-range-query-with-inline-sharding: false # 自定义属性 （是否允许范围查询）

    defaultAuditStrategy:
      auditorNames:
        - sharding_key_required_auditor
      allowHintDisable: true
    auditors:
      sharding_key_required_auditor:
        type: CUSTOM_SHARDING_AUDIT_ALGORITHM
