package com.pxb7.mall.workorder.domain.mapping;

import com.pxb7.mall.workorder.domain.model.AfterSaleStatisticDataBO;
import com.pxb7.mall.workorder.domain.model.AfterSaleStatisticDataSearchBO;
import com.pxb7.mall.workorder.domain.model.RemindAfterSaleBO;
import com.pxb7.mall.workorder.domain.model.RemindAfterSaleReqBO;
import com.pxb7.mall.workorder.domain.model.RemindAfterSaleRespBO;
import com.pxb7.mall.workorder.infra.model.AfterSaleStatisticDataPO;
import com.pxb7.mall.workorder.infra.model.AfterSaleStatisticSearchPO;
import com.pxb7.mall.workorder.infra.model.RemindAfterSaleReqPO;
import com.pxb7.mall.workorder.infra.repository.db.entity.RemindAfterSale;

import java.util.List;

import com.pxb7.mall.workorder.infra.repository.es.entity.RemindAfterSaleDoc;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

@Mapper
public interface RemindAfterSaleDomainMapping {

    RemindAfterSaleDomainMapping INSTANCE = Mappers.getMapper(RemindAfterSaleDomainMapping.class);

    RemindAfterSaleReqPO.AddPO remindAfterSaleBO2AddPO(RemindAfterSaleReqBO.AddBO source);

    RemindAfterSaleReqPO.UpdatePO remindAfterSaleBO2UpdatePO(RemindAfterSaleReqBO.UpdateBO source);

    RemindAfterSaleReqPO.DelPO remindAfterSaleBO2DelPO(RemindAfterSaleReqBO.DelBO source);

    RemindAfterSaleReqPO.SearchPO remindAfterSaleBO2SearchPO(RemindAfterSaleReqBO.SearchBO source);

    RemindAfterSaleReqPO.PagePO remindAfterSaleBO2PagePO(RemindAfterSaleReqBO.PageBO source);

    RemindAfterSaleRespBO.DetailBO remindAfterSalePO2DetailBO(RemindAfterSale source);

    List<RemindAfterSaleRespBO.DetailBO> remindAfterSalePO2ListBO(List<RemindAfterSale> source);

    Page<RemindAfterSaleRespBO.DetailBO> remindAfterSalePO2PageBO(Page<RemindAfterSale> source);

    List<RemindAfterSaleBO> remindAfterSaleDoc2ListBO(List<RemindAfterSaleDoc> list);

    RemindAfterSaleDoc remindAfterSalePO2Doc(RemindAfterSale remindAfterSale);

    AfterSaleStatisticSearchPO remindAfterSaleStatisticBO2SearchPO(AfterSaleStatisticDataSearchBO searchBO);

    List<AfterSaleStatisticDataBO>
        afterSaleStatisticDataPO2BO(List<AfterSaleStatisticDataPO> afterSaleStatisticDataPOS);

    RemindAfterSaleBO remindAfterSaleDoc2BO(RemindAfterSaleDoc source);

}
