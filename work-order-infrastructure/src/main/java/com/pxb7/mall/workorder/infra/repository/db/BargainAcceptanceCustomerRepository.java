package com.pxb7.mall.workorder.infra.repository.db;

import com.baomidou.mybatisplus.extension.service.IService;
import com.pxb7.mall.workorder.infra.repository.db.entity.BargainAcceptanceCustomer;

/**
 * 还价客服接单信息表(BargainAcceptanceCustomer)表服务接口
 *
 * <AUTHOR>
 * @since 2025-04-18 17:22:02
 */
public interface BargainAcceptanceCustomerRepository extends IService<BargainAcceptanceCustomer> {

    BargainAcceptanceCustomer getByReceiveId(String receiveId);


    BargainAcceptanceCustomer getByBuyerId(String buyerId);
}

