package com.pxb7.mall.workorder.domain.message;

import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Set;

/**
 * <AUTHOR>
 * @version : BargainTicketDetailReadChangeDTO.java, v 0.1 2025年04月23日 15:32 yang.xuexi Exp $
 */
@Data
@Accessors(chain = true)
@ToString
public class BargainTicketDetailReadChangeMessage implements Serializable {

    /**
     * 议价单id集合
     */
    private Set<String> bargainIdSet;


    /**
     * 读标识 10:未读 20:已读
     */
    private Integer readFlag;

    /**
     * 客服接单id
     */
    private String receiveId;
}
