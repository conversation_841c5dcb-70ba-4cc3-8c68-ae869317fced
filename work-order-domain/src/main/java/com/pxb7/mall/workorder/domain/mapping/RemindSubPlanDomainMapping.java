package com.pxb7.mall.workorder.domain.mapping;

import com.pxb7.mall.workorder.domain.model.RemindSubPlanBO;
import com.pxb7.mall.workorder.domain.model.RemindSubPlanReqBO;
import com.pxb7.mall.workorder.domain.model.RemindSubPlanRespBO;
import com.pxb7.mall.workorder.infra.model.RemindSubPlanReqPO;
import com.pxb7.mall.workorder.infra.repository.db.entity.RemindSubPlan;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;


@Mapper
public interface RemindSubPlanDomainMapping {

    RemindSubPlanDomainMapping INSTANCE = Mappers.getMapper(RemindSubPlanDomainMapping.class);



    RemindSubPlanReqPO.AddPO remindSubPlanBO2AddPO(RemindSubPlanBO source);

    RemindSubPlanReqPO.UpdatePO remindSubPlanBO2UpdatePO(RemindSubPlanBO source);

    RemindSubPlanReqPO.DelPO remindSubPlanBO2DelPO(RemindSubPlanReqBO.DelBO source);

    RemindSubPlanReqPO.SearchPO remindSubPlanBO2SearchPO(RemindSubPlanReqBO.SearchBO source);

    RemindSubPlanReqPO.PagePO remindSubPlanBO2PagePO(RemindSubPlanReqBO.PageBO source);

    RemindSubPlanRespBO.DetailBO remindSubPlanPO2DetailBO(RemindSubPlan source);

    List<RemindSubPlanRespBO.DetailBO> remindSubPlanPO2ListBO(List<RemindSubPlan> source);

    List<RemindSubPlanBO> remindSubPlan2ListBO(List<RemindSubPlan> source);

    Page<RemindSubPlanRespBO.DetailBO> remindSubPlanPO2PageBO(Page<RemindSubPlan> source);

}


