package com.pxb7.mall.workorder.app.handler.bargain;

import com.pxb7.mall.workorder.client.enums.BargainTicketEventEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version : BargainTicketSyncFactory.java, v 0.1 2025年04月21日 16:04 yang.xuexi Exp $
 */
@Component
public class BargainTicketSyncFactory {

    private Map<String, BargainTicketSyncProcessor> strategyMap;

    @Autowired
    public BargainTicketSyncFactory(Map<String, BargainTicketSyncProcessor> strategyMap) {
        this.strategyMap = strategyMap;
    }

    public BargainTicketSyncProcessor getProcessor(BargainTicketEventEnum ticketEventEnum) {
        BargainTicketSyncProcessor syncProcessor = strategyMap.get(ticketEventEnum.name());
        if (Objects.isNull(syncProcessor)) {
            throw new UnsupportedOperationException("不支持的还价工单事件类型");
        }
        return syncProcessor;
    }
}
