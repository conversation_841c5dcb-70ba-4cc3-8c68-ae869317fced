package com.pxb7.mall.workorder.infra.repository.es.entity;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;
import lombok.experimental.FieldNameConstants;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 商品工单预警记录(RemindWorkOrder)实体类
 *
 * <AUTHOR>
 * @since 2025-03-27 20:07:15
 */
@Getter
@Setter
@Accessors(chain = true)
@ToString
@FieldNameConstants
@Document(indexName = "remind_work_order")
public class RemindWorkOrderDoc implements Serializable {
    @Serial
    private static final long serialVersionUID = -35373010568238370L;

    /**
     * 预警记录id
     */
    @Id
    @Field(value = "remind_id", type = FieldType.Keyword)
    private String remindId;
    /**
     * 工单id
     */
    @Field(value = "work_order_id", type = FieldType.Keyword)
    private String workOrderId;
    /**
     * 商品id
     */
    @Field(value = "product_id", type = FieldType.Keyword)
    private String productId;
    /**
     * 商品编码
     */
    @Field(value = "product_code", type = FieldType.Keyword)
    private String productCode;
    /**
     * 游戏id
     */
    @Field(value = "game_id", type = FieldType.Keyword)
    private String gameId;
    /**
     * 工单状态,1:待接单,2:已接单,3:待跟进
     */
    @Field(value = "work_order_status", type = FieldType.Keyword)
    private Integer workOrderStatus;
    /**
     * 完结状态,1:未完结,2:已完结,3:终止
     */
    @Field(value = "complete_status", type = FieldType.Keyword)
    private Integer completeStatus;
    /**
     * 超时状态，0:无,1:即将超时,2:已超时
     */
    @Field(value = "time_out_status", type = FieldType.Keyword)
    private Integer timeOutStatus;
    /**
     * 接单美工
     */
    @Field(value = "art_designer_id", type = FieldType.Keyword)
    private String artDesignerId;
    /**
     * 跟进人
     */
    @Field(value = "follower_id", type = FieldType.Keyword)
    private String followerId;
    /**
     * 审核客服
     */
    @Field(value = "audit_user_id", type = FieldType.Keyword)
    private String auditUserId;
    /**
     * 预期完结时间
     */
    @Field(type = FieldType.Date,format = DateFormat.date_hour_minute_second_fraction,value = "expect_complete_time")
    private LocalDateTime expectCompleteTime;
    /**
     * 完结时间
     */
    @Field(type = FieldType.Date,format = DateFormat.date_hour_minute_second_fraction,value = "complete_time")
    private LocalDateTime completeTime;
    /**
     * 预警计划游戏配置id,remind_plan_game_config
     */
    @Field(value = "game_config_id", type = FieldType.Keyword)
    private Long gameConfigId;
    /**
     * 预警计划id,remind_plan
     */
    @Field(value = "remind_plan_id", type = FieldType.Keyword)
    private Long remindPlanId;
    /**
     * 预警子计划id,remind_sub_plan
     */
    @Field(value = "remind_sub_plan_id", type = FieldType.Keyword)
    private Long remindSubPlanId;

    /**
     * 待跟进流入时间
     */
    @Field(type = FieldType.Date,format = DateFormat.date_hour_minute_second_fraction,value = "followed_up_inflow_time")
    private LocalDateTime followedUpInflowTime;

    /**
     * 创建时间
     */
    @Field(type = FieldType.Date,format = DateFormat.date_hour_minute_second_fraction,value = "create_time")
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @Field(type = FieldType.Date,format = DateFormat.date_hour_minute_second_fraction,value = "update_time")
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    @Field(value = "create_user_id", type = FieldType.Keyword)
    private String createUserId;
    /**
     * 更新人
     */
    @Field(value = "update_user_id", type = FieldType.Keyword)
    private String updateUserId;


    /**
     * 上架方式
     */
    @Field(value = "on_shelf_type", type = FieldType.Keyword)
    private Integer onShelfType;

}

