package com.pxb7.mall.workorder.domain.service;


import com.alibaba.cola.exception.Assert;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.alibaba.cola.exception.BizException;
import com.pxb7.mall.workorder.domain.model.*;
import com.pxb7.mall.workorder.infra.enums.RemindPlanOnShelfTypeEnum;
import com.pxb7.mall.workorder.infra.enums.RemindPlanServiceTypeEnum;
import com.pxb7.mall.workorder.infra.enums.RemindPlanWorkOrderStatusEnum;
import com.pxb7.mall.workorder.infra.enums.RemindPlanWorkOrderTypeEnum;
import com.pxb7.mall.workorder.infra.model.GameBasePO;
import com.pxb7.mall.workorder.infra.model.RemindPlanReqPO;
import com.pxb7.mall.workorder.infra.repository.db.RemindPlanRepository;
import com.pxb7.mall.workorder.infra.repository.db.entity.RemindPlan;
import com.pxb7.mall.workorder.infra.repository.gateway.dubbo.product.GameGateway;
import com.pxb7.mall.workorder.infra.util.TimeUtils;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.pxb7.mall.workorder.domain.mapping.RemindPlanDomainMapping;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.transaction.annotation.Transactional;

import static com.pxb7.mall.workorder.client.enums.BizErrorCodeEnum.PARAM_ERROR;

/**
 * 提醒服务预警计划domain服务
 *
 * <AUTHOR>
 * @since 2025-03-24 21:11:11
 */
@Service
public class RemindPlanDomainService {

    @Resource
    private RemindPlanRepository remindPlanRepository;

    @Resource
    private RemindSubPlanDomainService remindSubPlanDomainService;

    @Resource
    private RemindPlanRuleDomainService remindPlanRuleDomainService;

    @Resource
    private RemindPlanGameConfigDomainService remindPlanGameConfigDomainService;

    @Resource
    private RemindPlanOperateRecordDomainService remindPlanOperateRecordDomainService;

    @Resource
    private GameGateway gameGateway;

    @Transactional(rollbackFor = Throwable.class)
    public Long saveAllInfo(RemindPlanBO param, List<RemindPlanOperateRecordBO> remindPlanOperateRecordBOS) {
        boolean a = this.insert(param);
        List<RemindSubPlanBO> remindSubPlans = param.getRemindSubPlans();
        boolean b = remindSubPlanDomainService.saveBatch(remindSubPlans);
        List<RemindPlanGameConfigBO> remindPlanGameConfigs =
            remindSubPlans.stream().map(RemindSubPlanBO::getRemindPlanGameConfigs).flatMap(Collection::stream)
                .collect(Collectors.toList());
        boolean c = remindPlanGameConfigDomainService.saveBatch(remindPlanGameConfigs);
        List<RemindPlanRuleBO> planRules = param.getRemindPlanRules();
        boolean d = remindPlanRuleDomainService.saveBatch(planRules);
        boolean e =  remindPlanOperateRecordDomainService.saveBatch(remindPlanOperateRecordBOS);

        return param.getId();
    }

    @Transactional(rollbackFor = Throwable.class)
    public boolean updateAllInfo(RemindPlanUpdateContextBO remindPlanUpdateContextBO) {
        boolean a = this.update(remindPlanUpdateContextBO.getRemindPlan());
        boolean b=remindPlanRuleDomainService.refresh(remindPlanUpdateContextBO.getRemindPlanRuleRefresh());
        boolean c=remindPlanGameConfigDomainService.refresh(remindPlanUpdateContextBO.getRemindPlanGameConfigRefresh());
        boolean e =  remindPlanOperateRecordDomainService.saveBatch(remindPlanUpdateContextBO.getOperateRecords());
        return true;
    }

    public boolean insert(RemindPlanBO param){
        RemindPlanReqPO.AddPO addPO = RemindPlanDomainMapping.INSTANCE.remindPlanBO2AddPO(param);
        return remindPlanRepository.insert(addPO);
    }

    public boolean update(RemindPlanBO param) {
        RemindPlanReqPO.UpdatePO updatePO = RemindPlanDomainMapping.INSTANCE.remindPlanBO2UpdatePO(param);
        return remindPlanRepository.update(updatePO);
    }



    @Transactional(rollbackFor = Throwable.class)
    public boolean delete(RemindPlanDeleteContextBO remindPlanDeleteContextBO) {

        remindPlanOperateRecordDomainService.saveBatch(remindPlanDeleteContextBO.getOperateRecords());

        remindSubPlanDomainService.deleteByIds(remindPlanDeleteContextBO.getRemindSubPlanIds());
        remindPlanGameConfigDomainService.deleteByIds(remindPlanDeleteContextBO.getRemindPlanGameConfigIds());
        remindPlanRuleDomainService.deleteByIds(remindPlanDeleteContextBO.getRemindPlanRuleIds());

        RemindPlanReqPO.DelPO delPO = new RemindPlanReqPO.DelPO();
        delPO.setId(remindPlanDeleteContextBO.getRemindPlanId());
        return remindPlanRepository.deleteById(delPO);
    }

    public RemindPlanRespBO.DetailBO findById(Long id) {
        RemindPlan entity = remindPlanRepository.findById(id);
        return RemindPlanDomainMapping.INSTANCE.remindPlanPO2DetailBO(entity);
    }

    public Boolean existPlanName(String planName,Integer serviceType) {
        return remindPlanRepository.countByPlanName(planName,serviceType)>0;
    }

    /**
     * 勿扰时段
     * @param remindPlanId
     * @return
     */
    public NotDisturbPeriodBO getNotDisturbPeriod(Long remindPlanId) {
        RemindPlanRespBO.DetailBO remindPlan = findById(remindPlanId);
        if (Objects.isNull(remindPlan)) {
            return null;
        }
        return remindPlan.getNotDisturbPeriod();
    }

    public List<RemindPlanRespBO.DetailBO> list(RemindPlanReqBO.SearchBO param) {
        RemindPlanReqPO.SearchPO searchPO = RemindPlanDomainMapping.INSTANCE.remindPlanBO2SearchPO(param);
        List<RemindPlan> list = remindPlanRepository.list(searchPO);
        return RemindPlanDomainMapping.INSTANCE.remindPlanPO2ListBO(list);
    }

    public Page<RemindPlanBO> page(RemindPlanReqBO.PageBO param) {
        RemindPlanReqPO.PagePO pagePO = RemindPlanDomainMapping.INSTANCE.remindPlanBO2PagePO(param);
        Page<RemindPlan> page = remindPlanRepository.page(pagePO);
        return RemindPlanDomainMapping.INSTANCE.remindPlanPO2PageBO(page);
    }

    public RemindPlanBO findByRemindPlanId(Long remindPlanId) {
        RemindPlan plan = remindPlanRepository.findById(remindPlanId);
        return RemindPlanDomainMapping.INSTANCE.remindPlanPO2BO(plan);
    }


    public RemindPlanBO getRemindPlanAllInfo(Long remindPlanId) {
        // 查询预警计划
        RemindPlanBO remindPlanBO = this.findByRemindPlanId(remindPlanId);
        if (null == remindPlanBO) {
            return null;
        }

        // 查询预警子计划
        List<RemindSubPlanBO> remindSubPlanList = remindSubPlanDomainService.getRemindSubPlanList(remindPlanId);
        remindPlanBO.setRemindSubPlans(remindSubPlanList);

        // 查询预警计划游戏配置
        List<RemindPlanGameConfigBO> remindPlanGameConfigList =
            remindPlanGameConfigDomainService.getRemindPlanGameConfigList(remindPlanId);
        Map<Long, List<RemindPlanGameConfigBO>> subPlanGameConfigMap = remindPlanGameConfigList.stream()
            .collect(Collectors.groupingBy(RemindPlanGameConfigBO::getRemindSubPlanId));

        // 设置子计划对应的游戏配置
        for (RemindSubPlanBO remindSubPlanBO : remindSubPlanList) {
            remindSubPlanBO.setRemindPlanGameConfigs(subPlanGameConfigMap.get(remindSubPlanBO.getId()));
        }

        // 查询预警计划规则
        List<RemindPlanRuleBO> remindPlanRuleList = remindPlanRuleDomainService.getRemindPlanRuleList(remindPlanId);
        remindPlanBO.setRemindPlanRules(remindPlanRuleList);

        return remindPlanBO;
    }


    public List<RemindPlanGameConfigBO> getSelectedPlanGameConfigList(QuerySelectedGameBO selectedGameBO){
        RemindSubPlanReqBO.SearchBO param = new RemindSubPlanReqBO.SearchBO();
        Integer serviceType = selectedGameBO.getServiceType();
        RemindPlanServiceTypeEnum serviceTypeEnum = RemindPlanServiceTypeEnum.getEnum(serviceType);
        Assert.isTrue(serviceTypeEnum != null, PARAM_ERROR.getErrCode(), "服务类型暂不支持");
        if (RemindPlanServiceTypeEnum.DELIVERY_PRODUCT.equals(serviceTypeEnum)){
            Assert.notEmpty(selectedGameBO.getBusinessTypes(), PARAM_ERROR.getErrCode(), "业务类型不能为空");
            param.setBusinessTypes(selectedGameBO.getBusinessTypes());
        }else if (RemindPlanServiceTypeEnum.WORK_ORDER.equals(serviceTypeEnum)){
            List<Integer> workOrderStatuses = selectedGameBO.getWorkOrderStatuses();
            Assert.notEmpty(workOrderStatuses, PARAM_ERROR.getErrCode(), "工单状态不能为空");
            List<Integer> memberships = selectedGameBO.getMemberships();
            Assert.notEmpty(memberships, PARAM_ERROR.getErrCode(), "订单来源不能为空");
            if (workOrderStatuses.contains(RemindPlanWorkOrderStatusEnum.WAIT_ACCEPT.getValue())) {
                Assert.isTrue(workOrderStatuses.size()==1, PARAM_ERROR.getErrCode(), "工单状态:待接单不支持和其他转态一起参与筛选");
            }
            param.setWorkOrderStatuses(workOrderStatuses);
            param.setOnShelfTypes(RemindPlanOnShelfTypeEnum.getAllTypes());
            param.setMemberships(memberships);
        } else if (RemindPlanServiceTypeEnum.ASS_ORDER.equals(serviceTypeEnum)){
            if (selectedGameBO.getWorkOrderType().equals(RemindPlanWorkOrderTypeEnum.RETRIEVE.getValue()) && Objects.isNull(selectedGameBO.getMembership())) {
                throw new BizException("找回工单需配合订单来源一起筛选");
            }
            param.setWorkOrderType(selectedGameBO.getWorkOrderType());
            param.setMembership(selectedGameBO.getMembership());
        }
        List<RemindSubPlanRespBO.DetailBO> list = remindSubPlanDomainService.list(param);
        List<Long> subPlanIds = list.stream().map(RemindSubPlanRespBO.DetailBO::getId).toList();
        return remindPlanGameConfigDomainService.getRemindPlanGameConfigListBySubPlanIds(subPlanIds);
    }

    public List<OptionalGameRespBO> getOptionalGames(OptionalGameReqBO param) {
        QuerySelectedGameBO selectedGameBO = new QuerySelectedGameBO();
        selectedGameBO.setServiceType(param.getServiceType());
        selectedGameBO.setWorkOrderType(param.getWorkOrderType());
        selectedGameBO.setBusinessTypes(param.getBusinessTypes());
        selectedGameBO.setWorkOrderStatuses(param.getWorkOrderStatuses());
        selectedGameBO.setOnShelfTypes(param.getBusinessTypes());
        selectedGameBO.setMemberships(param.getMemberships());
        selectedGameBO.setMembership(param.getMembership());
        List<RemindPlanGameConfigBO> selectedPlanGameConfigList = this.getSelectedPlanGameConfigList(selectedGameBO);
        Map<String, RemindPlanGameConfigBO> gameIdToConfigMap = selectedPlanGameConfigList.stream()
            .collect(Collectors.toMap(RemindPlanGameConfigBO::getGameId, Function.identity(), (l, r) -> l));

        List<GameBasePO> allGameList = gameGateway.getAllGameList(param.getGameName());
        List<OptionalGameRespBO> returnList = new ArrayList<>();
        for (GameBasePO gameBasePO : allGameList) {
            OptionalGameRespBO optionalGameRespBO = new OptionalGameRespBO();
            optionalGameRespBO.setGameId(gameBasePO.getGameId());
            optionalGameRespBO.setGameName(gameBasePO.getGameName());
            optionalGameRespBO.setGameAlias(gameBasePO.getGameAlias());
            optionalGameRespBO.setMaker(gameBasePO.getMaker());
            RemindPlanGameConfigBO remindPlanGameConfigBO = gameIdToConfigMap.get(gameBasePO.getGameId());
            if (null == remindPlanGameConfigBO){
                optionalGameRespBO.setSelected(false);
                optionalGameRespBO.setSelectedRemindPlanId(null);
            }else {
                optionalGameRespBO.setSelected(true);
                optionalGameRespBO.setSelectedRemindPlanId(remindPlanGameConfigBO.getRemindPlanId());
            }
            returnList.add(optionalGameRespBO);
        }
        return returnList;
    }

    /**
     * 判断提醒时间是否在防打扰时间段内，如果是，则设置为防打扰的结束时间
     * @param remindTime 提醒时间
     * @param notDisturbPeriod 防打扰时间段
     * @return 调整后的提醒时间
     */
    public LocalDateTime adjustRemindTimeIfInNotDisturbPeriod(LocalDateTime remindTime, NotDisturbPeriodBO notDisturbPeriod) {
        if (Objects.isNull(notDisturbPeriod)
                || StringUtils.isBlank(notDisturbPeriod.getStartTime())
                || StringUtils.isBlank(notDisturbPeriod.getEndTime())) {
            return remindTime;
        }
        //勿扰时段，{"startTime":"22:00","endTime":"08:00"}
        //转换成时间格式yyyy-MM-dd HH:mm:ss
        String dateStr = TimeUtils.convertLocalDateTimeToDateStr(remindTime);
        if (StringUtils.isBlank(dateStr)) {
            return remindTime;
        }
        String startDateTimeStr = dateStr.concat(" ").concat(notDisturbPeriod.getStartTime()).concat(":00");
        String endDateTimeStr = dateStr.concat(" ").concat(notDisturbPeriod.getEndTime()).concat(":00");
        LocalDateTime startTime = TimeUtils.parseDateTimeStr(startDateTimeStr);
        LocalDateTime endTime = TimeUtils.parseDateTimeStr(endDateTimeStr);

        // 如果结束时间小于开始时间，说明跨天
        if (endTime.isBefore(startTime)) {
            if (endTime.isAfter(remindTime)) {
                return endTime.plusMinutes(1);
            }
            endTime = endTime.plusDays(1);
        }

        // 判断提醒时间是否在防打扰时间段内
        if ((remindTime.isAfter(startTime) || remindTime.isEqual(startTime))
                && remindTime.isBefore(endTime)) {
            return endTime.plusMinutes(1);
        }

        return remindTime;
    }

}

