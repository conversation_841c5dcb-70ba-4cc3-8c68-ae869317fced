package com.pxb7.mall.workorder.app.handler.bargain;

import com.alibaba.fastjson.JSON;
import com.pxb7.mall.workorder.client.enums.BargainTicketReadFlagEnum;
import com.pxb7.mall.workorder.domain.message.BargainTicketMessage;
import com.pxb7.mall.workorder.infra.repository.db.entity.BargainAcceptanceCustomer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version : BargainInfoChangeProcessor.java, v 0.1 2025年04月21日 16:26 yang.xuexi Exp $
 */
@Component("BARGAIN_TICKET_READ_STATUS_CHANGE")
@Slf4j
public class BargainTicketReadStatusChangeProcessor extends AbstractBargainTicketSyncProcessor{

    @Override
    protected void doProcess(BargainTicketMessage message, BargainAcceptanceCustomer byReceiveId) {
        BargainTicketReadFlagEnum readFlagEnum = BargainTicketReadFlagEnum.of(message.getReadFlag());
        if(Objects.isNull(readFlagEnum)){
            log.warn("BargainTicketMessage readFlag un valid:{}", JSON.toJSONString(message));
            return;
        }
        bargainTicketDocEsRepository.updateReadFlag(byReceiveId.getReceiveId(), message.getReadFlag());
    }
}
