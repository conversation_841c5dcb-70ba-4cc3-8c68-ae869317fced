package com.pxb7.mall.workorder.app.service;


import com.alibaba.cola.exception.Assert;
import com.alibaba.cola.exception.BizException;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pxb7.mall.auth.c.util.AdminUserUtil;
import com.pxb7.mall.auth.dto.AdminUserDTO;
import com.pxb7.mall.product.client.dto.response.product.WorkOrderRpcResp;
import com.pxb7.mall.workorder.app.mapping.RemindWorkOrderAppMapping;
import com.pxb7.mall.workorder.app.model.RemindWorkOrderReqDTO;
import com.pxb7.mall.workorder.app.model.WorkOrderPageDetailDTO;
import com.pxb7.mall.workorder.app.model.WorkOrderStatisticDataDTO;
import com.pxb7.mall.workorder.app.model.WorkOrderStatisticDataSearchDTO;
import com.pxb7.mall.workorder.app.util.RemindPlanPageBuildUtil;
import com.pxb7.mall.workorder.app.util.TimeTipUtil;
import com.pxb7.mall.workorder.client.enums.BizTypeEnum;
import com.pxb7.mall.workorder.client.enums.CompleteStatusEnum;
import com.pxb7.mall.workorder.client.enums.WorkOrderStatusEnum;
import com.pxb7.mall.workorder.domain.model.*;
import com.pxb7.mall.workorder.domain.service.RemindPlanGameConfigDomainService;
import com.pxb7.mall.workorder.domain.service.RemindPlanRecordDomainService;
import com.pxb7.mall.workorder.domain.service.RemindWorkOrderDomainService;
import com.pxb7.mall.workorder.infra.aop.ClusterRedisLock;
import com.pxb7.mall.workorder.infra.model.GameBasePO;
import com.pxb7.mall.workorder.infra.model.SysUserRespPO;
import com.pxb7.mall.workorder.infra.repository.gateway.dubbo.product.GameGateway;
import com.pxb7.mall.workorder.infra.repository.gateway.dubbo.product.ProductGateway;
import com.pxb7.mall.workorder.infra.repository.gateway.dubbo.user.SysUserGateway;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.pxb7.mall.workorder.client.enums.BizErrorCodeEnum.PARAM_ERROR;
import static com.pxb7.mall.workorder.client.enums.BizErrorCodeEnum.USER_INFO_NOT_FOUND;

/**
 * 商品工单预警记录app服务
 *
 * <AUTHOR>
 * @since 2025-04-07 12:00:07
 */

@Slf4j
@Service
public class RemindWorkOrderAppService {

    @Resource
    private RemindWorkOrderDomainService workOrderDomainService;

    @Resource
    private RemindPlanRecordDomainService planRecordDomainService;

    @Resource
    private RemindPlanGameConfigDomainService gameConfigDomainService;

    @Resource
    private ProductGateway productGateway;

    @Resource
    private SysUserGateway sysUserGateway;

    @Resource
    private GameGateway gameGateway;

    /**
     * 分页查询工单信息
     *
     * @param param 分页查询参数，包含筛选条件和分页信息
     * @return 返回分页的工单详细信息DTO列表
     */
    public Page<WorkOrderPageDetailDTO> page(RemindWorkOrderReqDTO.PageDTO param) {

        AdminUserDTO adminUser = AdminUserUtil.getAdminUser();
        Assert.isTrue(adminUser != null && StringUtils.isNotBlank(adminUser.getUserId()), USER_INFO_NOT_FOUND.getErrCode(), USER_INFO_NOT_FOUND.getErrDesc());

        // 将查询参数转换为业务对象，以便在领域服务中使用
        RemindWorkOrderReqBO.PageBO pageBO = RemindWorkOrderAppMapping.INSTANCE.remindWorkOrderDTO2PageBO(param);
        // 调用领域服务执行分页查询，获取工作订单列表
        Page<RemindWorkOrderBO> page = workOrderDomainService.pageEs(pageBO);

        // 将查询结果转换为DTO形式，以便返回给客户端
        Page<WorkOrderPageDetailDTO> pageDTO = RemindWorkOrderAppMapping.INSTANCE.remindWorkOrderBO2PageDTO(page);

        // 提取并处理工作订单列表中的用户ID，用于后续查询用户信息
        List<WorkOrderPageDetailDTO> records = Optional.of(pageDTO).map(Page::getRecords).orElse(new ArrayList<>());

        Set<String> userIds = RemindPlanPageBuildUtil.extractUserIdsFromWorkOrder(records);
        List<SysUserRespPO> sysUserInfoList = sysUserGateway.getSysUserInfoList(userIds);
        Map<String, SysUserRespPO> sysUserRespPOMap = sysUserInfoList.stream().collect(Collectors.toMap(SysUserRespPO::getUserId, Function.identity()));

        List<String> gameIds = records.stream().map(WorkOrderPageDetailDTO::getGameId).toList();
        List<GameBasePO> gameBasePOList = gameGateway.getGameInfoByIds(gameIds);
        Map<String, GameBasePO> gameBasePOMap = gameBasePOList.stream().collect(Collectors.toMap(GameBasePO::getGameId, Function.identity()));



        // 定义一个双函数，用于根据用户ID获取用户名
        BiFunction<Map<String, SysUserRespPO>,String,String> biFunction=(Map<String, SysUserRespPO> userRespPOMap,String userId)->{
            if (StringUtils.isBlank(userId)){
                return null;
            }
            return Optional.ofNullable(userRespPOMap).map(a->a.get(userId)).map(SysUserRespPO::getUserName).orElse(null);
        };

        // 遍历工作订单列表，设置用户名称信息、提示信息等
        records.forEach(a -> {
            a.setArtDesignerName(biFunction.apply(sysUserRespPOMap,a.getArtDesignerId()));
            a.setFollowerName(biFunction.apply(sysUserRespPOMap,a.getFollowerId()));
            a.setAuditUserName(biFunction.apply(sysUserRespPOMap,a.getAuditUserId()));

            if (StringUtils.isNotBlank(a.getGameId())){
                String userName = Optional.of(gameBasePOMap).map(s -> s.get(a.getGameId()))
                    .map(GameBasePO::getGameName).orElse(null);
                a.setGameName(userName);
            }

            a.setDurationTip(TimeTipUtil.getDurationTip(
                    Objects.isNull(a.getFollowedUpInflowTime()) ? a.getCreateTime() : a.getFollowedUpInflowTime(),
                    Objects.isNull(a.getCompleteTime()) ? LocalDateTime.now() : a.getCompleteTime()));
            a.setCountDownTip(TimeTipUtil.getCountDownTip(
                    Objects.isNull(a.getCompleteTime()) ? LocalDateTime.now() : a.getCompleteTime(), a.getExpectCompleteTime()));
        });

        // 返回设置有用户名称信息的工作订单分页列表
        return pageDTO.setRecords(records);
    }


    /**
     * 根据商品工单状态变化生成提醒计划
     *
     * @param workOrderId 商品工单id
     */
    @Transactional(rollbackFor = Exception.class)
    @ClusterRedisLock(prefix = "work_order_plan", value = "#workOrderId")
    public void generateRemindPlan(String workOrderId) {
        //获取工单信息
        WorkOrderRpcResp workOrderInfo = productGateway.getWorkOrderInfo(workOrderId);
        if (Objects.isNull(workOrderInfo)) {
            log.error("商品工单信息不存在, workOrderId:{}", workOrderId);
            return;
        }
        //游戏id
        String gameId = workOrderInfo.getGameId();
        if (StringUtils.isBlank(gameId)) {
            log.error("游戏id不能为空, workOrderId:{}", workOrderId);
            return;
        }

        //状态 1=待接单   2=已接单   3=已完结  4=失效 5=机器冻结 6=待跟进
        Integer workOrderStatus = workOrderInfo.getStatus();
        //修改之前生成的未完结的预警记录状态
        changePreWorkOrderStatus(workOrderStatus, workOrderId);

        WorkOrderStatusEnum workOrderStatusEnum = WorkOrderStatusEnum.getByWorkOrderStatus(workOrderStatus);
        if (Objects.isNull(workOrderStatusEnum)) {
            return;
        }
        Integer statusCode = workOrderStatusEnum.getCode();
        //截图方式  1: 官方截图  2：自主截图
        Integer onShelfType = workOrderInfo.getScreen();
        //1 散户工单 2 号商工单 3 3A工单
        Integer membership = workOrderInfo.getMembership();
        //根据gameId和工单信息查询预警计划游戏配置
        RemindPlanGameConfigBO remindPlanGameConfig =
                gameConfigDomainService.getRemindPlanGameConfig(gameId, statusCode, onShelfType, membership);
        if (Objects.isNull(remindPlanGameConfig)) {
            log.warn("游戏id对应的预警计划不存在, " +
                    "gameId:{}, workOrderStatus:{}, onShelfType:{}, membership:{}", gameId, statusCode, onShelfType, membership);
            return;
        }
        TimeConfigBO expectCompleteTimeConfig = remindPlanGameConfig.getExpectCompleteTimeConfig();
        if (Objects.isNull(expectCompleteTimeConfig)
                || (Objects.isNull(expectCompleteTimeConfig.getHours()) && Objects.isNull(expectCompleteTimeConfig.getMinutes()))) {
            log.warn("游戏id对应的预警计划预期完结时间没有配置, gameId:{}, remindPlanId:{}", gameId, remindPlanGameConfig.getRemindPlanId());
            return;
        }
        //生成商品工单预警记录
        RemindWorkOrderReqBO.AddBO remindWorkOrder =
                workOrderDomainService.generateRemindWorkOrder(workOrderInfo, remindPlanGameConfig, statusCode);
        //生成商品工单预警执行计划记录
        generateWorkOrderPlanRecords(remindWorkOrder);
    }

    /**
     * 生成商品工单预警执行计划记录
     * @param remindWorkOrder
     */
    private void generateWorkOrderPlanRecords(RemindWorkOrderReqBO.AddBO remindWorkOrder) {
        RemindPlanRecordBO remindPlanRecordBO = new RemindPlanRecordBO()
                .setRecordIdPrefix("WOP")
                .setBizType(BizTypeEnum.WORK_ORDER.getType())
                .setBizId(remindWorkOrder.getWorkOrderId())
                .setRemindId(remindWorkOrder.getRemindId())
                .setRemindPlanId(remindWorkOrder.getRemindPlanId())
                .setRemindSubPlanId(remindWorkOrder.getRemindSubPlanId())
                .setExpectCompleteTime(remindWorkOrder.getExpectCompleteTime());
        //生成预警执行计划记录
        planRecordDomainService.generatePlanRecords(remindPlanRecordBO);
    }

    /**
     * 修改之前生成的未完结的预警记录状态
     * @param workOrderStatus
     * @param workOrderId
     */
    private void changePreWorkOrderStatus(Integer workOrderStatus, String workOrderId) {
        //待接单
        if (Objects.equals(workOrderStatus, 1)) {
            //如果工单是待接单
            //删除之前生成的未完结的预警记录
            workOrderDomainService.deleteByWorkOrderId(workOrderId);
            //删除待执行和执行中预警执行计划记录
            planRecordDomainService.deleteByBizId(workOrderId);
        } else {
            //修改之前生成的未完结的预警记录为已完结
            Integer completeStatus = Objects.equals(workOrderStatus, 2) || Objects.equals(workOrderStatus, 3) ?
                    CompleteStatusEnum.DONE.getCode() : CompleteStatusEnum.CANCELED.getCode();
            //更新账号交付预警记录状态
            RemindWorkOrderReqBO.UpdateBO updateBO = new RemindWorkOrderReqBO.UpdateBO();
            updateBO.setWorkOrderId(workOrderId);
            updateBO.setCompleteStatus(completeStatus);
            updateBO.setCompleteTime(LocalDateTime.now());
            workOrderDomainService.updateByWorkOrderId(updateBO);
            //修改待执行和执行中待预警执行计划记录为已失效
            planRecordDomainService.invalidPlanRecord(BizTypeEnum.WORK_ORDER.getType(), workOrderId);
        }
    }

    public List<WorkOrderStatisticDataDTO> getStatisticList(WorkOrderStatisticDataSearchDTO param) {

        AdminUserDTO adminUser = AdminUserUtil.getAdminUser();
        Assert.isTrue(adminUser != null && StringUtils.isNotBlank(adminUser.getUserId()), USER_INFO_NOT_FOUND.getErrCode(), USER_INFO_NOT_FOUND.getErrDesc());

        if (StringUtils.isBlank(param.getStartDate())|| StringUtils.isBlank(param.getEndDate())){
            LocalDateTime nowDateTime = LocalDateTime.now();
            LocalDateTime starDateTime = nowDateTime.minusDays(31);
            param.setStartDate(starDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            param.setEndDate(nowDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        }else{
            LocalDate startDate;
            LocalDate endDate;
            try {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                startDate = LocalDate.parse(param.getStartDate(), formatter);
                endDate = LocalDate.parse(param.getEndDate(), formatter);
            } catch (Exception e) {
                throw new BizException(PARAM_ERROR.getErrCode(), "日期格式不正确");
            }
            Assert.isTrue(!startDate.isAfter(endDate), PARAM_ERROR.getErrCode(), "创建开始时间要早于创建结束时间");
            long daysBetween = Math.abs(ChronoUnit.DAYS.between(startDate, endDate));
            Assert.isTrue(daysBetween <= 31, PARAM_ERROR.getErrCode(), "筛选的时间间隔不能超过31天");

        }

        WorkOrderStatisticDataSearchBO searchBO = RemindWorkOrderAppMapping.INSTANCE.statisticParam2BO(param);
        List<WorkOrderStatisticDataBO> list = workOrderDomainService.getStatisticList(searchBO);

        List<String> gameIds = list.stream().map(WorkOrderStatisticDataBO::getGameId).toList();
        List<GameBasePO> gameBasePOList = gameGateway.getGameInfoByIds(gameIds);
        Map<String, GameBasePO> gameBasePOMap = gameBasePOList.stream().collect(Collectors.toMap(GameBasePO::getGameId, Function.identity()));


        Set<String> userIds = RemindPlanPageBuildUtil.extractUserIdsFromWorkOrderStatisticList(list);
        List<SysUserRespPO> sysUserInfoList = sysUserGateway.getSysUserInfoList(userIds);
        Map<String, SysUserRespPO> sysUserRespPOMap = sysUserInfoList.stream().collect(Collectors.toMap(SysUserRespPO::getUserId, Function.identity()));

        // 定义一个双函数，用于根据用户ID获取用户名
        BiFunction<Map<String, SysUserRespPO>,String,String> biFunction=(Map<String, SysUserRespPO> userRespPOMap,String userId)->{
            if (StringUtils.isBlank(userId)){
                return null;
            }
            return Optional.ofNullable(userRespPOMap).map(a->a.get(userId)).map(SysUserRespPO::getUserName).orElse(null);
        };


        List<WorkOrderStatisticDataDTO> reulstList= RemindWorkOrderAppMapping.INSTANCE.statisticResul2DTOList(list);

        for (WorkOrderStatisticDataDTO dataDTO : reulstList) {
            String gameId = dataDTO.getGameId();
            if (StringUtils.isNotBlank(gameId)) {
                GameBasePO gameBasePO = gameBasePOMap.get(gameId);
                dataDTO.setGameName(null == gameBasePO ? null : gameBasePO.getGameName());
            }
            dataDTO.setCustomerCareName(biFunction.apply(sysUserRespPOMap,dataDTO.getCustomerCareId()));
            dataDTO.setFollowerName(biFunction.apply(sysUserRespPOMap,dataDTO.getFollowerId()));
            dataDTO.setArtDesignerName(biFunction.apply(sysUserRespPOMap,dataDTO.getArtDesignerId()));
        }

        return reulstList;
    }
}

