package com.pxb7.mall.workorder.infra.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum RemindPlanBusinessTypeEnum {

    /**
     * 1:代售，2:中介，服务类型为账号交付有值
     */
    AGENT_SALE(1, "代售"),
    AGENCY_SALE(2, "中介"),
    ;

    private final Integer value;

    private final String label;

    public static String getLabel(Integer value) {
        return Arrays.stream(RemindPlanBusinessTypeEnum.values()).filter(e -> e.getValue().equals(value))
            .map(RemindPlanBusinessTypeEnum::getLabel).findAny().orElse("");
    }

    public static RemindPlanBusinessTypeEnum getEnum(Integer value) {
        if (null == value) {
            return null;
        }
        return Arrays.stream(RemindPlanBusinessTypeEnum.values()).filter(e -> value.equals(e.getValue())).findFirst()
            .orElse(null);
    }

}
