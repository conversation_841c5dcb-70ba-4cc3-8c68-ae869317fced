package com.pxb7.mall.workorder.app.model;

import com.pxb7.mall.workorder.infra.enums.RemindPlanServiceTypeEnum;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 提醒服务预警计划(RemindPlan)实体类
 *
 * <AUTHOR>
 * @since 2025-03-24 21:12:15
 */
public class AssRemindPlanReqDTO {

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class AddDTO {
        /**
         * 预警计划名称
         */
        @NotBlank(message = "请输入预警计划名称")
        private String planName;
        /**
         * 业务类型 1:账号交付服务 2:商品工单服务 3:售后工单 4客诉工单
         * @see RemindPlanServiceTypeEnum
         */
        @NotNull(message = "业务类型不能为空")
        private Integer serviceType;
        /**
         * 工单类型  1:找回 2:纠纷 服务类型为售后工单有值
         */
        @NotNull(message = "请选择工单类型")
        private Integer workOrderType;
        /**
         * 订单来源(卖家类型)：1 散户工单 2 号商工单 3 3A工单，服务类型为【商品工单】【售后-找回工单】有值找回工单使用
         */
        private Integer membership;

        /**
         * 预警游戏配置
         */
        @Valid
        @NotEmpty(message = "预警设置不能为空")
        private List<AssPlanGameConfigDTO> planGameConfigs;
        /**
         * 预警规则
         */
        @Valid
        @NotEmpty(message = "提醒规则不能为空")
        private List<PlanRuleDTO> planRules;
        /**
         * 免打扰时间段 {"startTime":"22:00","endTime":"08:00"}
         */
        private NotDisturbPeriodDTO notDisturbPeriod;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class UpdateDTO {

        @NotNull(message = "预警计划Id不能为空")
        private Long id;

        /**
         * 预警计划名称
         */
        @NotBlank(message = "请输入预警计划名称")
        private String planName;

        /**
         * 业务类型 1:账号交付服务 2:商品工单服务 3:售后工单 4客诉工单
         * @see RemindPlanServiceTypeEnum
         */
        @NotNull(message = "业务类型不能为空")
        private Integer serviceType;

        /**
         * 工单类型  1:找回 2:纠纷 服务类型为售后工单有值
         */
        @NotNull(message = "请选择工单类型")
        private Integer workOrderType;

        /**
         * 订单来源(会员类型)：1 散户工单 2 号商工单 3 3A工单，服务类型为【商品工单】【售后-找回工单】有值找回工单使用
         */
        private Integer membership;

        /**
         * 预警游戏配置
         */
        @Valid
        @NotEmpty(message = "预警设置不能为空")
        private List<AssPlanGameConfigDTO> planGameConfigs;

        /**
         * 预警规则
         */
        @Valid
        @NotEmpty(message = "提醒规则不能为空")
        private List<PlanRuleDTO> planRules;

        /**
         * 免打扰时间段 {"startTime":"22:00","endTime":"08:00"}
         */
        //@NotBlank(message = "免打扰时间段不能为空")
        private NotDisturbPeriodDTO notDisturbPeriod;

    }


    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class DelDTO {
        @NotNull(message = "id不能为空")
        private Long id;
    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class SearchDTO {

        /**
         * 预警计划名称
         */
        private String planName;

        /**
         * 工单类型  1:找回 2:纠纷 服务类型为售后工单有值
         */
        private Integer workOrderType;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class PageDTO {

        /**
         * 预警计划名称
         */
        private String planName;

        /**
         * 工单类型  1:找回 2:纠纷 服务类型为售后工单有值
         */
        private Integer workOrderType;

        /**
         * 页码，从1开始
         */
        @NotNull(message = "页码不能为空")
        @Min(value = 1, message = "页码不能小于1")
        private Integer pageIndex;
        /**
         * 每页数量
         */
        @NotNull(message = "每页数量不能为空")
        @Min(value = 1, message = "每页数量不能小于1")
        private Integer pageSize;

    }

}

