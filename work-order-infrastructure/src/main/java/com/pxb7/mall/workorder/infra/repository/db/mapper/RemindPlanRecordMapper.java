package com.pxb7.mall.workorder.infra.repository.db.mapper;

import com.pxb7.mall.workorder.infra.repository.db.entity.RemindPlanRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 预警执行计划记录(RemindPlanRecord)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-04-12 14:13:00
 */
@Mapper
public interface RemindPlanRecordMapper extends BaseMapper<RemindPlanRecord> {
    /**
     * 批量新增数据
     *
     * @param entities List<RemindPlanRecord> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<RemindPlanRecord> entities);

    /**
     * 批量修改状态
     * @param ids
     * @param status
     */
    int batchUpdateStatusByIds(List<Long> ids, Integer status);
}

