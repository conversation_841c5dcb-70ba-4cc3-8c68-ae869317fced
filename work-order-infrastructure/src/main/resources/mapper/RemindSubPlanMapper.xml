<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pxb7.mall.workorder.infra.repository.db.mapper.RemindSubPlanMapper">
    <resultMap id="BaseResultMap" type="com.pxb7.mall.workorder.infra.repository.db.entity.RemindSubPlan">
        <result column="id" jdbcType="BIGINT" property="id"/>
        <result column="business_type" jdbcType="INTEGER" property="businessType"/>
        <result column="work_order_status" jdbcType="INTEGER" property="workOrderStatus"/>
        <result column="on_shelf_type" jdbcType="INTEGER" property="onShelfType"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_user_id" jdbcType="VARCHAR" property="createUserId"/>
        <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId"/>
        <result column="is_deleted" jdbcType="BOOLEAN" property="deleted"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into
        remind_sub_plan(business_type,work_order_status,on_shelf_type,create_user_id,update_user_id)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.businessType},#{entity.workOrderStatus},#{entity.onShelfType},#{entity.createUserId},#{entity.updateUserId})
        </foreach>
    </insert>
</mapper>

