package com.pxb7.mall.workorder.domain.mapping;

import com.pxb7.mall.workorder.domain.model.NotDisturbPeriodBO;
import com.pxb7.mall.workorder.domain.model.RemindPlanBO;
import com.pxb7.mall.workorder.domain.model.RemindPlanReqBO;
import com.pxb7.mall.workorder.domain.model.RemindPlanRespBO;
import com.pxb7.mall.workorder.infra.model.RemindPlanReqPO;
import com.pxb7.mall.workorder.infra.repository.db.entity.RemindPlan;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

@Mapper(
    imports = {com.alibaba.fastjson2.JSON.class, NotDisturbPeriodBO.class})
public interface RemindPlanDomainMapping {

    RemindPlanDomainMapping INSTANCE = Mappers.getMapper(RemindPlanDomainMapping.class);

    @Mapping(target = "notDisturbPeriod", expression = "java(JSON.toJSONString(source.getNotDisturbPeriod()))")
    RemindPlanReqPO.AddPO remindPlanBO2AddPO(RemindPlanBO source);

    @Mapping(target = "notDisturbPeriod", expression = "java(JSON.parseObject(source.getNotDisturbPeriod(), NotDisturbPeriodBO.class))")
    RemindPlanBO remindPlanPO2BO(RemindPlan source);

    @Mapping(target = "notDisturbPeriod", expression = "java(JSON.toJSONString(source.getNotDisturbPeriod()))")
    RemindPlanReqPO.UpdatePO remindPlanBO2UpdatePO(RemindPlanBO source);

    RemindPlanReqPO.DelPO remindPlanBO2DelPO(RemindPlanReqBO.DelBO source);

    RemindPlanReqPO.SearchPO remindPlanBO2SearchPO(RemindPlanReqBO.SearchBO source);

    RemindPlanReqPO.PagePO remindPlanBO2PagePO(RemindPlanReqBO.PageBO source);

    @Mapping(target = "notDisturbPeriod", expression = "java(JSON.parseObject(source.getNotDisturbPeriod(), NotDisturbPeriodBO.class))")
    RemindPlanRespBO.DetailBO remindPlanPO2DetailBO(RemindPlan source);

    List<RemindPlanRespBO.DetailBO> remindPlanPO2ListBO(List<RemindPlan> source);

    Page<RemindPlanBO> remindPlanPO2PageBO(Page<RemindPlan> source);

}


