package com.pxb7.mall.workorder.infra.enums;

import lombok.Getter;

/**
 * 本地或redis缓存枚举
 * <AUTHOR>
 */
@Getter
public enum LocalOrRedisCacheEnum {

    WORK_ORDER_ON_SHELF_TYPE(Constants.WORK_ORDER_ON_SHELF_TYPE, 1000, 3600 * 1000, 1800 * 1000, true),
    SYS_USER_BY_USER_ID(Constants.SYS_USER_BY_USER_ID, 1000, 3600 * 1000, 1800 * 1000, true),
    SYS_USER_BY_USER_NAME(Constants.SYS_USER_BY_USER_NAME, 1000, 3600 * 1000, 1800 * 1000, true),
    ;

    LocalOrRedisCacheEnum(String name, int capacity, long ttl, long maxIdleTime, boolean localFlag) {
        this.name = name;
        this.capacity = capacity;
        this.ttl = ttl;
        this.maxIdleTime = maxIdleTime;
        this.localFlag = localFlag;
    }

    private String name;
    private int capacity;
    private long ttl;
    private long maxIdleTime;
    private boolean localFlag;

    public static class Constants {

        public static final String WORK_ORDER_ON_SHELF_TYPE = "work_order_on_shelf_type";
        public static final String SYS_USER_BY_USER_ID = "sys_user_by_user_id";
        public static final String SYS_USER_BY_USER_NAME = "sys_user_by_user_name";

    }

}
