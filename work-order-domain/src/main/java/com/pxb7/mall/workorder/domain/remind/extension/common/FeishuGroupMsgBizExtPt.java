package com.pxb7.mall.workorder.domain.remind.extension.common;

import com.alibaba.cola.extension.Extensions;
import com.pxb7.mall.workorder.client.constant.MessageTemplateConstant;
import com.pxb7.mall.workorder.client.enums.BizTypeEnum;
import com.pxb7.mall.workorder.client.enums.RemindMethodEnum;
import com.pxb7.mall.workorder.client.enums.RemindObjectEnum;
import com.pxb7.mall.workorder.client.message.RemindPlanMessage;
import com.pxb7.mall.workorder.domain.model.*;
import com.pxb7.mall.workorder.domain.remind.extension.BaseMessageService;
import com.pxb7.mall.workorder.domain.remind.extension.RemindBizExtPt;
import com.pxb7.mall.workorder.domain.remind.extension.context.RemindBizContext;
import com.pxb7.mall.workorder.infra.enums.RemindPlanWorkOrderTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * 飞书消息提醒，群组
 * <AUTHOR>
 * @date 2025/4/2 14:56
 */

@Slf4j
@Extensions(
        bizId = {BizTypeEnum.Constants.DELIVERY_PRODUCT,
                BizTypeEnum.Constants.WORK_ORDER,
                BizTypeEnum.Constants.AFTER_SALE,
                BizTypeEnum.Constants.COMPLAINT},
        useCase = {RemindMethodEnum.Constants.FEISHU},
        scenario = {RemindObjectEnum.Constants.FEISHU_GROUP})
public class FeishuGroupMsgBizExtPt extends BaseMessageService implements RemindBizExtPt {

    @Override
    public void executeRemindTask(RemindBizContext context) {
        RemindPlanMessage remindPlanMessage = context.getRemindPlanMessage();
        RemindMethodConfigBO.RemindObjectBO remindObjectBO = context.getRemindObjectBO();
        BaseRemindRecordBO remindRecord = context.getRemindRecord();

        RemindSubPlanRespBO.DetailBO remindSubPlan =
                subPlanDomainService.findById(remindPlanMessage.getRemindSubPlanId());

        String title;
        String content;
        if (remindRecord instanceof RemindDeliveryProductBO) {
            title = MessageTemplateConstant.DELIVERY_PRODUCT_REMIND_TITLE;
            content = getDeliveryProductMessageContent(context);
        } else if (remindRecord instanceof RemindWorkOrderBO) {
            title = MessageTemplateConstant.WORK_ORDER_REMIND_TITLE;
            content = getWorkOrderMessageContent(context, RemindMethodEnum.FEISHU);
        } else if (remindRecord instanceof RemindAfterSaleBO) {
            title = RemindPlanWorkOrderTypeEnum.RETRIEVE.eq(remindSubPlan.getWorkOrderType()) ?
                    MessageTemplateConstant.AFTER_SALE_RETRIEVE_REMIND_TITLE : MessageTemplateConstant.AFTER_SALE_DISPUTE_REMIND_TITLE;
            content = getAfterSaleMessageContent(context, remindSubPlan, RemindMethodEnum.FEISHU);
        } else if (remindRecord instanceof RemindComplaintBO) {
            title = MessageTemplateConstant.COMPLAINT_REMIND_TITLE;
            content = getComplaintMessageContent(context, remindSubPlan, RemindMethodEnum.FEISHU);
        } else {
            // 添加默认处理分支，防止未来类型扩展导致逻辑静默失败
            throw new IllegalArgumentException("Unsupported remindRecord type: " + remindRecord.getClass().getName());
        }
        if (StringUtils.isBlank(content)) {
            log.error("Fail to get message content. planRecordId:{}", remindPlanMessage.getPlanRecordId());
            return;
        }
        //发送飞群组消息
        sengFeishuGroupMessage(title, content, remindObjectBO.getWebhook(), remindPlanMessage.getPlanRecordId());
    }

}
