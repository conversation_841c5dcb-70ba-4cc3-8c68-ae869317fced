package com.pxb7.mall.workorder.domain.model;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Getter
@Setter
@Accessors(chain = true)
@ToString
public class RemindPlanGameConfigBO {

    private Long id;

    private Long remindPlanId;

    private Long remindSubPlanId;

    /**
     *  游戏ID
     */
    private String  gameId;

    /**
     * 游戏名称
     */
    private String gameName;

    /**
     *  游戏厂商：1 网易系  2 腾讯系  3 米哈游  4其他
     */
    private Integer maker;

    /**
     * 投诉渠道，1:im,2:支付宝,3:咸鱼,4:12315,5:消费宝,6:连连支付
     */
    private Integer channel;

    /**
     *  游戏完成时间,{"hours":"10","minutes":"30"}
     */
    private TimeConfigBO expectCompleteTimeConfig;

    /**
     *  im客服端倒计时,{"hours":"10","minutes":"30"}
     */
    private TimeConfigBO imCountDownTimeConfig;

    private String createUserId;


    private String updateUserId;

    private LocalDateTime updateTime;
}
