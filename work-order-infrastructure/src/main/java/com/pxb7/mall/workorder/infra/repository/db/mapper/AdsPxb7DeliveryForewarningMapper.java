package com.pxb7.mall.workorder.infra.repository.db.mapper;




import com.pxb7.mall.workorder.infra.model.DeliveryStatictisSearchPO;
import com.pxb7.mall.workorder.infra.model.DeliveryStatictisDataPO;
import com.pxb7.mall.workorder.infra.repository.db.entity.AdsPxb7DeliveryForewarning;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.Pageable;
import java.util.List;

/**
 * 账号交付预警统计(AdsPxb7DeliveryForewarning)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-04-17 22:35:06
 */
@Mapper
public interface AdsPxb7DeliveryForewarningMapper{

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    AdsPxb7DeliveryForewarning queryById(Long id);

    /**
     * 查询指定行数据
     *
     * @param adsPxb7DeliveryForewarning 查询条件
     * @param pageable         分页对象
     * @return 对象列表
     */
    List<AdsPxb7DeliveryForewarning> queryAllByLimit(AdsPxb7DeliveryForewarning adsPxb7DeliveryForewarning, @Param("pageable") Pageable pageable);

    /**
     * 统计总行数
     *
     * @param adsPxb7DeliveryForewarning 查询条件
     * @return 总行数
     */
    long count(AdsPxb7DeliveryForewarning adsPxb7DeliveryForewarning);

    /**
     * 新增数据
     *
     * @param adsPxb7DeliveryForewarning 实例对象
     * @return 影响行数
     */
    int insert(AdsPxb7DeliveryForewarning adsPxb7DeliveryForewarning);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<AdsPxb7DeliveryForewarning> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<AdsPxb7DeliveryForewarning> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<AdsPxb7DeliveryForewarning> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<AdsPxb7DeliveryForewarning> entities);

    /**
     * 修改数据
     *
     * @param adsPxb7DeliveryForewarning 实例对象
     * @return 影响行数
     */
    int update(AdsPxb7DeliveryForewarning adsPxb7DeliveryForewarning);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Long id);


    List<DeliveryStatictisDataPO> countBySearchParam(@Param("param") DeliveryStatictisSearchPO param);
}

