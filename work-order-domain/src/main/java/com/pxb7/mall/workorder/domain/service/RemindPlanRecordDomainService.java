package com.pxb7.mall.workorder.domain.service;


import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.cola.exception.Assert;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pxb7.mall.components.env.profile.PxProfileHelper;
import com.pxb7.mall.workorder.client.enums.BizErrorCodeEnum;
import com.pxb7.mall.workorder.client.enums.PlanExecuteStatusEnum;
import com.pxb7.mall.workorder.client.message.RemindPlanMessage;
import com.pxb7.mall.workorder.domain.mapping.RemindPlanRecordDomainMapping;
import com.pxb7.mall.workorder.domain.model.NotDisturbPeriodBO;
import com.pxb7.mall.workorder.domain.model.RemindPlanRecordBO;
import com.pxb7.mall.workorder.domain.model.RemindPlanRecordReqBO;
import com.pxb7.mall.workorder.domain.model.RemindPlanRecordRespBO;
import com.pxb7.mall.workorder.domain.model.RemindPlanRuleBO;
import com.pxb7.mall.workorder.domain.model.TimeConfigBO;
import com.pxb7.mall.workorder.infra.constant.CommonConstants;
import com.pxb7.mall.workorder.infra.model.RemindPlanRecordReqPO;
import com.pxb7.mall.workorder.infra.repository.db.RemindPlanRecordRepository;
import com.pxb7.mall.workorder.infra.repository.db.entity.RemindPlanRecord;
import com.pxb7.mall.workorder.infra.util.IdGenUtil;
import com.pxb7.mall.workorder.infra.util.TimeUtils;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;


/**
 * 预警执行计划记录domain服务
 *
 * <AUTHOR>
 * @since 2025-04-12 14:13:43
 */
@Slf4j
@Service
public class RemindPlanRecordDomainService {

    @Resource
    private RemindPlanRuleDomainService ruleDomainService;

    @Resource
    private RemindPlanDomainService remindPlanDomainService;

    @Resource
    private RemindPlanRecordRepository planRecordRepository;

    /**
     * 根据提醒id和预警计划记录id查询预警执行计划记录
     * @param remindId
     * @param planRecordId
     * @return
     */
    public RemindPlanRecordRespBO.DetailBO findByRemindIdWithPlanRecordId(String remindId, String planRecordId) {
        RemindPlanRecord remindPlanRecord = planRecordRepository.lambdaQuery()
                .eq(RemindPlanRecord::getRemindId, remindId)
                .eq(RemindPlanRecord::getPlanRecordId, planRecordId)
                .one();
        return RemindPlanRecordDomainMapping.INSTANCE.remindPlanRecordPO2DetailBO(remindPlanRecord);
    }

    /**
     * 生成预警执行计划记录
     * @param remindPlanRecordBO
     * @return
     */
    public List<RemindPlanRecordReqBO.AddBO> generatePlanRecords(RemindPlanRecordBO remindPlanRecordBO) {
        List<RemindPlanRuleBO> remindPlanRuleList = ruleDomainService.getRemindPlanRuleList(remindPlanRecordBO.getRemindPlanId());
        if (CollectionUtil.isEmpty(remindPlanRuleList)) {
            log.warn("对应的预警计划没有配置预警规则, remindPlanId:{}", remindPlanRecordBO.getRemindPlanId());
            return Collections.emptyList();
        }
        //按节点升序排序
        remindPlanRuleList.sort(Comparator.comparingInt(RemindPlanRuleBO::getNodeNumber));
        //勿扰时段
        NotDisturbPeriodBO notDisturbPeriod = remindPlanDomainService.getNotDisturbPeriod(remindPlanRecordBO.getRemindPlanId());

        List<RemindPlanRecordReqBO.AddBO> planRecordList = new ArrayList<>();
        for (int i = 0; i < remindPlanRuleList.size(); i++) {
            RemindPlanRuleBO remindPlanRuleBO = remindPlanRuleList.get(i);
            //判断是否是超时前最后一个节点
            Boolean lastBeforeTimeout = ruleDomainService.isLastBeforeTimeout(i, remindPlanRuleList);
            //计算预警时间
            LocalDateTime remindTime = getRemindTime(remindPlanRecordBO.getExpectCompleteTime(), remindPlanRuleBO, notDisturbPeriod);
            //如果当前执行计划的时间和上一个的执行计划时间相同，则将上一个的执行计划状态设置为失效
            if (!planRecordList.isEmpty()
                    && planRecordList.get(planRecordList.size() - 1).getRemindTime().isEqual(remindTime)) {
                planRecordList.get(planRecordList.size() - 1)
                        .setStatus(PlanExecuteStatusEnum.INVALID_NOT_DISTURB.getCode());
            }
            //生成预警执行计划记录
            RemindPlanRecordReqBO.AddBO planRecord =
                    new RemindPlanRecordReqBO.AddBO()
                            .setPlanRecordId(remindPlanRecordBO.getRecordIdPrefix() +
                                    IdGenUtil.getShardingColumnId(remindPlanRecordBO.getBizId()))
                            .setBizType(remindPlanRecordBO.getBizType())
                            .setBizId(remindPlanRecordBO.getBizId())
                            .setWhichTime(i + 1)
                            .setLastBeforeTimeout(lastBeforeTimeout)
                            .setRemindTime(remindTime)
                            .setRemindId(remindPlanRecordBO.getRemindId())
                            .setPlanRuleId(remindPlanRuleBO.getId())
                            .setRemindPlanId(remindPlanRecordBO.getRemindPlanId())
                            .setRemindSubPlanId(remindPlanRecordBO.getRemindSubPlanId())
                            .setStatus(PlanExecuteStatusEnum.WILL_EXECUTE.getCode())
                            .setCreateUserId(CommonConstants.SYS_DEFAULT_USER)
                            .setUpdateUserId(CommonConstants.SYS_DEFAULT_USER)
                            .setEnvProfile(PxProfileHelper.getProfile().getProfileValue());
            planRecordList.add(planRecord);
        }

        List<RemindPlanRecordReqPO.AddPO> addPOList =
                RemindPlanRecordDomainMapping.INSTANCE.remindPlanRecordBO2AddPOList(planRecordList);
        boolean result = planRecordRepository.insertBatch(addPOList);
        Assert.isTrue(result,
                BizErrorCodeEnum.REMIND_PLAN_RECORD_SAVE_ERROR.getErrCode(), BizErrorCodeEnum.REMIND_PLAN_RECORD_SAVE_ERROR.getErrDesc());
        return planRecordList;
    }

    /**
     *  计算预警时间
     * @param expectCompleteTime
     * @param remindPlanRuleBO
     * @param notDisturbPeriod
     * @return
     */
    private LocalDateTime getRemindTime(
            LocalDateTime expectCompleteTime, RemindPlanRuleBO remindPlanRuleBO, NotDisturbPeriodBO notDisturbPeriod) {
        TimeConfigBO remindTimeConfig = remindPlanRuleBO.getRemindTimeConfig();
        //是否是超时前节点
        boolean isBeforeTimeout = remindPlanRuleBO.getNodeNumber().toString().startsWith("1");
        LocalDateTime remindTime;
        if (isBeforeTimeout) {
            remindTime = TimeUtils.subtractSpecificTime(
                    expectCompleteTime, remindTimeConfig.getHours(), remindTimeConfig.getMinutes(), 0);
        } else {
            remindTime = TimeUtils.addSpecificTime(
                    expectCompleteTime, remindTimeConfig.getHours(), remindTimeConfig.getMinutes(), 0);
        }
        //判断提醒时间是否在防打扰时间段内，如果是，则设置为防打扰的结束时间
        return remindPlanDomainService.adjustRemindTimeIfInNotDisturbPeriod(remindTime, notDisturbPeriod);
    }

    /**
     * 作废预警执行计划
     * @param bizType
     * @param bizId
     * @return
     */
    public boolean invalidPlanRecord(Integer bizType, String bizId) {
        RemindPlanRecordReqBO.UpdateBO updatePlanRecordBO = new RemindPlanRecordReqBO.UpdateBO();
        updatePlanRecordBO.setBizType(bizType);
        updatePlanRecordBO.setBizId(bizId);
        updatePlanRecordBO.setStatus(PlanExecuteStatusEnum.INVALID.getCode());
        return updateStatusByBizIdAndType(updatePlanRecordBO);
    }

    /**
     * 更新预警执行计划状态
     * @param updateBO
     * @return
     */
    public boolean updateStatusByBizIdAndType(RemindPlanRecordReqBO.UpdateBO updateBO) {
        RemindPlanRecordReqPO.UpdatePO updatePO =
                RemindPlanRecordDomainMapping.INSTANCE.remindPlanRecordBO2UpdatePO(updateBO);
        return planRecordRepository.updateStatusByBizIdAndType(updatePO);
    }

    /**
     * 分页查询
     * @param param
     * @return
     */
    public Page<RemindPlanRecordRespBO.DetailBO> page(RemindPlanRecordReqBO.PageBO param) {
        RemindPlanRecordReqPO.PagePO pagePO =
                RemindPlanRecordDomainMapping.INSTANCE.remindPlanRecordBO2PagePO(param);
        Page<RemindPlanRecord> page = planRecordRepository.page(pagePO);
        return RemindPlanRecordDomainMapping.INSTANCE.remindPlanRecordPO2PageBO(page);
    }


    /**
     * 批量更新预警执行计划
     * @param planRecordIds
     * @param status
     * @return
     */
    public boolean batchUpdateStatusByIds(List<Long> planRecordIds, PlanExecuteStatusEnum status) {
        if (CollectionUtils.isEmpty(planRecordIds)) {
            return Boolean.FALSE;
        }
        return planRecordRepository.batchUpdateStatusByIds(planRecordIds, status.getCode());
    }

    /**
     * 批量更新账号交付预警执行计划状态
     * @param remindIds
     * @param status
     * @return
     */
    public boolean batchUpdateStatusByRemindIds(List<String> remindIds, PlanExecuteStatusEnum status,Long addSeconds) {
        if (CollectionUtils.isEmpty(remindIds)) {
            return Boolean.FALSE;
        }
        return planRecordRepository.batchUpdateStatusByRemindIds(remindIds, status.getCode(),addSeconds);
    }


    /**
     * 更新预警执行计划状态
     * @param remindPlanMessage
     * @param executeStatus
     * @return
     */
    public boolean updatePlanRecordStatus(RemindPlanMessage remindPlanMessage, PlanExecuteStatusEnum executeStatus) {
        RemindPlanRecordReqBO.UpdateBO updatePlanRecordBO = new RemindPlanRecordReqBO.UpdateBO();
        updatePlanRecordBO.setBizType(remindPlanMessage.getBizType());
        updatePlanRecordBO.setBizId(remindPlanMessage.getBizId());
        updatePlanRecordBO.setPlanRecordId(remindPlanMessage.getPlanRecordId());
        updatePlanRecordBO.setStatus(executeStatus.getCode());
        return updateStatusByBizIdAndType(updatePlanRecordBO);
    }

    /**
     * 根据业务id删除记录
     * @param bizId
     * @return
     */
    public boolean deleteByBizId(String bizId) {
        return planRecordRepository.deleteByBizId(bizId);
    }

}

