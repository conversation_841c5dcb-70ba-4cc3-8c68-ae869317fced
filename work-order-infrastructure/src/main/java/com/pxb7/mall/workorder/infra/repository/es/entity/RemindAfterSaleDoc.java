package com.pxb7.mall.workorder.infra.repository.es.entity;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;
import lombok.experimental.FieldNameConstants;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 售后工单预警记录(RemindAfterSale)实体类
 *
 * <AUTHOR>
 * @since 2025-04-24 23:31:56
 */
@Getter
@Setter
@Accessors(chain = true)
@ToString
@FieldNameConstants
@Document(indexName = "remind_after_sale")
public class RemindAfterSaleDoc implements Serializable {

    private static final long serialVersionUID = -78808605079325933L;

    /**
     * 预警记录id
     */
    @Id
    @Field(value = "remind_id", type = FieldType.Keyword)
    private String remindId;
    /**
     * 工单id
     */
    @Field(value = "work_order_id", type = FieldType.Keyword)
    private String workOrderId;
    /**
     * 工单编号
     */
    @Field(value = "work_order_no", type = FieldType.Keyword)
    private String workOrderNo;
    /**
     * 商品id
     */
    @Field(value = "product_id", type = FieldType.Keyword)
    private String productId;
    /**
     * 商品编码
     */
    @Field(value = "product_code", type = FieldType.Keyword)
    private String productCode;
    /**
     * 游戏id
     */
    @Field(value = "game_id", type = FieldType.Keyword)
    private String gameId;
    /**
     * 群组id
     */
    @Field(value = "group_id", type = FieldType.Keyword)
    private String groupId;
    /**
     * 订单id
     */
    @Field(value = "order_id", type = FieldType.Keyword)
    private String orderId;
    /**
     * 子订单id
     */
    @Field(value = "order_item_id", type = FieldType.Keyword)
    private String orderItemId;
    /**
     * 完结状态,1:未完结,2:已完结,3:终止
     */
    @Field(value = "complete_status", type = FieldType.Keyword)
    private Integer completeStatus;
    /**
     * 超时状态，0:无,1:即将超时,2:已超时
     */
    @Field(value = "time_out_status", type = FieldType.Keyword)
    private Integer timeOutStatus;
    /**
     * 工单类型  1:找回 2:纠纷
     */
    @Field(value = "work_order_type", type = FieldType.Keyword)
    private Integer workOrderType;
    /**
     * 找回处理人员
     */
    @Field(value = "retrieve_user_id", type = FieldType.Keyword)
    private String retrieveUserId;
    /**
     * 纠纷处理人员
     */
    @Field(value = "dispute_user_id", type = FieldType.Keyword)
    private String disputeUserId;
    /**
     * 预期完结时间
     */
    @Field(type = FieldType.Date,format = DateFormat.date_hour_minute_second_fraction,value = "expect_complete_time")
    private LocalDateTime expectCompleteTime;
    /**
     * im客服端倒计时开始时间
     */
    @Field(type = FieldType.Date,format = DateFormat.date_hour_minute_second_fraction,value = "im_count_down_time")
    private LocalDateTime imCountDownTime;
    /**
     * 完结时间
     */
    @Field(type = FieldType.Date,format = DateFormat.date_hour_minute_second_fraction,value = "complete_time")
    private LocalDateTime completeTime;
    /**
     * 预警计划游戏配置id,remind_plan_game_config
     */
    @Field(value = "game_config_id", type = FieldType.Keyword)
    private Long gameConfigId;
    /**
     * 预警计划id,remind_plan
     */
    @Field(value = "remind_plan_id", type = FieldType.Keyword)
    private Long remindPlanId;
    /**
     * 预警子计划id,remind_sub_plan
     */
    @Field(value = "remind_sub_plan_id", type = FieldType.Keyword)
    private Long remindSubPlanId;
    /**
     * 创建时间
     */
    @Field(type = FieldType.Date,format = DateFormat.date_hour_minute_second_fraction,value = "create_time")
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @Field(type = FieldType.Date,format = DateFormat.date_hour_minute_second_fraction,value = "update_time")
    private LocalDateTime updateTime;
    /**
     * 创建人
     */
    @Field(value = "create_user_id", type = FieldType.Keyword)
    private String createUserId;
    /**
     * 更新人
     */
    @Field(value = "update_user_id", type = FieldType.Keyword)
    private String updateUserId;


}

