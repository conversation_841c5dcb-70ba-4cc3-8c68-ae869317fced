package com.pxb7.mall.workorder.app.model;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@ToString
public class TimeConfigDTO implements Comparable<TimeConfigDTO>{

    /**
     * 小时数
     */
    @NotNull(message = "小时数不能为空")
    @Min(value = 0, message = "小时数不能小于0")
    @Max(value = 999, message = "小时数不能大于999")
    private Integer hours;

    /**
     * 分钟数
     */
    @NotNull(message = "分钟数不能为空")
    @Min(value = 0, message = "分钟数不能小于0")
    @Max(value = 59, message = "分钟数不能大于59")
    private Integer minutes;

    @Override
    public int compareTo(@NotNull TimeConfigDTO other) {
        boolean hoursGreater = this.getHours() > other.getHours();
        boolean hoursEquals = this.getHours().equals(other.getHours());
        boolean minutesGreater = this.getMinutes() > other.getMinutes();
        boolean minuEquals = this.getMinutes().equals(other.getMinutes());
        if (hoursGreater || (hoursEquals && minutesGreater)) {
            return 1;
        } else if (hoursEquals && minuEquals) {
            return 0;
        } else {
            return -1;
        }

    }

    public int isSet() {
        return this.hours > 0 ? 1 : this.minutes > 0 ? 1 : 0;
    }
}
