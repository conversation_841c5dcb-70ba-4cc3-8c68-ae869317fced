package com.pxb7.mall.workorder.infra.repository.gateway.dubbo.im;

import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.fastjson2.JSON;
import com.pxb7.mall.im.client.api.SendCommonMsgServiceI;
import com.pxb7.mall.im.client.api.SendMsgServiceI;
import com.pxb7.mall.im.client.dto.request.SendReminderMsgReqDTO;
import com.pxb7.mall.im.client.dto.request.card.CommandMsgReqDTO;
import com.pxb7.mall.im.client.dto.request.card.SendRichTextMsgReqDTO;
import com.pxb7.mall.im.client.dto.request.card.SendTextMsgReqDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Repository;

@Repository
@Slf4j
public class ImMsgGateway {

    @DubboReference
    private SendMsgServiceI sendMsgServiceI;
    @DubboReference
    private SendCommonMsgServiceI sendCommonMsgService;


    public boolean sendRichTextMsg(SendRichTextMsgReqDTO msgReqDTO) {
        try {
            log.info("send rich text message to IM. msgReqDTO:{}", JSON.toJSONString(msgReqDTO));
            SingleResponse<String> response = sendCommonMsgService.sendRichTextMsg(msgReqDTO);
            return response.isSuccess();
        } catch (Throwable e) {
            log.error("An exception occurred while calling SendCommonMsgServiceI#sendRichTextMsg,req:{}", msgReqDTO);
            return false;
        }
    }

    public void sendTextMsg(SendTextMsgReqDTO msgReqDTO) {
        try {
            log.info("send text message to IM. msgReqDTO:{}", JSON.toJSONString(msgReqDTO));
            sendMsgServiceI.sendTextMsg(msgReqDTO);
        } catch (Throwable e) {
            log.error("An exception occurred while calling sendMsgServiceI#sendTextMsg,req:{}", msgReqDTO, e);
        }
    }

    /**
     * 发送催一催消息
     * @param msgReqDTO
     */
    public void sendRemindMsg(SendReminderMsgReqDTO msgReqDTO) {
        try {
            log.info("send remind message to IM. msgReqDTO:{}", JSON.toJSONString(msgReqDTO));
            sendMsgServiceI.sendRemindMsg(msgReqDTO);
        } catch (Throwable e) {
            log.error("An exception occurred while calling sendMsgServiceI#sendRemindMsg,req:{}", msgReqDTO, e);
        }
    }

    /**
     * 发送命令消息
     * @param msgReqDTO
     */
    public void sendCmdMsg(CommandMsgReqDTO msgReqDTO) {
        try {
            log.info("send cmd msg to IM. msgReqDTO:{}", JSON.toJSONString(msgReqDTO));
            sendMsgServiceI.sendCmdMsg(msgReqDTO);
        } catch (Throwable e) {
            log.error("An exception occurred while calling sendMsgServiceI#sendCmdMsg,req:{}", msgReqDTO, e);
        }
    }

}
