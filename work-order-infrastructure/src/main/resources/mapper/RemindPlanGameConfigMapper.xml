<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pxb7.mall.workorder.infra.repository.db.mapper.RemindPlanGameConfigMapper">
    <resultMap id="BaseResultMap" type="com.pxb7.mall.workorder.infra.repository.db.entity.RemindPlanGameConfig">
        <result column="id" jdbcType="BIGINT" property="id"/>
        <result column="game_id" jdbcType="VARCHAR" property="gameId"/>
        <result column="game_name" jdbcType="VARCHAR" property="gameName"/>
        <result column="maker" jdbcType="INTEGER" property="maker"/>
        <result column="expect_complete_time_config" jdbcType="VARCHAR" property="expectCompleteTimeConfig"/>
        <result column="im_count_down_time_config" jdbcType="VARCHAR" property="imCountDownTimeConfig"/>
        <result column="remind_plan_id" jdbcType="BIGINT" property="remindPlanId"/>
        <result column="remind_sub_plan_id" jdbcType="BIGINT" property="remindSubPlanId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_user_id" jdbcType="VARCHAR" property="createUserId"/>
        <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId"/>
        <result column="is_deleted" jdbcType="BOOLEAN" property="deleted"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into
        remind_plan_game_config(game_id,game_name,maker,expect_complete_time_config,im_count_down_time_config,remind_plan_id,remind_sub_plan_id,create_user_id,update_user_id)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.gameId},#{entity.gameName},#{entity.maker},#{entity.expectCompleteTimeConfig},#{entity.imCountDownTimeConfig},#{entity.remindPlanId},#{entity.remindSubPlanId},#{entity.createUserId},#{entity.updateUserId})
        </foreach>
    </insert>

    <select id="selectByGameIdAndBusinessType" resultMap="BaseResultMap">
        select *
        from remind_plan_game_config config join remind_sub_plan sub_plan on config.remind_sub_plan_id = sub_plan.id
        where config.game_id = #{gameId}
        and sub_plan.business_type = #{businessType}
        and config.is_deleted = 0
        and sub_plan.is_deleted = 0
        limit 1
    </select>

    <select id="selectByGameIdAndWorkOrderStatus" resultMap="BaseResultMap">
        select *
        from remind_plan_game_config config join remind_sub_plan sub_plan on config.remind_sub_plan_id = sub_plan.id
        where config.game_id = #{gameId}
          and sub_plan.work_order_status = #{workOrderStatus}
            <if test="onShelfType != null">
                and sub_plan.on_shelf_type = #{onShelfType}
            </if>
          and sub_plan.membership = #{membership}
          and config.is_deleted = 0
          and sub_plan.is_deleted = 0
        limit 1
    </select>

    <select id="selectByGameIdAndWorkOrderType" resultMap="BaseResultMap">
        select *
        from remind_plan_game_config config join remind_sub_plan sub_plan on config.remind_sub_plan_id = sub_plan.id
        where config.game_id = #{gameId}
          and sub_plan.work_order_type = #{workOrderType}
            <if test="membership != null">
                and sub_plan.membership = #{membership}
            </if>
          and config.is_deleted = 0
          and sub_plan.is_deleted = 0
        limit 1
    </select>

    <select id="selectByComplaintLevelAndChannel" resultMap="BaseResultMap">
        select *
        from remind_plan_game_config config join remind_sub_plan sub_plan on config.remind_sub_plan_id = sub_plan.id
        where config.channel = #{complaintChannel}
          and sub_plan.complaint_level = #{complaintLevel}
          and config.is_deleted = 0
          and sub_plan.is_deleted = 0
        limit 1
    </select>

</mapper>

