package com.pxb7.mall.workorder.adapter.web;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pxb7.mall.response.PxPageResponse;
import com.pxb7.mall.response.PxResponse;
import com.pxb7.mall.workorder.app.model.ComplaintRemindPlanReqDTO;
import com.pxb7.mall.workorder.app.model.ComplaintRemindPlanRespDTO;
import com.pxb7.mall.workorder.app.model.RemindPlanPageDetailDTO;
import com.pxb7.mall.workorder.app.service.ComplaintRemindPlanAppService;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 管理后台-投诉预警计划
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/web/remind/plan/complaint")
public class ComplaintRemindPlanController {

    @Resource
    private ComplaintRemindPlanAppService complaintRemindPlanAppService;

    /**
     * 新增
     */
    @PostMapping("/insert")
    public PxResponse<Long> insert(@RequestBody @Validated ComplaintRemindPlanReqDTO.AddDTO param) {
        return PxResponse.ok(complaintRemindPlanAppService.insert(param));
    }

    /**
     * 修改
     */
    @PostMapping("/update")
    public PxResponse<Boolean> update(@RequestBody @Validated ComplaintRemindPlanReqDTO.UpdateDTO param) {
        return PxResponse.ok(complaintRemindPlanAppService.update(param));
    }

    /**
     * 详情
     */
    @RequestMapping("/detail")
    public PxResponse<ComplaintRemindPlanRespDTO.DetailDTO> detail(@RequestParam(value = "remindPlanId") String remindPlanId) {
        return PxResponse.ok(complaintRemindPlanAppService.findById(Long.valueOf(remindPlanId)));
    }

    /**
     * 删除
     */
    @PostMapping("/del")
    public PxResponse<Boolean> del(@RequestBody @Validated ComplaintRemindPlanReqDTO.DelDTO param) {
        return PxResponse.ok(complaintRemindPlanAppService.deleteById(param));
    }

    /**
     * 分页
     */
    @PostMapping("/page")
    public PxPageResponse<RemindPlanPageDetailDTO>
        page(@RequestBody @Validated ComplaintRemindPlanReqDTO.PageDTO param) {
        Page<RemindPlanPageDetailDTO> page = complaintRemindPlanAppService.page(param);
        return PxPageResponse.of(page.getRecords(), (int)page.getTotal(), (int)page.getSize(), (int)page.getCurrent());
    }

}
