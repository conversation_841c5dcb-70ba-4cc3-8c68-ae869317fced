package com.pxb7.mall.workorder.domain.service;


import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollectionUtil;
import com.pxb7.mall.workorder.domain.model.RemindPlanRuleRefreshBO;
import com.pxb7.mall.workorder.domain.model.RemindPlanRuleReqBO;
import com.pxb7.mall.workorder.domain.model.RemindPlanRuleRespBO;
import com.pxb7.mall.workorder.infra.model.RemindPlanRuleReqPO;
import com.pxb7.mall.workorder.infra.repository.db.RemindPlanRuleRepository;
import com.pxb7.mall.workorder.infra.repository.db.entity.RemindPlanRule;
import com.pxb7.mall.workorder.domain.model.RemindPlanRuleBO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import com.pxb7.mall.workorder.domain.mapping.RemindPlanRuleDomainMapping;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.transaction.annotation.Transactional;

/**
 * 预警计划提醒规则配置domain服务
 *
 * <AUTHOR>
 * @since 2025-03-24 21:11:14
 */
@Service
public class RemindPlanRuleDomainService {

    @Resource
    private RemindPlanRuleRepository remindPlanRuleRepository;

    public boolean insert(RemindPlanRuleBO param) {
        RemindPlanRuleReqPO.AddPO addPO = RemindPlanRuleDomainMapping.INSTANCE.remindPlanRuleBO2AddPO(param);

        return remindPlanRuleRepository.insert(addPO);
    }

    public boolean saveBatch(List<RemindPlanRuleBO> params) {
        List<RemindPlanRuleReqPO.AddPO> addPOList = new ArrayList<>();
        for (RemindPlanRuleBO param : params) {
            RemindPlanRuleReqPO.AddPO addPO = RemindPlanRuleDomainMapping.INSTANCE.remindPlanRuleBO2AddPO(param);
            addPOList.add(addPO);
        }
        return remindPlanRuleRepository.saveBatch(addPOList);
    }

    @Transactional(rollbackFor = Throwable.class)
    public boolean refresh(RemindPlanRuleRefreshBO param) {
        // todo 要处理每次执行返回的值！！！
        // 新增
        List<RemindPlanRuleBO> needAddPlanRules = param.getNeedAddPlanRules();
        if (CollectionUtil.isNotEmpty(needAddPlanRules)){
            this.saveBatch(needAddPlanRules);
        }
        // 更新
        List<RemindPlanRuleBO> needUpdatePlanRules = param.getNeedUpdatePlanRules();
        if (CollectionUtil.isNotEmpty(needUpdatePlanRules)){
            for (RemindPlanRuleBO needUpdatePlanRule : needUpdatePlanRules) {
                this.update(needUpdatePlanRule);
            }
        }
        // 删除
        List<RemindPlanRuleBO> needDeletePlanRules = param.getNeedDeletePlanRules();
        if (CollectionUtil.isNotEmpty(needDeletePlanRules)){
            List<Long> planRuleIds =
                needDeletePlanRules.stream().map(RemindPlanRuleBO::getId).collect(Collectors.toList());
            boolean b = remindPlanRuleRepository.deleteBatchByIds(planRuleIds);
        }
        return true;
    }

    public boolean update(RemindPlanRuleBO param) {
        RemindPlanRuleReqPO.UpdatePO updatePO = RemindPlanRuleDomainMapping.INSTANCE.remindPlanRuleBO2UpdatePO(param);
        return remindPlanRuleRepository.update(updatePO);
    }

    public boolean deleteById(RemindPlanRuleReqBO.DelBO param) {
        RemindPlanRuleReqPO.DelPO delPO = RemindPlanRuleDomainMapping.INSTANCE.remindPlanRuleBO2DelPO(param);
        return remindPlanRuleRepository.deleteById(delPO);
    }

    public boolean deleteByIds(List<Long> ids) {
        return remindPlanRuleRepository.removeBatchByIds(ids);
    }

    public RemindPlanRuleRespBO.DetailBO findById(Long id) {
        RemindPlanRule entity = remindPlanRuleRepository.findById(id);
        return RemindPlanRuleDomainMapping.INSTANCE.remindPlanRulePO2DetailBO(entity);

    }

    public List<RemindPlanRuleBO> list(RemindPlanRuleReqBO.SearchBO param) {
        RemindPlanRuleReqPO.SearchPO searchPO = RemindPlanRuleDomainMapping.INSTANCE.remindPlanRuleBO2SearchPO(param);
        List<RemindPlanRule> list = remindPlanRuleRepository.list(searchPO);
        return RemindPlanRuleDomainMapping.INSTANCE.remindPlanRulePO2ListBO(list);
    }

    public Page<RemindPlanRuleRespBO.DetailBO> page(RemindPlanRuleReqBO.PageBO param) {
        RemindPlanRuleReqPO.PagePO pagePO = RemindPlanRuleDomainMapping.INSTANCE.remindPlanRuleBO2PagePO(param);
        Page<RemindPlanRule> page = remindPlanRuleRepository.page(pagePO);
        return RemindPlanRuleDomainMapping.INSTANCE.remindPlanRulePO2PageBO(page);
    }

    public List<RemindPlanRuleBO> getRemindPlanRuleList(Long remindPlanId) {
        if (null == remindPlanId || remindPlanId < 1) {
            return Collections.emptyList();
        }
        RemindPlanRuleReqPO.SearchPO searchPO = new RemindPlanRuleReqPO.SearchPO();
        searchPO.setRemindPlanId(remindPlanId);
        List<RemindPlanRule> list = remindPlanRuleRepository.list(searchPO);
        return RemindPlanRuleDomainMapping.INSTANCE.remindPlanRulePO2ListBO(list);
    }

    /**
     * 判断是否是超时前最后一个节点
     * @param i
     * @param remindPlanRuleList
     * @return
     */
    public Boolean isLastBeforeTimeout(int i, List<RemindPlanRuleBO> remindPlanRuleList) {
        RemindPlanRuleBO remindPlanRuleBO = remindPlanRuleList.get(i);
        Boolean lastBeforeTimeout = Boolean.FALSE;
        //找出nodeNumber为1开头且下一个节点不存在或下一个节点为2的规则
        if (remindPlanRuleBO.getNodeNumber().toString().startsWith("1")) {
            //判断下一个节点是否存在
            if (i + 1 < remindPlanRuleList.size()) {
                RemindPlanRuleBO nextRemindPlanRuleBO = remindPlanRuleList.get(i + 1);
                if (nextRemindPlanRuleBO.getNodeNumber().toString().startsWith("2")) {
                    lastBeforeTimeout = Boolean.TRUE;
                }
            } else {
                lastBeforeTimeout = Boolean.TRUE;
            }
        }
        return lastBeforeTimeout;
    }
}

