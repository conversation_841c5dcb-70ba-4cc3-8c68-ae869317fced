package com.pxb7.mall.workorder.infra.repository.db.mapper;

import com.pxb7.mall.workorder.infra.model.ComplaintStatisticDataPO;
import com.pxb7.mall.workorder.infra.model.ComplaintStatisticSearchPO;
import com.pxb7.mall.workorder.infra.repository.db.entity.AdsPxb7ComplaintForewarning;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 客诉预警统计(AdsPxb7ComplaintForewarning)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-05-08 18:15:03
 */
@Mapper
public interface AdsPxb7ComplaintForewarningMapper extends BaseMapper<AdsPxb7ComplaintForewarning> {
    /**
     * 批量新增数据
     *
     * @param entities List<AdsPxb7ComplaintForewarning> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<AdsPxb7ComplaintForewarning> entities);

    List<ComplaintStatisticDataPO> countBySearchParam(@Param("param") ComplaintStatisticSearchPO param);

    LocalDateTime getLastDataInsertTime();
}
