<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pxb7.mall.workorder.infra.repository.db.mapper.AdsPxb7ComplaintForewarningMapper">
    <resultMap id="BaseResultMap" type="com.pxb7.mall.workorder.infra.repository.db.entity.AdsPxb7ComplaintForewarning">
        <result column="id" jdbcType="BIGINT" property="id"/>
        <result column="create_date" jdbcType="VARCHAR" property="createDate"/>
        <result column="channel" jdbcType="INTEGER" property="channel"/>
        <result column="handle_user_id" jdbcType="VARCHAR" property="handleUserId"/>
        <result column="complaint_level" jdbcType="INTEGER" property="complaintLevel"/>
        <result column="complaint_work_order_cnt" jdbcType="BIGINT" property="complaintWorkOrderCnt"/>
        <result column="complaint_complete_work_order_cnt" jdbcType="BIGINT" property="complaintCompleteWorkOrderCnt"/>
        <result column="complaint_processing_work_order_cnt" jdbcType="BIGINT"
                property="complaintProcessingWorkOrderCnt"/>
        <result column="complaint_timeouting_work_order_cnt" jdbcType="BIGINT"
                property="complaintTimeoutingWorkOrderCnt"/>
        <result column="complaint_timeouted_work_order_cnt" jdbcType="BIGINT"
                property="complaintTimeoutedWorkOrderCnt"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_user_id" jdbcType="VARCHAR" property="createUserId"/>
        <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId"/>
        <result column="is_deleted" jdbcType="BOOLEAN" property="deleted"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into
        ads_pxb7_complaint_forewarning(create_date,channel,handle_user_id,complaint_level,complaint_work_order_cnt,complaint_complete_work_order_cnt,complaint_processing_work_order_cnt,complaint_timeouting_work_order_cnt,complaint_timeouted_work_order_cnt,create_time,update_time,create_user_id,update_user_id,is_deleted)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.createDate},#{entity.channel},#{entity.handleUserId},#{entity.complaintLevel},#{entity.complaintWorkOrderCnt},#{entity.complaintCompleteWorkOrderCnt},#{entity.complaintProcessingWorkOrderCnt},#{entity.complaintTimeoutingWorkOrderCnt},#{entity.complaintTimeoutedWorkOrderCnt},#{entity.createTime},#{entity.updateTime},#{entity.createUserId},#{entity.updateUserId},#{entity.deleted})
        </foreach>
    </insert>

    <select id="countBySearchParam" resultType="com.pxb7.mall.workorder.infra.model.ComplaintStatisticDataPO">
        select
        create_date,
        sum(complaint_work_order_cnt) as work_order_cnt,
        sum(complaint_complete_work_order_cnt) as complete_work_order_cnt,
        sum(complaint_processing_work_order_cnt) as processing_work_order_cnt,
        sum(complaint_timeouting_work_order_cnt) as timeouting_work_order_cnt,
        sum(complaint_timeouted_work_order_cnt) as timeouted_work_order_cnt
        from ads_pxb7_complaint_forewarning
        where is_deleted = 0
        and create_date BETWEEN #{param.startDate} AND #{param.endDate}
        <if test="param.channels !=null and !param.channels.isEmpty()">
            and channel IN
            <foreach collection="param.channels" item="channel" separator="," open="(" close=")">
                #{channel}
            </foreach>
        </if>
        <if test="param.complaintLevels !=null and !param.complaintLevels.isEmpty()">
            and complaint_level IN
            <foreach collection="param.complaintLevels" item="complaintLevel" separator="," open="(" close=")">
                #{complaintLevel}
            </foreach>
        </if>
        <if test="param.handleUserIds !=null and !param.handleUserIds.isEmpty()">
            and handle_user_id IN
            <foreach collection="param.handleUserIds" item="handleUserId" separator="," open="(" close=")">
                #{handleUserId}
            </foreach>
        </if>
        group by create_date
        ORDER BY create_date DESC
    </select>

    <select id="getLastDataInsertTime" resultType="java.time.LocalDateTime">
        select create_time from ads_pxb7_complaint_forewarning order by create_time desc limit 1;
    </select>
</mapper>

