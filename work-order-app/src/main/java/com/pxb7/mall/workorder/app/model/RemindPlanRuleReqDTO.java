package com.pxb7.mall.workorder.app.model;

import java.util.List;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Min;
import lombok.ToString;


/**
 * 预警计划提醒规则配置(RemindPlanRule)实体类
 *
 * <AUTHOR>
 * @since 2025-03-24 21:12:18
 */
public class RemindPlanRuleReqDTO {

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class AddDTO {


        @NotNull(message = "nodeNumber不能为空")
        private Integer nodeNumber;


        @NotBlank(message = "remindTimeConfig不能为空")
        private TimeConfigDTO remindTimeConfig;


        @NotBlank(message = "remindMethodConfig不能为空")
        private List<RemindMethodConfigDTO> remindMethodConfig;


        @NotNull(message = "remindPlanId不能为空")
        private Long remindPlanId;


        @NotNull(message = "remindSubPlanId不能为空")
        private Long remindSubPlanId;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class UpdateDTO {


        @NotNull(message = "id不能为空")
        private Long id;


        @NotNull(message = "nodeNumber不能为空")
        private Integer nodeNumber;


        @NotBlank(message = "remindTimeConfig不能为空")
        private TimeConfigDTO remindTimeConfig;


        @NotBlank(message = "remindMethodConfig不能为空")
        private List<RemindMethodConfigDTO> remindMethodConfig;


        @NotNull(message = "remindPlanId不能为空")
        private Long remindPlanId;


        @NotNull(message = "remindSubPlanId不能为空")
        private Long remindSubPlanId;


    }


    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class DelDTO {
        @NotNull(message = "id不能为空")
        private Long id;
    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class SearchDTO {

        @NotNull(message = "nodeNumber不能为空")
        private Integer nodeNumber;


        @NotBlank(message = "remindTimeConfig不能为空")
        private String remindTimeConfig;


        @NotBlank(message = "remindMethodConfig不能为空")
        private String remindMethodConfig;


        @NotNull(message = "remindPlanId不能为空")
        private Long remindPlanId;


        @NotNull(message = "remindSubPlanId不能为空")
        private Long remindSubPlanId;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class PageDTO {


        @NotNull(message = "nodeNumber不能为空")
        private Integer nodeNumber;


        @NotBlank(message = "remindTimeConfig不能为空")
        private String remindTimeConfig;


        @NotBlank(message = "remindMethodConfig不能为空")
        private String remindMethodConfig;


        @NotNull(message = "remindPlanId不能为空")
        private Long remindPlanId;


        @NotNull(message = "remindSubPlanId不能为空")
        private Long remindSubPlanId;


        /**
         * 页码，从1开始
         */
        @NotNull(message = "pageIndex不能为空")
        @Min(value = 1, message = "pageIndex不能小于1")
        private Integer pageIndex;
        /**
         * 每页数量
         */
        @NotNull(message = "pageSize不能为空")
        private Integer pageSize;

    }

}

