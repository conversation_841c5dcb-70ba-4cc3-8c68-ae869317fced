package com.pxb7.mall.workorder.domain.model;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Getter
@Setter
@Accessors(chain = true)
@ToString
public class RemindDeliveryProductBO extends BaseRemindRecordBO {

    /**
     * 商品id
     */
    private String productId;
    /**
     * 商品编码
     */
    private String productCode;
    /**
     * 游戏id
     */
    private String gameId;
    /**
     * 群组id
     */
    private String groupId;
    /**
     * 订单id
     */
    private String orderId;
    /**
     * 子订单id
     */
    private String orderItemId;
    /**
     * 交付客服
     */
    private String deliveryCustomerCare;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

}
