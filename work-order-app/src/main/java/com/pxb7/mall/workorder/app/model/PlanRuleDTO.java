package com.pxb7.mall.workorder.app.model;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import javax.annotation.Nullable;
import jakarta.validation.constraints.NotNull;

import java.util.List;

@Getter
@Setter
@Accessors(chain = true)
@ToString
public class PlanRuleDTO {

    /**
     * 预警游戏配置Id
     *
     */
    @Nullable
    private Long id;

    /**
     * 节点序列：
     * 11:完结时间之前第1次 12:完结时间之前第2次 13:完结时间之前第3次
     * 21:完结时间之后第1次 22:完结时间之后第2次 23:完结时间之后第3次
     */
    @NotNull(message = "请选择提醒的节点")
    private Integer nodeNumber;

    /**
     * 提醒时间点,{"hours":"10","minutes":"30"}
     */
    @Valid
    @NotNull(message = "请填写正确的提醒时间")
    private TimeConfigDTO remindTimeConfig;

    /**
     * 提醒方式配置
     */
    @Valid
    @NotEmpty(message = "请选择提醒方式")
    private List<RemindMethodConfigDTO> remindMethodConfig;

}
