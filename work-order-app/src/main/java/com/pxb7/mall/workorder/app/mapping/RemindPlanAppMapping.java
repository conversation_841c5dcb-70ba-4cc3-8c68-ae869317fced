package com.pxb7.mall.workorder.app.mapping;

import com.pxb7.mall.workorder.app.model.AssRemindPlanReqDTO;
import com.pxb7.mall.workorder.app.model.ComplaintRemindPlanReqDTO;
import com.pxb7.mall.workorder.app.model.RemindPlanPageDetailDTO;
import com.pxb7.mall.workorder.app.model.RemindPlanReqDTO;
import com.pxb7.mall.workorder.app.model.RemindPlanRespDTO;
import com.pxb7.mall.workorder.domain.model.RemindPlanBO;
import com.pxb7.mall.workorder.domain.model.RemindPlanReqBO;
import com.pxb7.mall.workorder.domain.model.RemindPlanRespBO;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;


@Mapper
public interface RemindPlanAppMapping {

    RemindPlanAppMapping INSTANCE = Mappers.getMapper(RemindPlanAppMapping.class);


    RemindPlanBO remindPlanAddDTO2BO(RemindPlanReqDTO.AddDTO source);

    RemindPlanBO remindPlanAddDTO2BO(AssRemindPlanReqDTO.AddDTO source);

    RemindPlanBO remindPlanAddDTO2BO(ComplaintRemindPlanReqDTO.AddDTO source);

    RemindPlanBO remindPlanUpdateDTO2BO(RemindPlanReqDTO.UpdateDTO source);

    RemindPlanReqBO.DelBO remindPlanDTO2DelBO(RemindPlanReqDTO.DelDTO source);

    RemindPlanReqBO.SearchBO remindPlanDTO2SearchBO(RemindPlanReqDTO.SearchDTO source);

    RemindPlanReqBO.PageBO remindPlanDTO2PageBO(RemindPlanReqDTO.PageDTO source);

    RemindPlanReqBO.PageBO remindPlanDTO2PageBO(AssRemindPlanReqDTO.PageDTO source);

    RemindPlanReqBO.PageBO remindPlanDTO2PageBO(ComplaintRemindPlanReqDTO.PageDTO source);

    RemindPlanRespDTO.DetailDTO remindPlanBO2DetailDTO(RemindPlanRespBO.DetailBO source);

    List<RemindPlanRespDTO.DetailDTO> remindPlanBO2ListDTO(List<RemindPlanRespBO.DetailBO> source);

    RemindPlanPageDetailDTO remindPlanBO2DTO(RemindPlanBO source);

    Page<RemindPlanPageDetailDTO> remindPlanBO2PageDTO(Page<RemindPlanBO> source);

}


