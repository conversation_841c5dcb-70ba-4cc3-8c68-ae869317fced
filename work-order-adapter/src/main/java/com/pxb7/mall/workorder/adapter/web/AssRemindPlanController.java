package com.pxb7.mall.workorder.adapter.web;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pxb7.mall.response.PxPageResponse;
import com.pxb7.mall.response.PxResponse;
import com.pxb7.mall.workorder.app.model.AssOptionalGameReqDTO;
import com.pxb7.mall.workorder.app.model.AssRemindPlanReqDTO;
import com.pxb7.mall.workorder.app.model.AssRemindPlanRespDTO;
import com.pxb7.mall.workorder.app.model.OptionalGameRespDTO;
import com.pxb7.mall.workorder.app.model.RemindPlanPageDetailDTO;
import com.pxb7.mall.workorder.app.service.AssRemindPlanAppService;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 管理后台-售后预警计划
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/web/remind/plan/ass")
public class AssRemindPlanController {

    @Resource
    private AssRemindPlanAppService assRemindPlanAppService;

    /**
     * 新增
     */
    @PostMapping("insert")
    public PxResponse<Long> insert(@RequestBody @Validated AssRemindPlanReqDTO.AddDTO param) {
        return PxResponse.ok(assRemindPlanAppService.insert(param));
    }

    /**
     * 修改
     */
    @PostMapping("update")
    public PxResponse<Boolean> update(@RequestBody @Validated AssRemindPlanReqDTO.UpdateDTO param) {
        return PxResponse.ok(assRemindPlanAppService.update(param));
    }

    /**
     * 详情
     */
    @RequestMapping("detail")
    public PxResponse<AssRemindPlanRespDTO.DetailDTO> detail(@RequestParam(value = "remindPlanId") String remindPlanId) {
        return PxResponse.ok(assRemindPlanAppService.findById(Long.valueOf(remindPlanId)));
    }

    /**
     * 删除
     */
    @PostMapping("del")
    public PxResponse<Boolean> del(@RequestBody @Validated AssRemindPlanReqDTO.DelDTO param) {
        return PxResponse.ok(assRemindPlanAppService.deleteById(param));
    }

    /**
     * 分页
     */
    @PostMapping("page")
    public PxPageResponse<RemindPlanPageDetailDTO> page(@RequestBody @Validated AssRemindPlanReqDTO.PageDTO param) {
        Page<RemindPlanPageDetailDTO> page = assRemindPlanAppService.page(param);
        return PxPageResponse.of(page.getRecords(), (int) page.getTotal(), (int) page.getSize(), (int) page.getCurrent());
    }

    /**
     * 获取可选游戏列表
     * @param param 参数
     * @return 游戏列表
     */
    @PostMapping("optional-game-list")
    public PxResponse<List<OptionalGameRespDTO>> getGameList(@RequestBody @Validated AssOptionalGameReqDTO param) {
        List<OptionalGameRespDTO> optionalGameDetailRespDTOS = assRemindPlanAppService.getOptionalGames(param);
        return PxResponse.ok(optionalGameDetailRespDTOS);
    }

}

