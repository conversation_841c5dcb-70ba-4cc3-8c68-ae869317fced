package com.pxb7.mall.workorder.infra.repository.db;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pxb7.mall.workorder.infra.model.RemindPlanRuleReqPO;
import com.pxb7.mall.workorder.infra.repository.db.entity.RemindPlanRule;

/**
 * 预警计划提醒规则配置(RemindPlanRule)表服务接口
 *
 * <AUTHOR>
 * @since 2025-03-24 21:09:56
 */
public interface RemindPlanRuleRepository extends IService<RemindPlanRule> {

    boolean insert(RemindPlanRuleReqPO.AddPO param);

    boolean update(RemindPlanRuleReqPO.UpdatePO param);

    boolean deleteById(RemindPlanRuleReqPO.DelPO param);

    boolean deleteBatchByIds(List<Long> params);

    RemindPlanRule findById(Long id);

    List<RemindPlanRule> list(RemindPlanRuleReqPO.SearchPO param);

    Page<RemindPlanRule> page(RemindPlanRuleReqPO.PagePO param);

    boolean saveBatch(List<RemindPlanRuleReqPO.AddPO> params);
}

