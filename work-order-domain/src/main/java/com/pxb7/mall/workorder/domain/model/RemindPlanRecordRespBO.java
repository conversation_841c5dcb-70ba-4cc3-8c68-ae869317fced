package com.pxb7.mall.workorder.domain.model;

import java.util.Date;
import java.time.*;
import java.math.BigDecimal;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.ToString;


/**
 * 预警执行计划记录(RemindPlanRecord)实体类
 *
 * <AUTHOR>
 * @since 2025-04-12 14:13:43
 */
public class RemindPlanRecordRespBO {


    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class DetailBO {
        private Long id;
        private String planRecordId;
        private Integer bizType;
        private String bizId;
        private String remindId;
        private Integer whichTime;
        private Boolean lastBeforeTimeout;
        private Integer status;
        private LocalDateTime remindTime;
        private Long planRuleId;
        private Long remindPlanId;
        private Long remindSubPlanId;
        private LocalDateTime createTime;
        private LocalDateTime updateTime;
        private String createUserId;
        private String updateUserId;
        private Boolean deleted;
    }
}

