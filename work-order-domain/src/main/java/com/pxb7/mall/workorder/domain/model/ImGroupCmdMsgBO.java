package com.pxb7.mall.workorder.domain.model;

import com.pxb7.mall.workorder.client.enums.TimeoutStatusEnum;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2025/4/29 14:50
 */

@Data
@Accessors(chain = true)
public class ImGroupCmdMsgBO {

    /**
     * 群聊id
     */
    private String groupId;

    /**
     * 群聊成员id
     */
    private String targetUserId;

    /**
     * 超时状态
     */
    private TimeoutStatusEnum status;

}
