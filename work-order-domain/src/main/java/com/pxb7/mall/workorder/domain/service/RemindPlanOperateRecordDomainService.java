package com.pxb7.mall.workorder.domain.service;


import java.util.ArrayList;
import java.util.List;

import com.pxb7.mall.workorder.domain.model.RemindPlanOperateRecordBO;
import com.pxb7.mall.workorder.domain.model.RemindPlanOperateRecordReqBO;
import com.pxb7.mall.workorder.infra.model.RemindPlanOperateRecordReqPO;
import com.pxb7.mall.workorder.infra.repository.db.RemindPlanOperateRecordRepository;
import com.pxb7.mall.workorder.infra.repository.db.entity.RemindPlanOperateRecord;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import com.pxb7.mall.workorder.domain.model.RemindPlanOperateRecordRespBO;
import com.pxb7.mall.workorder.domain.mapping.RemindPlanOperateRecordDomainMapping;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;


/**
 * 预警计划操作记录表domain服务
 *
 * <AUTHOR>
 * @since 2025-03-31 20:36:47
 */
@Service
public class RemindPlanOperateRecordDomainService {

    @Resource
    private RemindPlanOperateRecordRepository remindPlanOperateRecordRepository;

    public boolean insert(RemindPlanOperateRecordReqBO.AddBO param) {
        RemindPlanOperateRecordReqPO.AddPO addPO = RemindPlanOperateRecordDomainMapping.INSTANCE.remindPlanOperateRecordBO2AddPO(param);
        return remindPlanOperateRecordRepository.insert(addPO);
    }

    public boolean saveBatch(List<RemindPlanOperateRecordBO> params) {
        List<RemindPlanOperateRecordReqPO.AddPO> addPOList = new ArrayList<>();
        for (RemindPlanOperateRecordBO param : params) {
            RemindPlanOperateRecordReqPO.AddPO addPO = RemindPlanOperateRecordDomainMapping.INSTANCE.remindPlanOperateRecordBO2AddPO(param);
            addPOList.add(addPO);
        }
        return remindPlanOperateRecordRepository.saveBatch(addPOList);
    }

    public boolean update(RemindPlanOperateRecordReqBO.UpdateBO param) {
        RemindPlanOperateRecordReqPO.UpdatePO updatePO = RemindPlanOperateRecordDomainMapping.INSTANCE.remindPlanOperateRecordBO2UpdatePO(param);
        return remindPlanOperateRecordRepository.update(updatePO);
    }

    public boolean deleteById(RemindPlanOperateRecordReqBO.DelBO param) {
        RemindPlanOperateRecordReqPO.DelPO delPO = RemindPlanOperateRecordDomainMapping.INSTANCE.remindPlanOperateRecordBO2DelPO(param);
        return remindPlanOperateRecordRepository.deleteById(delPO);
    }

    public RemindPlanOperateRecordRespBO.DetailBO findById(Long id) {
        RemindPlanOperateRecord entity = remindPlanOperateRecordRepository.findById(id);
        return RemindPlanOperateRecordDomainMapping.INSTANCE.remindPlanOperateRecordPO2DetailBO(entity);

    }

    public List<RemindPlanOperateRecordRespBO.DetailBO> list(RemindPlanOperateRecordReqBO.SearchBO param) {
        RemindPlanOperateRecordReqPO.SearchPO searchPO = RemindPlanOperateRecordDomainMapping.INSTANCE.remindPlanOperateRecordBO2SearchPO(param);
        List<RemindPlanOperateRecord> list = remindPlanOperateRecordRepository.list(searchPO);
        return RemindPlanOperateRecordDomainMapping.INSTANCE.remindPlanOperateRecordPO2ListBO(list);
    }

    public Page<RemindPlanOperateRecordRespBO.DetailBO> page(RemindPlanOperateRecordReqBO.PageBO param) {
        RemindPlanOperateRecordReqPO.PagePO pagePO = RemindPlanOperateRecordDomainMapping.INSTANCE.remindPlanOperateRecordBO2PagePO(param);
        Page<RemindPlanOperateRecord> page = remindPlanOperateRecordRepository.page(pagePO);
        return RemindPlanOperateRecordDomainMapping.INSTANCE.remindPlanOperateRecordPO2PageBO(page);
    }

}

