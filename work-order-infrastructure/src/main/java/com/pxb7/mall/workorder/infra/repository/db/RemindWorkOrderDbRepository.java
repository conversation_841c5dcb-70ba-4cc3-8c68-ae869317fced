package com.pxb7.mall.workorder.infra.repository.db;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pxb7.mall.components.env.profile.PxProfileHelper;
import com.pxb7.mall.workorder.client.enums.CompleteStatusEnum;
import com.pxb7.mall.workorder.infra.mapping.WorkOrderMapping;
import com.pxb7.mall.workorder.infra.model.RemindWorkOrderReqPO;
import com.pxb7.mall.workorder.infra.repository.db.entity.RemindWorkOrder;
import com.pxb7.mall.workorder.infra.repository.db.mapper.RemindWorkOrderMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Objects;

/**
 * 商品工单预警记录(RemindWorkOrder)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-04-07 11:58:56
 */
@Slf4j
@Repository
public class RemindWorkOrderDbRepository extends ServiceImpl<RemindWorkOrderMapper, RemindWorkOrder> implements RemindWorkOrderRepository {

    @Override
    public boolean insert(RemindWorkOrderReqPO.AddPO param) {
        RemindWorkOrder entity = WorkOrderMapping.INSTANCE.addDto2PO(param);
        return this.save(entity);
    }

    @Override
    public RemindWorkOrder getByRemindId(String remindId) {
        if (StringUtils.isBlank(remindId)) {
            return null;
        }
        return this.lambdaQuery().eq(RemindWorkOrder::getRemindId, remindId).one();
    }

    @Override
    public boolean deleteByWorkOrderId(String workOrderId) {
        if (StringUtils.isBlank(workOrderId)) {
            return false;
        }
        return this.remove(
                new LambdaQueryWrapper<RemindWorkOrder>()
                        .eq(RemindWorkOrder::getWorkOrderId, workOrderId)
                        .eq(RemindWorkOrder::getCompleteStatus, CompleteStatusEnum.IN_PROCESS.getCode()));
    }

    @Override
    public boolean updateByWorkOrderId(RemindWorkOrderReqPO.UpdatePO param) {
        LambdaUpdateWrapper<RemindWorkOrder> updateWrapper = new LambdaUpdateWrapper<>();
        //where
        updateWrapper.eq(RemindWorkOrder::getWorkOrderId, param.getWorkOrderId());
        if (StringUtils.isNotBlank(param.getRemindId())) {
            updateWrapper.eq(RemindWorkOrder::getRemindId, param.getRemindId());
        }
        updateWrapper.eq(RemindWorkOrder::getCompleteStatus, CompleteStatusEnum.IN_PROCESS.getCode());
        //set
        if (Objects.nonNull(param.getCompleteStatus())) {
            updateWrapper.set(RemindWorkOrder::getCompleteStatus, param.getCompleteStatus());
        }
        if (Objects.nonNull(param.getTimeOutStatus())) {
            updateWrapper.set(RemindWorkOrder::getTimeOutStatus, param.getTimeOutStatus());
        }
        if (Objects.nonNull(param.getCompleteTime())) {
            updateWrapper.set(RemindWorkOrder::getCompleteTime, param.getCompleteTime());
        }
        return this.update(updateWrapper);
    }

    @Override
    public RemindWorkOrder findByWorkOrderId(String workOrderId, String remindId) {
        LambdaQueryWrapper<RemindWorkOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RemindWorkOrder::getWorkOrderId, workOrderId);
        queryWrapper.eq(RemindWorkOrder::getRemindId, remindId);
        return this.getOne(queryWrapper);
    }

    @Override
    public Page<RemindWorkOrder> page(RemindWorkOrderReqPO.PagePO param) {
        LambdaQueryWrapper<RemindWorkOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RemindWorkOrder::getEnvProfile, PxProfileHelper.getProfile().getProfileValue());
        queryWrapper.gt(RemindWorkOrder::getId, param.getId());
        queryWrapper.lt(RemindWorkOrder::getExpectCompleteTime, param.getExpectCompleteTime());
        queryWrapper.eq(RemindWorkOrder::getCompleteStatus, param.getCompleteStatus());
        queryWrapper.ne(RemindWorkOrder::getTimeOutStatus, param.getTimeOutStatus());
        queryWrapper.orderByAsc(RemindWorkOrder::getId);
        return this.page(new Page<>(param.getPageIndex(), param.getPageSize()), queryWrapper);
    }

    /**
     * 批量更新状态
     * @param ids
     * @param status
     * @return
     */
    @Override
    public boolean batchUpdateTimeoutStatusByIds(List<Long> ids, Integer status) {
        return this.getBaseMapper().batchUpdateTimeoutStatusByIds(ids, status) > 0;
    }

    /**
     * 是否存在跟进状态工单
     * @param workOrderId
     * @return
     */
    @Override
    public boolean existFollowUpStatusWorkOrder(String workOrderId) {
        return this.getBaseMapper().checkFollowUpStatusByWorkOrderId(workOrderId) > 0;
    }

}
