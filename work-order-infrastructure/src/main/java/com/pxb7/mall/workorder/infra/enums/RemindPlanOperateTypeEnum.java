package com.pxb7.mall.workorder.infra.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum RemindPlanOperateTypeEnum {

    ADD(1, "新增"),
    UPDATE(2, "修改"),
    DELETE(3, "删除"),
    REFRESH_ADD(4, "更新（新增）"),
    REFRESH_DELETE(5, "更新(删除)"),
    REFRESH_UPDATE(6, "更新(修改)"),

    ;

    private final Integer value;

    private final String label;

    public static String getLabel(Integer value) {
        return Arrays.stream(RemindPlanOperateTypeEnum.values()).filter(e -> e.getValue().equals(value))
            .map(RemindPlanOperateTypeEnum::getLabel).findAny().orElse("");
    }

    public static RemindPlanOperateTypeEnum getEnum(Integer value) {
        if (null == value) {
            return null;
        }
        return Arrays.stream(RemindPlanOperateTypeEnum.values()).filter(e -> value.equals(e.getValue())).findFirst()
            .orElse(null);
    }

}
