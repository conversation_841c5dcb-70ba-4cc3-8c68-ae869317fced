package com.pxb7.mall.workorder.infra.repository.db.mapper;

import com.pxb7.mall.workorder.infra.repository.db.entity.RemindSubPlan;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 提醒服务预警子计划(RemindSubPlan)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-03-24 21:09:56
 */
@Mapper
public interface RemindSubPlanMapper extends BaseMapper<RemindSubPlan> {
    /**
     * 批量新增数据
     *
     * @param entities List<RemindSubPlan> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<RemindSubPlan> entities);

}

