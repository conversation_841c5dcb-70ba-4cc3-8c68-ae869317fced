package com.pxb7.mall.workorder.infra.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class DeliveryStatictisSearchPO {

    /**
     * 起始时间
     */
    //@JsonFormat(pattern = "yyyy-MM-dd")
    private String startDate;

    /**
     * 结束时间
     */
    //@JsonFormat(pattern = "yyyy-MM-dd")
    private String endDate;


    /**
     * 游戏id 列表
     */
    private List<String> gameIds;


    /**
     * 交易类型，1:代售，2:中介
     */
    private Integer businessType;

    /**
     * 交易客服id列表
     */
    private List<String> deliveryCustomerCareIds;

}
