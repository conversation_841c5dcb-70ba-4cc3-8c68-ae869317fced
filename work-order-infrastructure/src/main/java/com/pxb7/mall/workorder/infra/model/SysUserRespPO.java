package com.pxb7.mall.workorder.infra.model;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
@Accessors(chain = true)
@ToString
public class SysUserRespPO {

    private String userId;
    private String userName;
    private String nickName;
    private String phone;
    private List<String> roleIds;
    private String roleName;
    private Boolean locked;
    private String companyName;
    private LocalDateTime hiredDate;
    private LocalDateTime departDate;
    private Integer onlineStatus;
    private Boolean imDisable;
    private String avatar;
    private String imPhone;
    private LocalDateTime createTime;
    private LocalDateTime lastLoginTime;
    private String userWorkStatus;
    private List<String> deptIds;
    private List<String> perms;
    private Integer cusType;
    private Boolean customerCareFlag;
    private Integer attributeType;
    private Boolean admin;
    /**
     * 飞书员工编号
     */
    private String employeeNo;

    /**
     * 飞书open_id
     */
    private String openId;

    /**
     * 飞书user_id
     */
    private String externalUserId;

    /**
     * 飞书用户名
     */
    private String externalName;

    /**
     * 授权时间
     */
    private LocalDateTime authTime;
}
