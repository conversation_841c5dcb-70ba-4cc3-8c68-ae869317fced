package com.pxb7.mall.workorder.domain.model;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import java.util.List;

@Data
public class AfterSaleStatisticDataSearchBO {

    /**
     * 工单类型 1:找回 2:纠纷
     */
    @NotNull(message = "工单类型参数不能为空")
    @Range(min = 1, max = 2, message = "工单类型参数错误")
    private Integer workOrderType;

    /**
     * 起始时间 精确到天
     */
    // @JsonFormat(pattern = "yyyy-MM-dd")
    // @NotNull(message = "起始时间不能为空")
    private String startDate;

    /**
     * 结束时间 精确到天
     */
    // @JsonFormat(pattern = "yyyy-MM-dd")
    // @NotNull(message = "结束时间不能为空")
    private String endDate;

    /**
     * 游戏id 列表
     */
    private List<String> gameIds;

    /**
     * 找回/纠纷处理人ID
     */
    private List<String> processUserIds;

    /**
     * 订单来源 1:散户 2:号商 3:3A号商
     */
    private Integer membership;

}
