package com.pxb7.mall.workorder.app.service;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pxb7.mall.workorder.client.enums.BizTypeEnum;
import com.pxb7.mall.workorder.client.enums.PlanExecuteStatusEnum;
import com.pxb7.mall.workorder.client.message.RemindPlanMessage;
import com.pxb7.mall.workorder.domain.model.RemindPlanRecordReqBO;
import com.pxb7.mall.workorder.domain.model.RemindPlanRecordRespBO;
import com.pxb7.mall.workorder.domain.service.BaseRemindDomainService;
import com.pxb7.mall.workorder.domain.service.RemindDomainServiceFactory;
import com.pxb7.mall.workorder.domain.service.RemindPlanRecordDomainService;
import com.pxb7.mall.workorder.infra.constant.CommonConstants;
import com.pxb7.mall.workorder.infra.constant.RMQConstant;
import com.pxb7.mall.workorder.infra.template.RocketMQEnhanceTemplate;
import com.pxb7.mall.workorder.infra.util.RedissonUtils;
import com.pxb7.mall.workorder.infra.util.TimeUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;


/**
 * 预警执行计划记录app服务
 *
 * <AUTHOR>
 * @since 2025-04-12 14:14:29
 */
@Slf4j
@Service
public class RemindPlanRecordAppService {

    /**
     * 提前分钟数
     */
    @Value("${remind.message.aheadMinutes:10}")
    private Integer aheadMinutes;

    @Resource
    private RemindDomainServiceFactory remindDomainServiceFactory;
    @Resource
    private RemindPlanRecordDomainService planRecordDomainService;
    @Resource
    private RocketMQEnhanceTemplate rocketMQEnhanceTemplate;


    /**
     * 执行预警计划任务
     * @param databaseName
     */
    public void executeRemindTask(String databaseName) {
        log.info("执行预警计划任务，任务开始，databaseName:{}", databaseName);
        //提前时间
        LocalDateTime aheadDateTime = TimeUtils.addSpecificTime(0, aheadMinutes, 0);

        //分页查询符合条件的提醒计划记录
        //游标id
        long minId = 0;
        long pageSize = CommonConstants.QUERY_PAGE_SIZE;
        while (true) {
            RemindPlanRecordReqBO.PageBO param = new RemindPlanRecordReqBO.PageBO();
            param.setId(minId);
            param.setStatus(PlanExecuteStatusEnum.WILL_EXECUTE.getCode());
            param.setRemindTime(aheadDateTime);
            param.setPageIndex(1L);
            param.setPageSize(pageSize);

            Page<RemindPlanRecordRespBO.DetailBO> page = planRecordDomainService.page(param);
            List<RemindPlanRecordRespBO.DetailBO> pageRecords = page.getRecords();
            if (CollectionUtils.isEmpty(pageRecords)) {
                break;
            }
            minId = pageRecords.get(pageRecords.size() - 1).getId();
            pageRecords.forEach(this::sendMQMessage);
            //更新状态为执行中
            List<Long> planRecordIds = pageRecords.stream()
                    .map(RemindPlanRecordRespBO.DetailBO::getId).toList();
            planRecordDomainService
                    .batchUpdateStatusByIds(planRecordIds, PlanExecuteStatusEnum.IN_PROCESS);
            log.info("执行预警计划任务，databaseName:{}，page:{}，page size:{}", databaseName, page.getCurrent(), page.getSize());
        }
        log.info("执行预警计划任务，任务结束，databaseName:{}", databaseName);
    }

    /**
     * 发送到mq中进行延时处理
     * @param planRecord
     */
    private void sendMQMessage(RemindPlanRecordRespBO.DetailBO planRecord) {
        //增加限流，限制写入mq的速度，tps最大为100
        RedissonUtils.rateLimit(CommonConstants.REMIND_PRODUCE_RATE_LIMITER_KEY, 100);

        RemindPlanMessage payload = new RemindPlanMessage();
        BeanUtils.copyProperties(planRecord, payload);
        //计算剩余时间
        long waitTime = TimeUtils.leftSeconds(payload.getRemindTime());
        if (waitTime <= 0) {
            //如果当前时间已经超过提醒时间，延时10s
            waitTime = 10;
        }
        log.info("发送预警消息到topic:{}, payload:{}, waitTime:{}", RMQConstant.REMIND_EVENT_TOPIC, JSON.toJSONString(payload), waitTime);
        rocketMQEnhanceTemplate.asyncSendDelay(RMQConstant.REMIND_EVENT_TOPIC, RMQConstant.REMIND_MESSAGE_TAG,
                    payload, Duration.ofSeconds(waitTime));
    }

    /**
     * 更新预警执行计划状态
     * @param remindPlanMessage
     * @param executeStatus
     * @return
     */
    public boolean updatePlanRecordStatus(RemindPlanMessage remindPlanMessage, PlanExecuteStatusEnum executeStatus) {
        return planRecordDomainService.updatePlanRecordStatus(remindPlanMessage, executeStatus);
    }

    /**
     * 处理提醒消息发送
     * @param remindPlanMessage
     */
    public PlanExecuteStatusEnum handleRemindMessage(RemindPlanMessage remindPlanMessage) {
        BaseRemindDomainService remindService =
                remindDomainServiceFactory.getRemindService(BizTypeEnum.getByType(remindPlanMessage.getBizType()));
        return remindService.handleRemindMessage(remindPlanMessage);
    }

    /**
     * 更新预警即将超时状态
     * @param remindPlanMessage
     */
    public void updateWillTimeOutStatus(RemindPlanMessage remindPlanMessage) {
        if (!Objects.equals(remindPlanMessage.getLastBeforeTimeout(), Boolean.TRUE)) {
            return;
        }
        BaseRemindDomainService remindService =
                remindDomainServiceFactory.getRemindService(BizTypeEnum.getByType(remindPlanMessage.getBizType()));
        remindService.updateWillTimeOutStatus(remindPlanMessage);
    }

}

