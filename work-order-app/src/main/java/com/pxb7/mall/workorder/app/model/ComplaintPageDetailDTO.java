package com.pxb7.mall.workorder.app.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Getter
@Setter
@Accessors(chain = true)
@ToString
public class ComplaintPageDetailDTO {


    /**
     * 预警Id
     */
    private String remindId;

    /**
     * 预警子计划ID
     */
    private Long remindSubPlanId;

    /**
     * 工单Id
     */
    private String workOrderId;

    /**
     * 工标题单
     */
    private String workOrderTitle;

    /**
     * 投诉渠道 1:IM 2支付宝 3闲鱼 4:12315 5消费宝 6连连支付 7电话 8反诈邮箱 9外部门升级 10黑猫投诉 11工商局 12工信部
     */
    private Integer channel;

    /**
     * 投诉级别 1-6 一级至六级
     */
    private Integer complaintLevel;

    /**
     * 投诉与建议内容
     */
    private String complaintContent;

    /**
     * 完结状态,1:未完结,2:已完结,3:交付终止
     */
    private Integer completeStatus;

    /**
     * 当前处理人Id
     */
    private String handleUserId;

    /**
     * 当前处理人
     */
    private String handleUserName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 预期完结时间：通过当前时间减去expectCompleteTime可以算出
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expectCompleteTime;

    /**
     *  完结时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime completeTime;

    /**
     * 工单持续时间提示："xx天xx小时xx分"
     */
    private String durationTip;

    /**
     * 倒计时提示："xx天xx小时xx分钟后超时" OR "已超时xx天xx小时xx分钟"
     */
    private String countDownTip;


}
