package com.pxb7.mall.workorder.domain.model;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;


/**
 * 售后工单预警记录
 *
 * <AUTHOR>
 * @since 2025-04-24 23:32:52
 */
@Getter
@Setter
@Accessors(chain = true)
@ToString
public class RemindAfterSaleBO extends BaseRemindRecordBO {

    /**
     * 工单id
     */
    private String workOrderId;
    /**
     * 工单编号
     */
    private String workOrderNo;
    /**
     * 商品id
     */
    private String productId;
    /**
     * 商品编码
     */
    private String productCode;
    /**
     * 游戏id
     */
    private String gameId;
    /**
     * 群组id
     */
    private String groupId;
    /**
     * 订单id
     */
    private String orderId;
    /**
     * 子订单id
     */
    private String orderItemId;
    /**
     * 找回处理人员
     */
    private String retrieveUserId;
    /**
     * 纠纷处理人员
     */
    private String disputeUserId;
    /**
     * 工单类型  1:找回 2:纠纷
     */
    private Integer workOrderType;

}
