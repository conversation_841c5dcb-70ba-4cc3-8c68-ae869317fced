package com.pxb7.mall.workorder.domain.model;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;


/**
 * 客诉工单预警记录
 *
 * <AUTHOR>
 * @since 2025-04-24 23:32:52
 */
@Getter
@Setter
@Accessors(chain = true)
@ToString
public class RemindComplaintBO extends BaseRemindRecordBO {

    /**
     * 工单id
     */
    private String workOrderId;
    /**
     * 工单标题
     */
    private String workOrderTitle;
    /**
     * 投诉渠道
     */
    private String channel;
    /**
     * 投诉级别
     */
    private String complaintLevel;
    /**
     * 投诉内容
     */
    private String complaintContent;
    /**
     * 群组id
     */
    private String groupId;
    /**
     * 处理人员
     */
    private String handleUserId;

}
