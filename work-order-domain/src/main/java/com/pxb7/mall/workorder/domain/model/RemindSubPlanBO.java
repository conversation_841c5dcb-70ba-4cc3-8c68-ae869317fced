package com.pxb7.mall.workorder.domain.model;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.List;

@Getter
@Setter
@Accessors(chain = true)
@ToString
public class RemindSubPlanBO {

    private Long id;

    private Long remindPlanId;

    private Integer businessType;

    private Integer workOrderStatus;

    private Integer onShelfType;

    private Integer membership;

    private Integer workOrderType;

    private Integer complaintLevel;

    private String createUserId;

    private String updateUserId;

    /**
     * 预警游戏配置
     */
    private List<RemindPlanGameConfigBO> remindPlanGameConfigs;
}
