package com.pxb7.mall.workorder.app;

import com.pxb7.mall.trade.ass.client.dto.response.afc.ComplaintWORespDTO;
import com.pxb7.mall.workorder.app.service.RemindComplaintAppService;
import com.pxb7.mall.workorder.client.enums.BizTypeEnum;
import com.pxb7.mall.workorder.client.enums.CompleteStatusEnum;
import com.pxb7.mall.workorder.domain.model.RemindComplaintRespBO;
import com.pxb7.mall.workorder.domain.service.RemindComplaintDomainService;
import com.pxb7.mall.workorder.domain.service.RemindPlanGameConfigDomainService;
import com.pxb7.mall.workorder.domain.service.RemindPlanRecordDomainService;
import com.pxb7.mall.workorder.infra.repository.gateway.dubbo.ass.AfcWorkOrderGateway;
import com.pxb7.mall.workorder.infra.repository.gateway.dubbo.user.SysUserGateway;
import com.pxb7.mall.workorder.infra.util.RedissonUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.mockito.Mockito.*;

class RemindComplaintAppServiceTest {

    @InjectMocks
    private RemindComplaintAppService service;

    @Mock
    private AfcWorkOrderGateway afcWorkOrderGateway;

    @Mock
    private RemindComplaintDomainService complaintDomainService;

    @Mock
    private RemindPlanRecordDomainService planRecordDomainService;

    @Mock
    private RemindPlanGameConfigDomainService gameConfigDomainService;

    @Mock
    private SysUserGateway sysUserGateway;

    @Mock
    private RedissonUtils redissonUtils;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testDealComplaintWorkOrder_WorkOrderNotFound() {
        when(afcWorkOrderGateway.getComplaintWorkOrderInfo("123")).thenReturn(null);

        service.dealComplaintWorkOrder("123");

        verify(complaintDomainService, never()).generateRemindComplaint(any(), any());
        verify(planRecordDomainService, never()).invalidPlanRecord(anyInt(), anyString());
    }

    @Test
    void testDealComplaintWorkOrder_InProcessStatus() {
        ComplaintWORespDTO dto = new ComplaintWORespDTO();
        dto.setWorkOrderId("123");
        dto.setWorkOrderStatus(CompleteStatusEnum.IN_PROCESS.getCode());

        when(afcWorkOrderGateway.getComplaintWorkOrderInfo("123")).thenReturn(dto);

        service.dealComplaintWorkOrder("123");

        verify(complaintDomainService).generateRemindComplaint(any(), any());
        verify(planRecordDomainService, never()).invalidPlanRecord(anyInt(), anyString());
    }

    @Test
    void testGenerateRemindPlan_ConfigIsNull() {
        ComplaintWORespDTO dto = new ComplaintWORespDTO();
        dto.setComplaintLevel(1);
        dto.setComplaintChannel(2);

        when(gameConfigDomainService.getComplaintRemindPlanGameConfig(1, 2)).thenReturn(null);

        service.generateRemindPlan(dto);

        verify(complaintDomainService, never()).generateRemindComplaint(any(), any());
    }

    @Test
    void testChangeRemindPlanStatus_RemindComplaintExists() {
        ComplaintWORespDTO dto = new ComplaintWORespDTO();
        dto.setWorkOrderId("123");
        dto.setWorkOrderStatus(CompleteStatusEnum.DONE.getCode());

        RemindComplaintRespBO.DetailBO detailBO = new RemindComplaintRespBO.DetailBO();
        detailBO.setGroupId("group1");
        detailBO.setHandleUserId("user1");

        when(complaintDomainService.findByWorkOrderId("123", null)).thenReturn(detailBO);

        service.changeRemindPlanStatus(dto);

        verify(planRecordDomainService).invalidPlanRecord(BizTypeEnum.COMPLAINT.getType(), "123");
        verify(redissonUtils).deleteObject(anyString());
    }
}
