package com.pxb7.mall.workorder.infra.repository.db.mapper;

import com.pxb7.mall.workorder.infra.repository.db.entity.RemindWorkOrder;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 商品工单预警记录(RemindWorkOrder)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-04-07 11:58:53
 */
@Mapper
public interface RemindWorkOrderMapper extends BaseMapper<RemindWorkOrder> {
    /**
     * 批量新增数据
     *
     * @param entities List<RemindWorkOrder> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<RemindWorkOrder> entities);

    /**
     * 批量修改超时状态
     * @param ids
     * @param status
     */
    int batchUpdateTimeoutStatusByIds(List<Long> ids, Integer status);

    /**
     * 检查工单是否存在已跟进
     * @param workOrderId
     * @return
     */
    int checkFollowUpStatusByWorkOrderId(@Param("workOrderId") String workOrderId);

}

