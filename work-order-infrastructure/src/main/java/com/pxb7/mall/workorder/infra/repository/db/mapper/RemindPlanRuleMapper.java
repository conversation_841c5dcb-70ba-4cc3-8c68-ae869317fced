package com.pxb7.mall.workorder.infra.repository.db.mapper;

import com.pxb7.mall.workorder.infra.repository.db.entity.RemindPlanRule;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 预警计划提醒规则配置(RemindPlanRule)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-03-24 21:09:56
 */
@Mapper
public interface RemindPlanRuleMapper extends BaseMapper<RemindPlanRule> {
    /**
     * 批量新增数据
     *
     * @param entities List<RemindPlanRule> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<RemindPlanRule> entities);

}

