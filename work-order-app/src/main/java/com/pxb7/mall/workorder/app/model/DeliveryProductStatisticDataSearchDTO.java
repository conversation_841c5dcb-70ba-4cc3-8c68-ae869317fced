package com.pxb7.mall.workorder.app.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.pxb7.mall.workorder.infra.enums.RemindPlanBusinessTypeEnum;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
@Accessors(chain = true)
@ToString
public class DeliveryProductStatisticDataSearchDTO {

    /**
     * 起始时间
     */
    //@JsonFormat(pattern = "yyyy-MM-dd")
   // @NotNull(message = "起始时间不能为空")
    private String startDate;

    /**
     * 结束时间
     */
    //@JsonFormat(pattern = "yyyy-MM-dd")
    //@NotNull(message = "结束时间不能为空")
    private String endDate;


    /**
     * 游戏id 列表
     */
    private List<String> gameIds;


    /**
     * 交易类型，1:代售，2:中介
     */
    private Integer businessType;

    /**
     * 交易客服id列表
     */
    private List<String> deliveryCustomerCareIds;
}
