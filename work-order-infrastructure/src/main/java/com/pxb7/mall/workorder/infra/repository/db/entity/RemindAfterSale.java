package com.pxb7.mall.workorder.infra.repository.db.entity;

import java.io.Serializable;
import java.util.Date;
import java.time.*;
import java.math.BigDecimal;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import com.baomidou.mybatisplus.annotation.*;

/**
 * 售后工单预警记录(RemindAfterSale)实体类
 *
 * <AUTHOR>
 * @since 2025-04-24 23:31:56
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "remind_after_sale")
@ToString
public class RemindAfterSale implements Serializable {
    private static final long serialVersionUID = -78808605079325933L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 预警记录id
     */
    @TableField(value = "remind_id")
    private String remindId;
    /**
     * 工单id
     */
    @TableField(value = "work_order_id")
    private String workOrderId;
    /**
     * 工单编号
     */
    @TableField(value = "work_order_no")
    private String workOrderNo;
    /**
     * 商品id
     */
    @TableField(value = "product_id")
    private String productId;
    /**
     * 商品编码
     */
    @TableField(value = "product_code")
    private String productCode;
    /**
     * 游戏id
     */
    @TableField(value = "game_id")
    private String gameId;
    /**
     * 群组id
     */
    @TableField(value = "group_id")
    private String groupId;
    /**
     * 订单id
     */
    @TableField(value = "order_id")
    private String orderId;
    /**
     * 子订单id
     */
    @TableField(value = "order_item_id")
    private String orderItemId;
    /**
     * 完结状态,1:未完结,2:已完结,3:终止
     */
    @TableField(value = "complete_status")
    private Integer completeStatus;
    /**
     * 超时状态，0:无,1:即将超时,2:已超时
     */
    @TableField(value = "time_out_status")
    private Integer timeOutStatus;
    /**
     * 工单类型  1:找回 2:纠纷
     */
    @TableField(value = "work_order_type")
    private Integer workOrderType;
    /**
     * 找回处理人员
     */
    @TableField(value = "retrieve_user_id")
    private String retrieveUserId;
    /**
     * 纠纷处理人员
     */
    @TableField(value = "dispute_user_id")
    private String disputeUserId;
    /**
     * 预期完结时间
     */
    @TableField(value = "expect_complete_time")
    private LocalDateTime expectCompleteTime;
    /**
     * im客服端倒计时开始时间
     */
    @TableField(value = "im_count_down_time")
    private LocalDateTime imCountDownTime;
    /**
     * 完结时间
     */
    @TableField(value = "complete_time")
    private LocalDateTime completeTime;
    /**
     * 预警计划游戏配置id,remind_plan_game_config
     */
    @TableField(value = "game_config_id")
    private Long gameConfigId;
    /**
     * 预警计划id,remind_plan
     */
    @TableField(value = "remind_plan_id")
    private Long remindPlanId;
    /**
     * 预警子计划id,remind_sub_plan
     */
    @TableField(value = "remind_sub_plan_id")
    private Long remindSubPlanId;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;
    /**
     * 创建人
     */
    @TableField(value = "create_user_id")
    private String createUserId;
    /**
     * 更新人
     */
    @TableField(value = "update_user_id")
    private String updateUserId;
    /**
     * 是否删除 0:否, 1:是
     */
    @TableLogic
    @TableField(value = "is_deleted")
    private Boolean deleted;

    /**
      * 环境变量
      */
     @TableField(value = "env_profile", fill = FieldFill.INSERT)
     private Integer envProfile;
}

