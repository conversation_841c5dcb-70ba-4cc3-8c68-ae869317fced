package com.pxb7.mall.workorder.infra.repository.es.entity;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;
import lombok.experimental.FieldNameConstants;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 账号交付预警记录(RemindDeliveryProduct)实体类
 *
 * <AUTHOR>
 * @since 2025-03-27 20:07:05
 */
@Getter
@Setter
@Accessors(chain = true)
@ToString
@FieldNameConstants
@Document(indexName = "remind_delivery_product")
public class RemindDeliveryProductDoc implements Serializable {

    @Serial
    private static final long serialVersionUID = -31016803846050585L;

    /**
     * 预警记录id
     */
    @Id
    @Field(value = "remind_id", type = FieldType.Keyword)
    private String remindId;
    /**
     * 商品id
     */
    @Field(value = "product_id", type = FieldType.Keyword)
    private String productId;
    /**
     * 商品编码
     */
    @Field(value = "product_code", type = FieldType.Keyword)
    private String productCode;
    /**
     * 游戏id
     */
    @Field(value = "game_id", type = FieldType.Keyword)
    private String gameId;
    /**
     * 群组id
     */
    @Field(value = "group_id", type = FieldType.Keyword)
    private String groupId;
    /**
     * 订单id
     */
    @Field(value = "order_id", type = FieldType.Keyword)
    private String orderId;
    /**
     * 子订单id
     */
    @Field(value = "order_item_id", type = FieldType.Keyword)
    private String orderItemId;
    /**
     * 完结状态,1:未完结,2:已完结,3:终止
     */
    @Field(value = "complete_status", type = FieldType.Keyword)
    private Integer completeStatus;
    /**
     * 超时状态，0: 无 1:即将超时 2:已超时
     */
    @Field(value = "time_out_status", type = FieldType.Keyword)
    private Integer timeOutStatus;
    /**
     * 交付客服
     */
    @Field(value = "delivery_customer_care", type = FieldType.Keyword)
    private String deliveryCustomerCare;
    /**
     * 预期完结时间
     */
    @Field(type = FieldType.Date,format = DateFormat.date_hour_minute_second_fraction,value = "expect_complete_time")
    private LocalDateTime expectCompleteTime;
    /**
     * 预期完结时间
     */
    @Field(type = FieldType.Date,format = DateFormat.date_hour_minute_second_fraction,value = "complete_time")
    private LocalDateTime completeTime;
    /**
     * im客服端倒计时开始时间
     */
    @Field(type = FieldType.Date,format = DateFormat.date_hour_minute_second_fraction,value = "im_count_down_time")
    private LocalDateTime imCountDownTime;
    /**
     * 预警计划游戏配置id,remind_plan_game_config
     */
    @Field(value = "game_config_id", type = FieldType.Keyword)
    private Long gameConfigId;
    /**
     * 预警计划id,remind_plan
     */
    @Field(value = "remind_plan_id", type = FieldType.Keyword)
    private Long remindPlanId;
    /**
     * 预警子计划id,remind_sub_plan
     */
    @Field(value = "remind_sub_plan_id", type = FieldType.Keyword)
    private Long remindSubPlanId;

    /**
     * 创建时间
     */
    @Field(type = FieldType.Date,format = DateFormat.date_hour_minute_second_fraction,value = "create_time")
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @Field(type = FieldType.Date,format = DateFormat.date_hour_minute_second_fraction,value = "update_time")
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    @Field(value = "create_user_id", type = FieldType.Keyword)
    private String createUserId;

    /**
     * 更新人
     */
    @Field(value = "update_user_id", type = FieldType.Keyword)
    private String updateUserId;

}

