package com.pxb7.mall.workorder.infra.repository.db;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pxb7.mall.workorder.infra.model.AdsPxb7ComplaintForewarningReqPO;
import com.pxb7.mall.workorder.infra.repository.db.entity.AdsPxb7ComplaintForewarning;

/**
 * 客诉预警统计(AdsPxb7ComplaintForewarning)表服务接口
 *
 * <AUTHOR>
 * @since 2025-05-08 18:15:03
 */
public interface AdsPxb7ComplaintForewarningRepository extends IService<AdsPxb7ComplaintForewarning> {

    boolean insert(AdsPxb7ComplaintForewarningReqPO.AddPO param);

    boolean update(AdsPxb7ComplaintForewarningReqPO.UpdatePO param);

    boolean deleteById(AdsPxb7ComplaintForewarningReqPO.DelPO param);

    AdsPxb7ComplaintForewarning findById(Long id);

    List<AdsPxb7ComplaintForewarning> list(AdsPxb7ComplaintForewarningReqPO.SearchPO param);

    Page<AdsPxb7ComplaintForewarning> page(AdsPxb7ComplaintForewarningReqPO.PagePO param);

}
