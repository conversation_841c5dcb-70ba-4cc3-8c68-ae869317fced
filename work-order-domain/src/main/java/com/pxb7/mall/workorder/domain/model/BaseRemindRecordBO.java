package com.pxb7.mall.workorder.domain.model;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 预警记录基类
 *
 * <AUTHOR>
 * @since 2025-03-27 20:08:02
 */
@Getter
@Setter
@Accessors(chain = true)
@ToString
public class BaseRemindRecordBO {

    /**
     * 预警记录id
     */
    private String remindId;
    /**
     * 完结状态,1:未完结,2:已完结,3:终止
     */
    private Integer completeStatus;
    /**
     * 超时状态，0:无,1:即将超时,2:已超时
     */
    private Integer timeOutStatus;
    /**
     * 预期完结时间
     */
    private LocalDateTime expectCompleteTime;
    /**
     * 完结时间
     */
    private LocalDateTime completeTime;
    /**
     * im客服端倒计时开始时间
     */
    private LocalDateTime imCountDownTime;
    /**
     * 预警计划游戏配置id,remind_plan_game_config
     */
    private Long gameConfigId;
    /**
     * 预警计划id,remind_plan
     */
    private Long remindPlanId;
    /**
     * 预警子计划id,remind_sub_plan
     */
    private Long remindSubPlanId;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    /**
     * 创建人
     */
    private String createUserId;
    /**
     * 更新人
     */
    private String updateUserId;
    /**
     * 是否删除 0:否, 1:是
     */
    private Boolean deleted;

}

