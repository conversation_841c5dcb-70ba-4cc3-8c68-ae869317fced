package com.pxb7.mall.workorder.infra.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum ComplaintChannelEnum {

    // 投诉渠道 1:IM 2:支付宝 3:闲鱼 4:12315 5:消费宝 6:连连支付 7:电话 8:反诈邮箱 9:外部门升级 10:黑猫投诉 11:工商局 12:工信部
    IM(1, "IM"),
    ALIPAY(2, "支付宝"),
    IDLE_FISH(3, "闲鱼"),
    DISPUTE(4, "12315"),
    CONSUMER_TREASURE(5, "消费宝"),
    LL_PAY(6, "连连支付"),
    PHONE(7, "电话"),
    EMAIL(8, "反诈邮箱"),
    OTHER_DEPARTMENT(9, "外部门升级"),
    BLACK_CAT(10, "黑猫投诉"),
    AIC(11, "工商局"),
    MIIT(12, "工信部"),
    ;

    private final Integer value;

    private final String label;

    public static String getLabel(Integer value) {
        return Arrays.stream(ComplaintChannelEnum.values()).filter(e -> e.getValue().equals(value))
            .map(ComplaintChannelEnum::getLabel).findAny().orElse("");
    }

    public static ComplaintChannelEnum getEnum(Integer value) {
        if (null == value) {
            return null;
        }
        return Arrays.stream(ComplaintChannelEnum.values()).filter(e -> value.equals(e.getValue())).findFirst()
            .orElse(null);
    }

}
