package com.pxb7.mall.workorder.app.service;

import com.alibaba.cola.exception.Assert;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pxb7.mall.auth.c.util.AdminUserUtil;
import com.pxb7.mall.auth.dto.AdminUserDTO;
import com.pxb7.mall.workorder.app.mapping.NotDisturbPeriodDomainMapping;
import com.pxb7.mall.workorder.app.mapping.RemindPlanAppMapping;
import com.pxb7.mall.workorder.app.mapping.RemindPlanRuleAppMapping;
import com.pxb7.mall.workorder.app.model.*;
import com.pxb7.mall.workorder.app.util.PlanRuleOrGameConfigChecker;
import com.pxb7.mall.workorder.app.util.RemindPlanBuildUtil;
import com.pxb7.mall.workorder.app.util.RemindPlanGameConfigBuildUtil;
import com.pxb7.mall.workorder.app.util.RemindPlanOperateRecordBuildUtil;
import com.pxb7.mall.workorder.app.util.RemindPlanPageBuildUtil;
import com.pxb7.mall.workorder.app.util.RemindPlanRuleBuildUtil;
import com.pxb7.mall.workorder.domain.model.RemindPlanBO;
import com.pxb7.mall.workorder.domain.model.RemindPlanDeleteContextBO;
import com.pxb7.mall.workorder.domain.model.RemindPlanGameConfigBO;
import com.pxb7.mall.workorder.domain.model.RemindPlanGameConfigRefreshBO;
import com.pxb7.mall.workorder.domain.model.RemindPlanOperateRecordBO;
import com.pxb7.mall.workorder.domain.model.RemindPlanReqBO;
import com.pxb7.mall.workorder.domain.model.RemindPlanRuleBO;
import com.pxb7.mall.workorder.domain.model.RemindPlanRuleRefreshBO;
import com.pxb7.mall.workorder.domain.model.RemindPlanUpdateContextBO;
import com.pxb7.mall.workorder.domain.model.RemindSubPlanBO;
import com.pxb7.mall.workorder.domain.service.RemindPlanDomainService;
import com.pxb7.mall.workorder.domain.service.RemindPlanGameConfigDomainService;
import com.pxb7.mall.workorder.domain.service.RemindSubPlanDomainService;
import com.pxb7.mall.workorder.infra.aop.ClusterRedisLock;
import com.pxb7.mall.workorder.infra.enums.ComplaintChannelEnum;
import com.pxb7.mall.workorder.infra.enums.RemindPlanServiceTypeEnum;
import com.pxb7.mall.workorder.infra.model.SysUserRespPO;
import com.pxb7.mall.workorder.infra.repository.gateway.dubbo.user.SysUserGateway;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.pxb7.mall.workorder.client.enums.BizErrorCodeEnum.PARAM_ERROR;
import static com.pxb7.mall.workorder.client.enums.BizErrorCodeEnum.REMIND_PLAN_NOT_FOUND;
import static com.pxb7.mall.workorder.client.enums.BizErrorCodeEnum.USER_INFO_NOT_FOUND;

/**
 * 提醒服务预警计划app服务
 *
 * <AUTHOR>
 * @since 2025-04-22 09:12:17
 */
@Service
public class ComplaintRemindPlanAppService {

    @Resource
    private RemindPlanDomainService remindPlanDomainService;

    @Resource
    private RemindSubPlanDomainService remindSubPlanDomainService;

    @Resource
    private RemindPlanGameConfigDomainService remindPlanGameConfigDomainService;

    @Resource
    private SysUserGateway sysUserGateway;

    @ClusterRedisLock(prefix = "complaint_remind_plan_insert_lock", value = "")
    public Long insert(ComplaintRemindPlanReqDTO.AddDTO param) {
        AdminUserDTO adminUser = AdminUserUtil.getAdminUser();
        Assert.isTrue(adminUser != null && StringUtils.isNotBlank(adminUser.getUserId()),
            USER_INFO_NOT_FOUND.getErrCode(), USER_INFO_NOT_FOUND.getErrDesc());

        RemindPlanServiceTypeEnum serviceTypeEnum = RemindPlanServiceTypeEnum.getEnum(param.getServiceType());
        Assert.isTrue(serviceTypeEnum != null, PARAM_ERROR.getErrCode(), "业务类型暂不支持");

        // 判断计划名称是否重复
        Boolean existPlanName = remindPlanDomainService.existPlanName(param.getPlanName(), param.getServiceType());
        Assert.isFalse(existPlanName, PARAM_ERROR.getErrCode(), "预警计划名称已存在");

        //判断同一个投诉级别同一渠道渠道下，不能有重复的提醒计划
        List<ComplaintPlanGameConfigDTO> planGameConfigs = param.getPlanGameConfigs();
        for (ComplaintPlanGameConfigDTO planGameConfig : planGameConfigs) {
            RemindPlanGameConfigBO remindPlanGameConfig = remindPlanGameConfigDomainService.getComplaintRemindPlanGameConfig(
                    param.getComplaintLevel(), planGameConfig.getChannel());
            Assert.isTrue(Objects.isNull(remindPlanGameConfig), PARAM_ERROR.getErrCode(),
                    String.format("该投诉级别(%s)渠道配置已存在", ComplaintChannelEnum.getLabel(planGameConfig.getChannel())));
        }

        PlanRuleOrGameConfigChecker.checkPlanRuleSorted(param.getPlanRules());
        PlanRuleOrGameConfigChecker.complaintCompareGameConfigTime(param.getPlanGameConfigs());
        PlanRuleOrGameConfigChecker.complaintComparePlanRuleAndGameConfigTime(param.getPlanRules(),
            param.getPlanGameConfigs());
        RemindPlanBO remindPlanBO = RemindPlanBuildUtil.buildComplaintFromAddDTO(param, adminUser);
        List<RemindPlanOperateRecordBO> remindPlanOperateRecordBOS =
            RemindPlanOperateRecordBuildUtil.buildAddOperateRecordList(remindPlanBO, adminUser);
        return remindPlanDomainService.saveAllInfo(remindPlanBO, remindPlanOperateRecordBOS);
    }

    @ClusterRedisLock(prefix = "complaint_remind_plan_update_lock", value = "")
    public boolean update(ComplaintRemindPlanReqDTO.UpdateDTO param) {
        AdminUserDTO adminUser = AdminUserUtil.getAdminUser();
        Assert.isTrue(adminUser != null && StringUtils.isNotBlank(adminUser.getUserId()),
            USER_INFO_NOT_FOUND.getErrCode(), USER_INFO_NOT_FOUND.getErrDesc());

        RemindPlanServiceTypeEnum serviceTypeEnum = RemindPlanServiceTypeEnum.getEnum(param.getServiceType());
        Assert.isTrue(serviceTypeEnum != null, PARAM_ERROR.getErrCode(), "业务类型暂不支持");

        List<PlanRuleDTO> planRuleDTOS = param.getPlanRules();
        PlanRuleOrGameConfigChecker.checkPlanRuleSorted(planRuleDTOS);
        PlanRuleOrGameConfigChecker.complaintCompareGameConfigTime(param.getPlanGameConfigs());
        PlanRuleOrGameConfigChecker.complaintComparePlanRuleAndGameConfigTime(param.getPlanRules(),
            param.getPlanGameConfigs());
        // 修改前预警配置
        RemindPlanBO remindPlanBO = remindPlanDomainService.getRemindPlanAllInfo(param.getId());
        Assert.notNull(remindPlanBO, REMIND_PLAN_NOT_FOUND.getErrCode(), REMIND_PLAN_NOT_FOUND.getErrDesc());
        RemindPlanBO originalRemindPlanBO = new RemindPlanBO();
        BeanUtils.copyProperties(remindPlanBO, originalRemindPlanBO);
        remindPlanBO.setPlanName(param.getPlanName());
        remindPlanBO.setNotDisturbPeriod(
            NotDisturbPeriodDomainMapping.INSTANCE.notDisturbPeriodDTO2BO(param.getNotDisturbPeriod()));
        remindPlanBO.setUpdateUserId(adminUser.getUserId());
        remindPlanBO.setUpdateTime(LocalDateTime.now());

        // 判断计划名称是否重复
        if (!param.getPlanName().equals(originalRemindPlanBO.getPlanName())) {
            Boolean existPlanName =
                remindPlanDomainService.existPlanName(param.getPlanName(), originalRemindPlanBO.getServiceType());
            Assert.isFalse(existPlanName, PARAM_ERROR.getErrCode(), "预警计划名称已存在");
        }

        // 预警-游戏配置：
        // 新增了哪些？删除了哪些？ 修改了哪些？
        RemindPlanGameConfigRefreshBO remindPlanGameConfigRefreshBO = RemindPlanGameConfigBuildUtil
            .buildComplaintRemindPlanGameConfigRefreshBO(param.getPlanGameConfigs(), remindPlanBO, adminUser);

        // 新增了哪些？删除了哪些？ 修改了哪些？
        RemindPlanRuleRefreshBO remindPlanRuleRefreshBO =
            RemindPlanRuleBuildUtil.buildRemindPlanRuleRefreshBO(planRuleDTOS, remindPlanBO, adminUser);

        RemindPlanUpdateContextBO remindPlanUpdateContextBO = new RemindPlanUpdateContextBO();
        remindPlanUpdateContextBO.setOriginRemindPlan(originalRemindPlanBO);
        remindPlanUpdateContextBO.setRemindPlan(remindPlanBO);
        remindPlanUpdateContextBO.setRemindPlanRuleRefresh(remindPlanRuleRefreshBO);
        remindPlanUpdateContextBO.setRemindPlanGameConfigRefresh(remindPlanGameConfigRefreshBO);
        remindPlanUpdateContextBO.setOperateRecords(
            RemindPlanOperateRecordBuildUtil.buildUpdateOperateRecordList(remindPlanUpdateContextBO, adminUser));
        return remindPlanDomainService.updateAllInfo(remindPlanUpdateContextBO);
    }

    public ComplaintRemindPlanRespDTO.DetailDTO findById(Long remindPlanId) {
        AdminUserDTO adminUser = AdminUserUtil.getAdminUser();
        Assert.isTrue(adminUser != null && StringUtils.isNotBlank(adminUser.getUserId()),
            USER_INFO_NOT_FOUND.getErrCode(), USER_INFO_NOT_FOUND.getErrDesc());

        RemindPlanBO remindPlanBO = remindPlanDomainService.getRemindPlanAllInfo(remindPlanId);
        Assert.notNull(remindPlanBO, REMIND_PLAN_NOT_FOUND.getErrCode(), REMIND_PLAN_NOT_FOUND.getErrDesc());

        List<RemindPlanGameConfigBO> allRemindPlanGameConfigBOS =
            Optional.of(remindPlanBO).map(RemindPlanBO::getAllRemindPlanGameConfigs).orElse(new ArrayList<>());

        Integer serviceType = remindPlanBO.getServiceType();
        List<RemindSubPlanBO> remindSubPlans = remindPlanBO.getRemindSubPlans();
        ComplaintRemindPlanRespDTO.DetailDTO detailDTO = new ComplaintRemindPlanRespDTO.DetailDTO();
        detailDTO.setId(remindPlanBO.getId());
        detailDTO.setPlanName(remindPlanBO.getPlanName());
        detailDTO.setServiceType(serviceType);
        detailDTO.setNotDisturbPeriod(
            NotDisturbPeriodDomainMapping.INSTANCE.notDisturbPeriodBO2DTO(remindPlanBO.getNotDisturbPeriod()));
        detailDTO.setComplaintLevel(remindSubPlans.get(0).getComplaintLevel());
        detailDTO.setPlanGameConfigs(
            RemindPlanGameConfigBuildUtil.complaintPlanGameConfigDTOList(allRemindPlanGameConfigBOS));
        detailDTO.setPlanRules(
            RemindPlanRuleAppMapping.INSTANCE.remindPlanRuleBO2ListDTO(remindPlanBO.getRemindPlanRules()));
        return detailDTO;
    }

    public Page<RemindPlanPageDetailDTO> page(ComplaintRemindPlanReqDTO.PageDTO param) {
        AdminUserDTO adminUser = AdminUserUtil.getAdminUser();
        Assert.isTrue(adminUser != null && StringUtils.isNotBlank(adminUser.getUserId()),
            USER_INFO_NOT_FOUND.getErrCode(), USER_INFO_NOT_FOUND.getErrDesc());

        RemindPlanReqBO.PageBO pageBO = RemindPlanAppMapping.INSTANCE.remindPlanDTO2PageBO(param);
        pageBO.setServiceType(RemindPlanServiceTypeEnum.COMPLAIN_ORDER.getValue());
        Page<RemindPlanBO> page = remindPlanDomainService.page(pageBO);
        List<RemindPlanBO> remindPlans = Optional.ofNullable(page).map(Page::getRecords).orElse(new ArrayList<>());
        List<Long> remindPlanIds = remindPlans.stream().map(RemindPlanBO::getId).toList();
        List<RemindSubPlanBO> remindSubPlanList = remindSubPlanDomainService.getRemindSubPlanList(remindPlanIds);
        List<RemindPlanGameConfigBO> remindPlanGameConfigList =
            remindPlanGameConfigDomainService.getRemindPlanGameConfigList(remindPlanIds);

        Set<String> userIds = remindPlans.stream().map(RemindPlanBO::getUpdateUserId).collect(Collectors.toSet());
        List<SysUserRespPO> sysUserInfoList = sysUserGateway.getSysUserInfoList(userIds);
        Map<String, SysUserRespPO> sysUserRespPOMap =
            sysUserInfoList.stream().collect(Collectors.toMap(SysUserRespPO::getUserId, Function.identity()));

        Page<RemindPlanPageDetailDTO> pageDTO = RemindPlanAppMapping.INSTANCE.remindPlanBO2PageDTO(page);
        // 构造返回的dto数据
        List<RemindPlanPageDetailDTO> detailDTOS = RemindPlanPageBuildUtil.buildPageDetailDTOList(remindPlans,
            remindSubPlanList, remindPlanGameConfigList, sysUserRespPOMap);
        pageDTO.setRecords(detailDTOS);
        return pageDTO;
    }

    /**
     * 根据ID删除提醒计划
     *
     * 此方法首先验证当前用户是否已登录并具有管理员权限，然后根据提供的参数获取提醒计划的详细信息 如果提醒计划存在，则构建一个包含提醒计划及其关联子计划和配置的删除上下文对象，并执行删除操作
     *
     * @param param 包含要删除提醒计划ID的请求数据传输对象
     * @return 返回删除操作的成功与否
     */
    @ClusterRedisLock(prefix = "complaint_remind_plan_delete_", value = "#param.id")
    public boolean deleteById(ComplaintRemindPlanReqDTO.DelDTO param) {
        // 获取当前登录的管理员用户信息
        AdminUserDTO adminUser = AdminUserUtil.getAdminUser();
        Assert.isTrue(adminUser != null && StringUtils.isNotBlank(adminUser.getUserId()),
            USER_INFO_NOT_FOUND.getErrCode(), USER_INFO_NOT_FOUND.getErrDesc());

        // 根据提供的ID获取提醒计划的详细信息
        RemindPlanBO remindPlanBO = remindPlanDomainService.getRemindPlanAllInfo(param.getId());
        Assert.notNull(remindPlanBO, REMIND_PLAN_NOT_FOUND.getErrCode(), REMIND_PLAN_NOT_FOUND.getErrDesc());

        // 创建提醒计划删除上下文对象
        RemindPlanDeleteContextBO remindPlanDeleteContextBO = new RemindPlanDeleteContextBO();
        // 设置提醒计划ID
        remindPlanDeleteContextBO.setRemindPlanId(remindPlanBO.getId());
        // 设置提醒子计划ID列表
        remindPlanDeleteContextBO
            .setRemindSubPlanIds(remindPlanBO.getRemindSubPlans().stream().map(RemindSubPlanBO::getId).toList());
        // 设置提醒计划游戏配置ID列表
        remindPlanDeleteContextBO.setRemindPlanGameConfigIds(
            remindPlanBO.getAllRemindPlanGameConfigs().stream().map(RemindPlanGameConfigBO::getId).toList());
        // 设置提醒计划规则ID列表
        remindPlanDeleteContextBO
            .setRemindPlanRuleIds(remindPlanBO.getRemindPlanRules().stream().map(RemindPlanRuleBO::getId).toList());
        // 构建并设置删除操作的操作记录列表
        remindPlanDeleteContextBO.setOperateRecords(
            RemindPlanOperateRecordBuildUtil.buildDeleteAllOperateRecordList(remindPlanBO, adminUser));
        // 调用领域服务执行删除操作，并返回操作结果
        return remindPlanDomainService.delete(remindPlanDeleteContextBO);
    }

}
