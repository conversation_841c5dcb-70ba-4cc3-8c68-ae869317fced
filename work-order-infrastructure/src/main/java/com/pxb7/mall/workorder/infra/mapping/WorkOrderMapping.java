package com.pxb7.mall.workorder.infra.mapping;

import com.pxb7.mall.workorder.infra.model.RemindWorkOrderReqPO;
import com.pxb7.mall.workorder.infra.repository.db.entity.RemindWorkOrder;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2025/4/3 17:34
 */

@Mapper
public interface WorkOrderMapping {

    WorkOrderMapping INSTANCE = Mappers.getMapper(WorkOrderMapping.class);

    RemindWorkOrder addDto2PO(RemindWorkOrderReqPO.AddPO source);

}
