package com.pxb7.mall.workorder.domain.remind.extension;

import com.alibaba.fastjson2.JSON;
import com.pxb7.mall.common.client.enums.BusinessTypeEnum;
import com.pxb7.mall.common.client.enums.SystemFeishuIdTypeEnum;
import com.pxb7.mall.common.client.enums.message.FeishuMessageContentType;
import com.pxb7.mall.common.client.request.message.SysUserFeishuMsgReqDTO;
import com.pxb7.mall.workorder.client.constant.MessageTemplateConstant;
import com.pxb7.mall.workorder.client.enums.ProductSourceEnum;
import com.pxb7.mall.workorder.client.enums.RemindMethodEnum;
import com.pxb7.mall.workorder.client.enums.TimeoutStatusEnum;
import com.pxb7.mall.workorder.client.enums.WorkOrderStatusEnum;
import com.pxb7.mall.workorder.client.message.RemindPlanMessage;
import com.pxb7.mall.workorder.domain.model.BaseRemindRecordBO;
import com.pxb7.mall.workorder.domain.model.RemindDeliveryProductBO;
import com.pxb7.mall.workorder.domain.model.RemindWorkOrderBO;
import com.pxb7.mall.workorder.domain.model.*;
import com.pxb7.mall.workorder.domain.remind.extension.context.RemindBizContext;
import com.pxb7.mall.workorder.domain.service.RemindPlanDomainService;
import com.pxb7.mall.workorder.domain.service.RemindSubPlanDomainService;
import com.pxb7.mall.workorder.domain.service.RemindWorkOrderDomainService;
import com.pxb7.mall.workorder.infra.enums.ComplaintChannelEnum;
import com.pxb7.mall.workorder.infra.enums.ComplaintLevelEnum;
import com.pxb7.mall.workorder.infra.enums.RemindPlanWorkOrderTypeEnum;
import com.pxb7.mall.workorder.infra.model.SysUserRespPO;
import com.pxb7.mall.workorder.infra.repository.es.mapper.RemindAfterSaleDocEsRepository;
import com.pxb7.mall.workorder.infra.repository.es.mapper.RemindComplaintDocEsRepository;
import com.pxb7.mall.workorder.infra.repository.es.mapper.RemindWorkOrderDocEsRepository;
import com.pxb7.mall.workorder.infra.repository.gateway.dubbo.common.MessageGateway;
import com.pxb7.mall.workorder.infra.repository.gateway.dubbo.user.SysUserGateway;
import com.pxb7.mall.workorder.infra.util.TimeUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.pxb7.mall.workorder.client.constant.MessageTemplateConstant.*;

/**
 * <AUTHOR>
 * @date 2025/4/2 17:13
 */
@Slf4j
public abstract class BaseMessageService {

    @Resource
    private RemindWorkOrderDomainService workOrderDomainService;

    @Resource
    protected RemindPlanDomainService remindPlanDomainService;

    @Resource
    protected RemindSubPlanDomainService subPlanDomainService;

    @Resource
    private SysUserGateway sysUserGateway;

    @Resource
    private MessageGateway messageGateway;

    @Resource
    private RemindWorkOrderDocEsRepository workOrderDocEsRepository;

    @Resource
    private RemindAfterSaleDocEsRepository afterSaleDocEsRepository;

    @Resource
    private RemindComplaintDocEsRepository complaintDocEsRepository;

    /**
     * 获取催一催消息内容
     * @param context
     * @return
     */
    protected String getRemindMessageContent(RemindBizContext context) {
        BaseRemindRecordBO remindRecord = context.getRemindRecord();
        Long pastedMinutes = TimeUtils.pastMinutes(remindRecord.getCreateTime());
        Long totalMinutes = TimeUtils.lastMinutes(remindRecord.getCreateTime(), remindRecord.getExpectCompleteTime());
        //获取超时信息
        return getTimeOutInfo(totalMinutes, pastedMinutes, false,null) + "，请及时处理。";
    }

    /**
     * 获取账号交付消息内容
     * @param context
     * @return
     */
    protected String getDeliveryProductMessageContent(RemindBizContext context) {
        RemindDeliveryProductBO remindDeliveryProduct = (RemindDeliveryProductBO) context.getRemindRecord();
        RemindPlanMessage remindPlan = context.getRemindPlanMessage();
        //根据用户ID获取客服信息
        SysUserRespPO sysUserInfo =
                sysUserGateway.getSysUserInfo(remindDeliveryProduct.getDeliveryCustomerCare());
        if (Objects.isNull(sysUserInfo)) {
            log.error("Fail to get sys user info by userId. userId:{}", remindDeliveryProduct.getDeliveryCustomerCare());
            return "";
        }
        Long pastedMinutes = TimeUtils.pastMinutes(remindDeliveryProduct.getCreateTime());
        Long totalMinutes = TimeUtils.lastMinutes(remindDeliveryProduct.getCreateTime(), remindDeliveryProduct.getExpectCompleteTime());
        //交付时长
        String durationInfo = TimeUtils.formatDuration(pastedMinutes);
        //获取超时信息
        // String timeOutInfo = getTimeOutInfo(totalMinutes, pastedMinutes, true);
        //超时时间直接取配置的时间
        String timeOutInfo = getTimeOutInfo(totalMinutes, pastedMinutes, true, context.getTimeConfigBO());
        return String.format(FEISHU_DELIVERY_PRODUCT_REMIND_TEMPLATE,
                context.getRemindPlanGameConfig().getGameName(),
                remindDeliveryProduct.getProductCode(),
                remindDeliveryProduct.getOrderId(),
                remindDeliveryProduct.getGroupId(),
                sysUserInfo.getUserName(),
                durationInfo,
                timeOutInfo,
                remindPlan.getWhichTime());
    }

    /**
     * 获取超时信息
     *
     * @param totalMinutes
     * @param pastedMinutes
     * @param emphasize
     * @return
     */
    private static String getTimeOutInfo(Long totalMinutes, Long pastedMinutes, boolean emphasize, TimeConfigBO timeConfig) {
        String timeOutInfo;
        //未超时
        if (totalMinutes > pastedMinutes) {
            String prefix = emphasize ? "要求<font color=red>" : "要求";
            String suffix = emphasize ? "</font>后完结" : "后完结";
            if (timeConfig != null) {
                timeOutInfo = prefix + timeConfig.getHours() + "小时" + timeConfig.getMinutes() + "分钟" + suffix;
            } else {
                timeOutInfo = prefix + TimeUtils.formatDuration(totalMinutes - pastedMinutes) + suffix;
            }
        } else {
            String prefix = emphasize ? "已超时<font color=red>" : "已超时";
            String suffix = emphasize ? "</font>" : "";
            timeOutInfo = prefix + TimeUtils.formatDuration(pastedMinutes - totalMinutes) + suffix;
        }
        return timeOutInfo;
    }

    /**
     * 获取商品工单消息内容
     * @param context
     * @param remindMethod
     * @return
     */
    protected String getWorkOrderMessageContent(RemindBizContext context, RemindMethodEnum remindMethod) {
        RemindWorkOrderBO remindWorkOrder = (RemindWorkOrderBO) context.getRemindRecord();
        RemindPlanMessage remindPlanMessage = context.getRemindPlanMessage();
        String template = remindMethod == RemindMethodEnum.FEISHU ?
                FEISHU_WORK_ORDER_REMIND_TEMPLATE : WORK_ORDER_REMIND_TEMPLATE;
        //查询系统用户信息
        Map<String, SysUserRespPO> sysUserIdNameMap = getSysUserIdNameMap(remindWorkOrder);
        SysUserRespPO follower = sysUserIdNameMap.get(remindWorkOrder.getFollowerId());
        SysUserRespPO auditUser = sysUserIdNameMap.get(remindWorkOrder.getAuditUserId());
        SysUserRespPO artDesigner = sysUserIdNameMap.get(remindWorkOrder.getArtDesignerId());
        //工单状态
        WorkOrderStatusEnum workOrderStatus =
                WorkOrderStatusEnum.getByCode(remindWorkOrder.getWorkOrderStatus());

        //上架方式：1:官方截图，2:自主截图，服务类型为商品工单且 不为“待接单”时有值
        String onShelfType = workOrderDomainService.getOnShelfType(remindWorkOrder.getWorkOrderId());
        if (Objects.isNull(onShelfType)) {
            return "";
        }
        Long pastedMinutes = TimeUtils.pastMinutes(Objects.isNull(remindWorkOrder.getFollowedUpInflowTime()) ?
                remindWorkOrder.getCreateTime() : remindWorkOrder.getFollowedUpInflowTime());
        Long totalMinutes = TimeUtils.lastMinutes(Objects.isNull(remindWorkOrder.getFollowedUpInflowTime()) ?
                remindWorkOrder.getCreateTime() : remindWorkOrder.getFollowedUpInflowTime(), remindWorkOrder.getExpectCompleteTime());
        //交付时长
        String durationInfo = TimeUtils.formatDuration(pastedMinutes);
        //获取超时信息
        String timeOutInfo = getTimeOutInfo(totalMinutes, pastedMinutes, true,null);
        return String.format(template,
                context.getRemindPlanGameConfig().getGameName(),
                remindWorkOrder.getProductCode(),
                remindWorkOrder.getWorkOrderId(),
                Objects.isNull(workOrderStatus) ? "" : workOrderStatus.getDesc(),
                onShelfType,
                Objects.isNull(follower) ? "" : follower.getUserName() + "(" + follower.getExternalName() + ")",
                Objects.isNull(auditUser) ? "" : auditUser.getUserName() + "(" + auditUser.getExternalName() + ")",
                Objects.isNull(artDesigner) ? "" : artDesigner.getUserName() + "(" + artDesigner.getExternalName() + ")",
                durationInfo,
                timeOutInfo,
                remindPlanMessage.getWhichTime());
    }

    /**
     * 查询系统用户信息
     * @param remindWorkOrder
     * @return
     */
    private Map<String, SysUserRespPO> getSysUserIdNameMap(RemindWorkOrderBO remindWorkOrder) {
        //根据用户ID获取客服信息
        Set<String> userIds = new HashSet<>();
        if (StringUtils.isNotBlank(remindWorkOrder.getFollowerId())) {
            userIds.add(remindWorkOrder.getFollowerId());
        }
        if (StringUtils.isNotBlank(remindWorkOrder.getAuditUserId())) {
            userIds.add(remindWorkOrder.getAuditUserId());
        }
        if (StringUtils.isNotBlank(remindWorkOrder.getArtDesignerId())) {
            userIds.add(remindWorkOrder.getArtDesignerId());
        }
        List<SysUserRespPO> sysUserInfoList = sysUserGateway.getSysUserInfoList(userIds);
        return sysUserInfoList.stream()
                .collect(Collectors.toMap(SysUserRespPO::getUserId, Function.identity(), (a, b) -> a));
    }

    /**
     * 根据用户名获取用户ID
     * @param userNameStr
     * @return
     */
    protected String getUserIdsByUserNames(String userNameStr) {
        if (StringUtils.isBlank(userNameStr)) {
            return "";
        }
        List<String> userIds = new ArrayList<>();
        String[] userNames = userNameStr.split(",");
        for (String userName : userNames) {
            if (StringUtils.isBlank(userName)) {
                continue;
            }
            SysUserRespPO sysUserInfo = sysUserGateway.getSysUserByUserName(userName);
            if (Objects.isNull(sysUserInfo)) {
                continue;
            }
            userIds.add(sysUserInfo.getUserId());
        }
        String userIdStr = String.join(",", userIds);
        log.info("getUserIdsByUserNames,userNameStr:{},userIds:{}", userNameStr, userIdStr);
        return userIdStr;
    }


    /**
     * 获取商品工单后台弹窗消息
     * @param context
     * @return
     */
    protected String getToastMessageContent(RemindBizContext context) {
        BaseRemindRecordBO remindRecord = context.getRemindRecord();
        //获取超时信息
        LocalDateTime startTime = remindRecord.getCreateTime();
        if (remindRecord instanceof RemindWorkOrderBO remindWorkOrder) {
            startTime = Objects.isNull(remindWorkOrder.getFollowedUpInflowTime()) ?
                    remindWorkOrder.getCreateTime() : remindWorkOrder.getFollowedUpInflowTime();
        }
        Long pastedMinutes = TimeUtils.pastMinutes(startTime);
        Long totalMinutes = TimeUtils.lastMinutes(startTime, remindRecord.getExpectCompleteTime());
        String timeOutInfo = getTimeOutInfo(totalMinutes, pastedMinutes, true,null);

        String toastTemplate;
        Long willTimeOutNumber;
        Long timeOutNumber;
        if (remindRecord instanceof RemindWorkOrderBO) {
            toastTemplate = MessageTemplateConstant.WORK_ORDER_REMIND_TOAST_TEMPLATE;
            willTimeOutNumber = workOrderDocEsRepository.countWorkOrder(TimeoutStatusEnum.WILL_TIMEOUT);
            timeOutNumber = workOrderDocEsRepository.countWorkOrder(TimeoutStatusEnum.TIMEOUT);
        } else if (remindRecord instanceof RemindAfterSaleBO remindAfterSaleBO) {
            toastTemplate = MessageTemplateConstant.AFTER_SALE_RETRIEVE_REMIND_TOAST_TEMPLATE;
            willTimeOutNumber = afterSaleDocEsRepository.countWorkOrder(TimeoutStatusEnum.WILL_TIMEOUT, remindAfterSaleBO.getWorkOrderType());
            timeOutNumber = afterSaleDocEsRepository.countWorkOrder(TimeoutStatusEnum.TIMEOUT, remindAfterSaleBO.getWorkOrderType());
        } else if (remindRecord instanceof RemindComplaintBO) {
            toastTemplate = MessageTemplateConstant.COMPLAINT_REMIND_TOAST_TEMPLATE;
            willTimeOutNumber = complaintDocEsRepository.countWorkOrder(TimeoutStatusEnum.WILL_TIMEOUT);
            timeOutNumber = complaintDocEsRepository.countWorkOrder(TimeoutStatusEnum.TIMEOUT);
        } else {
            // 添加默认处理分支，防止未来类型扩展导致逻辑静默失败
            throw new IllegalArgumentException("Unsupported remindRecord type: " + remindRecord.getClass().getName());
        }

        return String.format(toastTemplate,
                timeOutInfo,
                willTimeOutNumber,
                timeOutNumber);
    }

    /**
     * 获取售后工单消息内容
     * @param context
     * @return
     */
    protected String getAfterSaleMessageContent(RemindBizContext context,
                                                RemindSubPlanRespBO.DetailBO remindSubPlan,
                                                RemindMethodEnum remindMethod) {
        RemindAfterSaleBO remindAfterSale = (RemindAfterSaleBO) context.getRemindRecord();
        RemindPlanMessage remindPlanMessage = context.getRemindPlanMessage();

        String template;
        String userId;
        if (RemindPlanWorkOrderTypeEnum.RETRIEVE.eq(remindSubPlan.getWorkOrderType())) {
            template = remindMethod == RemindMethodEnum.FEISHU ?
                    FEISHU_AFTER_SALE_RETRIEVE_REMIND_TEMPLATE : AFTER_SALE_RETRIEVE_REMIND_TEMPLATE;
            userId = remindAfterSale.getRetrieveUserId();
        } else {
            template = remindMethod == RemindMethodEnum.FEISHU ?
                    FEISHU_AFTER_SALE_DISPUTE_REMIND_TEMPLATE : AFTER_SALE_DISPUTE_REMIND_TEMPLATE;
            userId = remindAfterSale.getDisputeUserId();
        }
        //根据用户ID获取客服信息
        SysUserRespPO sysUserInfo = sysUserGateway.getSysUserInfo(userId);
        if (Objects.isNull(sysUserInfo)) {
            log.error("Fail to get sys user info by userId. userId:{}", remindAfterSale.getRetrieveUserId());
            return "";
        }

        Long pastedMinutes = TimeUtils.pastMinutes(remindAfterSale.getCreateTime());
        Long totalMinutes = TimeUtils.lastMinutes(remindAfterSale.getCreateTime(), remindAfterSale.getExpectCompleteTime());
        //交付时长
        String durationInfo = TimeUtils.formatDuration(pastedMinutes);
        //获取超时信息
        String timeOutInfo = getTimeOutInfo(totalMinutes, pastedMinutes, true,null);

        if (RemindPlanWorkOrderTypeEnum.RETRIEVE.eq(remindSubPlan.getWorkOrderType())) {
            ProductSourceEnum productSource = ProductSourceEnum.getByCode(remindSubPlan.getMembership());
            return String.format(template,
                    context.getRemindPlanGameConfig().getGameName(),
                    remindAfterSale.getWorkOrderNo(),
                    remindAfterSale.getOrderItemId(),
                    remindAfterSale.getProductCode(),
                    sysUserInfo.getUserName() + "(" + sysUserInfo.getExternalName() + ")",
                    Objects.nonNull(productSource) ? productSource.getDesc() : "",
                    durationInfo,
                    timeOutInfo,
                    remindPlanMessage.getWhichTime());
        } else {
            return String.format(template,
                    context.getRemindPlanGameConfig().getGameName(),
                    remindAfterSale.getWorkOrderNo(),
                    remindAfterSale.getOrderItemId(),
                    remindAfterSale.getProductCode(),
                    sysUserInfo.getUserName() + "(" + sysUserInfo.getExternalName() + ")",
                    durationInfo,
                    timeOutInfo,
                    remindPlanMessage.getWhichTime());
        }
    }

    /**
     * 获取投诉工单消息内容
     * @param context
     * @return
     */
    protected String getComplaintMessageContent(RemindBizContext context,
                                                RemindSubPlanRespBO.DetailBO remindSubPlan,
                                                RemindMethodEnum remindMethod) {
        RemindComplaintBO remindComplaint = (RemindComplaintBO) context.getRemindRecord();
        RemindPlanMessage remindPlanMessage = context.getRemindPlanMessage();

        String template = remindMethod == RemindMethodEnum.FEISHU ?
                FEISHU_COMPLAINT_REMIND_TEMPLATE : COMPLAINT_REMIND_TEMPLATE;
        //根据用户ID获取客服信息
        SysUserRespPO sysUserInfo = sysUserGateway.getSysUserInfo(remindComplaint.getHandleUserId());
        if (Objects.isNull(sysUserInfo)) {
            log.error("Fail to get sys user info by userId. userId:{}", remindComplaint.getHandleUserId());
            return "";
        }

        ComplaintLevelEnum complaintLevel = ComplaintLevelEnum.getByCode(remindSubPlan.getComplaintLevel());
        ComplaintChannelEnum complaintChannel = ComplaintChannelEnum.getEnum(context.getRemindPlanGameConfig().getChannel());

        Long pastedMinutes = TimeUtils.pastMinutes(remindComplaint.getCreateTime());
        Long totalMinutes = TimeUtils.lastMinutes(remindComplaint.getCreateTime(), remindComplaint.getExpectCompleteTime());
        //交付时长
        String durationInfo = TimeUtils.formatDuration(pastedMinutes);
        //获取超时信息
        String timeOutInfo = getTimeOutInfo(totalMinutes, pastedMinutes, true,null);

        return String.format(template,
                remindComplaint.getWorkOrderTitle(),
                remindComplaint.getWorkOrderId(),
                Objects.nonNull(complaintLevel) ? complaintLevel.getDesc() : "",
                sysUserInfo.getUserName() + "(" + sysUserInfo.getExternalName() + ")",
                Objects.nonNull(complaintChannel) ? complaintChannel.getLabel() : "",
                remindComplaint.getComplaintContent(),
                durationInfo,
                timeOutInfo,
                remindPlanMessage.getWhichTime());
    }

    /**
     * 发送飞书群消息
     * @param title
     * @param content
     * @param targetId
     * @param businessId
     */
    protected void sengFeishuGroupMessage(String title, String content, String targetId, String businessId) {
        SysUserFeishuMsgReqDTO msgReqDTO = new SysUserFeishuMsgReqDTO();
        msgReqDTO.setTitle(title);
        msgReqDTO.setContent(content);
        msgReqDTO.setTargetId(targetId);
        msgReqDTO.setTargetType(SystemFeishuIdTypeEnum.CHAT.getCode());
        msgReqDTO.setContentType(FeishuMessageContentType.CARD.getType());
        msgReqDTO.setBusinessType(BusinessTypeEnum.SERVICE_TIME_WARNING.getCode());
        msgReqDTO.setBusinessId(businessId);
        String messageId = messageGateway.sendFeishuMessage(msgReqDTO);
        log.info("Send message to the systemUserMessageService#sendFeishuMessage,msgReqDTO:{},messageId:{}",
                JSON.toJSONString(msgReqDTO), messageId);
    }

    /**
     * 发送飞书私聊消息
     * @param title
     * @param content
     * @param systemUserId
     * @param businessId
     */
    protected void sendFeishuMessage(String title, String content, String systemUserId, String businessId) {
        SysUserFeishuMsgReqDTO msgReqDTO = new SysUserFeishuMsgReqDTO();
        msgReqDTO.setTitle(title);
        msgReqDTO.setContent(content);
        msgReqDTO.setTargetId(systemUserId);
        msgReqDTO.setTargetType(SystemFeishuIdTypeEnum.SYSTEM_USER.getCode());
        msgReqDTO.setContentType(FeishuMessageContentType.CARD.getType());
        msgReqDTO.setBusinessType(BusinessTypeEnum.SERVICE_TIME_WARNING.getCode());
        msgReqDTO.setBusinessId(businessId + systemUserId);
        String messageId = messageGateway.sendFeishuMessage(msgReqDTO);
        log.info("Send message to the systemUserMessageService#sendFeishuMessage,msgReqDTO:{},messageId:{}",
                JSON.toJSONString(msgReqDTO), messageId);
    }

}
