package com.pxb7.mall.workorder.domain.service;


import com.alibaba.cola.exception.Assert;
import com.alibaba.cola.exception.BizException;
import com.alibaba.excel.util.BooleanUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pxb7.mall.product.client.dto.response.product.WorkOrderRpcResp;
import com.pxb7.mall.workorder.client.enums.*;
import com.pxb7.mall.workorder.client.message.RemindPlanMessage;
import com.pxb7.mall.workorder.domain.mapping.RemindWorkOrderDomainMapping;
import com.pxb7.mall.workorder.domain.model.*;
import com.pxb7.mall.workorder.domain.remind.task.RemindMessageTask;
import com.pxb7.mall.workorder.infra.aop.ClusterRedisLock;
import com.pxb7.mall.workorder.infra.constant.CommonConstants;
import com.pxb7.mall.workorder.infra.enums.LocalOrRedisCacheEnum;
import com.pxb7.mall.workorder.infra.enums.RemindPlanOnShelfTypeEnum;
import com.pxb7.mall.workorder.infra.model.ProductForewarningStatictisDataPO;
import com.pxb7.mall.workorder.infra.model.ProductForewarningStatictisSearchPO;
import com.pxb7.mall.workorder.infra.model.RemindWorkOrderReqPO;
import com.pxb7.mall.workorder.infra.repository.db.RemindSubPlanRepository;
import com.pxb7.mall.workorder.infra.repository.db.RemindWorkOrderRepository;
import com.pxb7.mall.workorder.infra.repository.db.entity.RemindSubPlan;
import com.pxb7.mall.workorder.infra.repository.db.entity.RemindWorkOrder;
import com.pxb7.mall.workorder.infra.repository.db.mapper.AdsPxb7ProductForewarningMapper;
import com.pxb7.mall.workorder.infra.repository.es.entity.RemindWorkOrderDoc;
import com.pxb7.mall.workorder.infra.repository.es.mapper.RemindWorkOrderDocEsRepository;
import com.pxb7.mall.workorder.infra.repository.es.mapper.RemindWorkOrderDocRepository;
import com.pxb7.mall.workorder.infra.repository.gateway.dubbo.product.ProductGateway;
import com.pxb7.mall.workorder.infra.util.IdGenUtil;
import com.pxb7.mall.workorder.infra.util.TimeUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchPage;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 商品工单预警记录domain服务
 *
 * <AUTHOR>
 * @since 2025-04-07 11:59:34
 */
@Slf4j
@Service
public class RemindWorkOrderDomainService extends BaseRemindDomainService {

    @Resource
    private RemindWorkOrderRepository remindWorkOrderRepository;

    @Resource
    private RemindWorkOrderDocRepository remindWorkOrderDocRepository;

    @Resource
    private RemindWorkOrderDocEsRepository remindWorkOrderDocEsRepository;

    @Resource
    private AdsPxb7ProductForewarningMapper adsPxb7ProductForewarningMapper;

    @Resource
    private RemindMessageTask remindMessageTask;

    @Resource
    private ProductGateway productGateway;

    @Resource
    private RemindSubPlanRepository remindSubPlanRepository;

    @Override
    public BizTypeEnum getBizType() {
        return BizTypeEnum.WORK_ORDER;
    }

    public Page<RemindWorkOrderBO> pageEs(RemindWorkOrderReqBO.PageBO param) {
        RemindWorkOrderReqPO.PagePO pagePO = RemindWorkOrderDomainMapping.INSTANCE.remindWorkOrderBO2PagePO(param);
        SearchPage<RemindWorkOrderDoc> searchHits = remindWorkOrderDocEsRepository.pageQueryWorkOrder(pagePO);
        if (searchHits == null){
            return new Page<>(param.getPageIndex(), param.getPageSize());
        }
        if (CollectionUtils.isEmpty(searchHits.getContent())) {
            return new Page<>(searchHits.getNumber(), searchHits.getSize(), searchHits.getTotalElements());
        }
        List<SearchHit<RemindWorkOrderDoc>> content = searchHits.getContent();
        List<RemindWorkOrderDoc> list = content.stream().map(SearchHit::getContent).toList();
        List<RemindWorkOrderBO> remindWorkOrderBOS = RemindWorkOrderDomainMapping.INSTANCE.remindWorkOrderDoc2ListBO(list);
        Page<RemindWorkOrderBO> remindWorkOrderBOPage = new Page<>(searchHits.getNumber(), searchHits.getSize(), searchHits.getTotalElements());
        remindWorkOrderBOPage.setRecords(remindWorkOrderBOS);
        return remindWorkOrderBOPage;
    }

    public void deleteRemindWorkOrderEs(List<String> remindIds) {
        if (CollectionUtils.isEmpty(remindIds)) {
            return;
        }
        remindWorkOrderDocRepository.deleteAllById(remindIds);
    }


    @ClusterRedisLock(prefix = "work_order_data_sync", value = "#remindId")
    public void syncRemindWorkOrderToEs(String remindId) {
        RemindWorkOrder remindWorkOrder = remindWorkOrderRepository.getByRemindId(remindId);
        if (Objects.isNull(remindWorkOrder)
                || BooleanUtils.isTrue(remindWorkOrder.getDeleted())) {
            remindWorkOrderDocRepository.deleteById(remindId);
        } else {
            RemindWorkOrderDoc remindWorkOrderDoc = RemindWorkOrderDomainMapping.INSTANCE.remindWorkOrderBO2Doc(remindWorkOrder);
            if (null != remindWorkOrderDoc) {
                Long remindSubPlanId = remindWorkOrder.getRemindSubPlanId();
                RemindSubPlan remindSubPlan = remindSubPlanRepository.findById(remindSubPlanId);
                remindWorkOrderDoc.setOnShelfType(remindSubPlan == null ? null : remindSubPlan.getOnShelfType());
                remindWorkOrderDocRepository.save(remindWorkOrderDoc);
            }
        }
    }

    /**
     * 生成商品工单预警记录
     * @param workOrderInfo
     * @param remindPlanGameConfig
     * @return
     */
    public RemindWorkOrderReqBO.AddBO generateRemindWorkOrder(
            WorkOrderRpcResp workOrderInfo, RemindPlanGameConfigBO remindPlanGameConfig, Integer workOrderStatus) {
        //待跟进流入时间
        LocalDateTime followedUpInflowTime = getFollowedUpInflowTime(workOrderInfo, workOrderStatus);
        // 获取开始时间
        LocalDateTime startDateTime = Objects.isNull(followedUpInflowTime) ? LocalDateTime.now() : followedUpInflowTime;
        //预期完结时间
        TimeConfigBO expectCompleteTimeConfig = remindPlanGameConfig.getExpectCompleteTimeConfig();
        LocalDateTime expectCompleteTime = TimeUtils.addSpecificTime(startDateTime,
                expectCompleteTimeConfig.getHours(), expectCompleteTimeConfig.getMinutes(), 0);
        //插入商品工单预警记录
        RemindWorkOrderReqBO.AddBO addBO = new RemindWorkOrderReqBO.AddBO()
                .setRemindId("WO" + IdGenUtil.getShardingColumnId(workOrderInfo.getWorkOrderId()))
                .setWorkOrderId(workOrderInfo.getWorkOrderId())
                .setWorkOrderStatus(workOrderStatus)
                .setProductId(workOrderInfo.getProductId())
                .setProductCode(workOrderInfo.getUniqueNo())
                .setGameId(workOrderInfo.getGameId())
                .setCompleteStatus(CompleteStatusEnum.IN_PROCESS.getCode())
                .setTimeOutStatus(TimeoutStatusEnum.NONE.getCode())
                .setArtDesignerId(workOrderInfo.getAdminIdMeigong())
                .setFollowerId(workOrderInfo.getFollowedUpPersonId())
                .setAuditUserId(workOrderInfo.getAdminIdKefu())
                .setFollowedUpInflowTime(followedUpInflowTime)
                .setExpectCompleteTime(expectCompleteTime)
                .setGameConfigId(remindPlanGameConfig.getId())
                .setRemindPlanId(remindPlanGameConfig.getRemindPlanId())
                .setRemindSubPlanId(remindPlanGameConfig.getRemindSubPlanId())
                .setCreateUserId(CommonConstants.SYS_DEFAULT_USER)
                .setUpdateUserId(CommonConstants.SYS_DEFAULT_USER);

        RemindWorkOrderReqPO.AddPO addPO = RemindWorkOrderDomainMapping.INSTANCE.remindWorkOrderBO2AddPO(addBO);
        boolean result = remindWorkOrderRepository.insert(addPO);
        Assert.isTrue(result,
                BizErrorCodeEnum.REMIND_RECORD_SAVE_ERROR.getErrCode(), BizErrorCodeEnum.REMIND_RECORD_SAVE_ERROR.getErrDesc());
        return addBO;
    }

    /**
     * 获取待跟进流入时间
     * @param workOrderInfo
     * @param workOrderStatus
     * @return
     */
    private LocalDateTime getFollowedUpInflowTime(WorkOrderRpcResp workOrderInfo, Integer workOrderStatus) {
        LocalDateTime followedUpInflowTime = null;
        if (Objects.equals(WorkOrderStatusEnum.TO_BE_FOLLOWED.getCode(), workOrderStatus)
                && Objects.nonNull(workOrderInfo.getFollowedUpInflowTime())) {
            //是否存在待跟进状态工单
            boolean existFollowUpStatusWorkOrder = remindWorkOrderRepository.existFollowUpStatusWorkOrder(workOrderInfo.getWorkOrderId());
            if (!existFollowUpStatusWorkOrder) {
                followedUpInflowTime = workOrderInfo.getFollowedUpInflowTime();
            }
        }
        return followedUpInflowTime;
    }

    public boolean deleteByWorkOrderId(String workOrderId) {
        return remindWorkOrderRepository.deleteByWorkOrderId(workOrderId);
    }

    public boolean updateByWorkOrderId(RemindWorkOrderReqBO.UpdateBO param) {
        RemindWorkOrderReqPO.UpdatePO updatePO = RemindWorkOrderDomainMapping.INSTANCE.remindWorkOrderBO2UpdatePO(param);
        return remindWorkOrderRepository.updateByWorkOrderId(updatePO);
    }

    /**
     * 根据workOrderId查询商品工单预警记录
     * @param workOrderId
     * @param remindId
     * @return
     */
    public RemindWorkOrderRespBO.DetailBO findByWorkOrderId(String workOrderId, String remindId) {
        RemindWorkOrder entity = remindWorkOrderRepository.findByWorkOrderId(workOrderId, remindId);
        return RemindWorkOrderDomainMapping.INSTANCE.remindWorkOrderPO2DetailBO(entity);
    }

    /**
     * 更新商品工单预警记录超时状态
     * @param remindPlanMessage
     */
    @Override
    public void updateWillTimeOutStatus(RemindPlanMessage remindPlanMessage) {
        RemindWorkOrderRespBO.DetailBO remindWorkOrder =
                findByWorkOrderId(remindPlanMessage.getBizId(), remindPlanMessage.getRemindId());
        if (Objects.isNull(remindWorkOrder)
                || !Objects.equals(CompleteStatusEnum.IN_PROCESS.getCode(), remindWorkOrder.getCompleteStatus())) {
            return;
        }
        //更新账号交付预警记录状态
        RemindWorkOrderReqBO.UpdateBO updateBO = new RemindWorkOrderReqBO.UpdateBO();
        updateBO.setWorkOrderId(remindPlanMessage.getBizId());
        updateBO.setRemindId(remindPlanMessage.getRemindId());
        updateBO.setTimeOutStatus(TimeoutStatusEnum.WILL_TIMEOUT.getCode());
        this.updateByWorkOrderId(updateBO);
    }

    /**
     * 执行商品工单超时状态更新任务
     */
    @Override
    public void executeUpdateTimeoutStatusTask(String databaseName) {
        log.info("执行商品工单超时状态更新任务，任务开始，databaseName:{}", databaseName);
        //提前时间
        LocalDateTime nowDateTime = LocalDateTime.now();

        //分页查询符合条件的提醒计划记录
        //游标id
        long minId = 0;
        long pageSize = CommonConstants.QUERY_PAGE_SIZE;
        while (true) {
            RemindWorkOrderReqBO.PageBO param = new RemindWorkOrderReqBO.PageBO();
            param.setId(minId);
            param.setCompleteStatus(CompleteStatusEnum.IN_PROCESS.getCode());
            param.setTimeOutStatus(TimeoutStatusEnum.TIMEOUT.getCode());
            param.setExpectCompleteTime(nowDateTime);
            param.setPageIndex(1);
            param.setPageSize(pageSize);

            Page<RemindWorkOrderRespBO.DetailBO> page = pageDb(param);
            List<RemindWorkOrderRespBO.DetailBO> pageRecords = page.getRecords();
            if (CollectionUtils.isEmpty(pageRecords)) {
                break;
            }
            minId = pageRecords.get(pageRecords.size() - 1).getId();
            //更新状态为过期
            List<Long> ids = pageRecords.stream().map(RemindWorkOrderRespBO.DetailBO::getId).toList();
            batchUpdateTimeoutStatusByIds(ids, TimeoutStatusEnum.TIMEOUT);
            log.info("执行商品工单超时状态更新任务，databaseName:{}，page:{}，page size:{}", databaseName, page.getCurrent(), page.getSize());
        }
        log.info("执行商品工单超时状态更新任务，任务结束，databaseName:{}", databaseName);
    }

    /**
     * 分页查询
     * @param param
     * @return
     */
    private Page<RemindWorkOrderRespBO.DetailBO> pageDb(RemindWorkOrderReqBO.PageBO param) {
        RemindWorkOrderReqPO.PagePO pagePO =
                RemindWorkOrderDomainMapping.INSTANCE.remindWorkOrderBO2PagePO(param);
        Page<RemindWorkOrder> page = remindWorkOrderRepository.page(pagePO);
        return RemindWorkOrderDomainMapping.INSTANCE.remindWorkOrderPO2PageBO(page);
    }

    /**
     * 批量更新账号交付超时状态
     * @param ids
     * @param status
     * @return
     */
    private Boolean batchUpdateTimeoutStatusByIds(List<Long> ids, TimeoutStatusEnum status) {
        if (CollectionUtils.isEmpty(ids)) {
            return Boolean.FALSE;
        }
        return remindWorkOrderRepository.batchUpdateTimeoutStatusByIds(ids, status.getCode());
    }

    /**
     * 处理提醒消息发送
     * @param remindPlanMessage
     */
    @Override
    public PlanExecuteStatusEnum handleRemindMessage(RemindPlanMessage remindPlanMessage) {
        //根据workOrderId查询商品工单预警记录
        RemindWorkOrderRespBO.DetailBO remindWorkOrder =
                findByWorkOrderId(remindPlanMessage.getBizId(), remindPlanMessage.getRemindId());
        if (Objects.isNull(remindWorkOrder)) {
            log.warn("根据workOrderId查询商品工单预警记录为空，workOrderId:{}", remindPlanMessage.getBizId());
            return PlanExecuteStatusEnum.FAILED;
        }
        //完结状态,1:未完结,2:已完结,3:终止
        Integer completeStatus = remindWorkOrder.getCompleteStatus();
        if (!Objects.equals(CompleteStatusEnum.IN_PROCESS.getCode(), completeStatus)) {
            return PlanExecuteStatusEnum.EXPIRED;
        }
        //处理提醒消息发送
        RemindWorkOrderBO remindRecord = new RemindWorkOrderBO();
        BeanUtils.copyProperties(remindWorkOrder, remindRecord);
        Boolean result = remindMessageTask.handleRemindMessage(remindPlanMessage, remindRecord);
        return Objects.equals(result, Boolean.TRUE)
                ? PlanExecuteStatusEnum.DONE : PlanExecuteStatusEnum.FAILED;
    }

    /**
     * 通过房间查询预警记录
     * @param groupId
     * @param customerCareId
     * @return
     */
    @Override
    public RemindWorkOrderBO searchByGroupId(String groupId, String customerCareId) {
        return null;
    }


    /**
     * 获取上架方式
     * @param workOrderId
     * @return
     */
    @Cacheable(value = LocalOrRedisCacheEnum.Constants.WORK_ORDER_ON_SHELF_TYPE,
            key = "#p0", sync = true, cacheManager = "localCacheManager")
    public String getOnShelfType(String workOrderId) {
        WorkOrderRpcResp workOrderInfo =
                productGateway.getWorkOrderInfo(workOrderId);
        if (Objects.isNull(workOrderInfo)) {
            return null;
        }
        RemindPlanOnShelfTypeEnum onShelfTypeEnum = RemindPlanOnShelfTypeEnum.getEnum(workOrderInfo.getScreen());
        if (Objects.isNull(onShelfTypeEnum)) {
            return null;
        }
        return onShelfTypeEnum.getLabel();
    }

    public List<WorkOrderStatisticDataBO> getStatisticList(WorkOrderStatisticDataSearchBO searchBO) {

        Assert.notNull(searchBO.getWorkOrderStatus(), "工单状态不能为空");
        ProductForewarningStatictisSearchPO param = new ProductForewarningStatictisSearchPO();
        param.setStartDate(searchBO.getStartDate());
        param.setEndDate(searchBO.getEndDate());
        param.setWorkOrderStatus(searchBO.getWorkOrderStatus());
        param.setGameIds(searchBO.getGameIds());
        param.setOnShelfType(searchBO.getOnShelfType());
        param.setCustomerCareIds(searchBO.getCustomerCareIds());
        param.setArtDesignerIds(searchBO.getArtDesignerIds());
        param.setFollowerIds(searchBO.getFollowerIds());

        List<ProductForewarningStatictisDataPO> productForewarningStatictisDataPOS =
            adsPxb7ProductForewarningMapper.countBySearchParam(param);

        List<WorkOrderStatisticDataBO> statisticDataBOS = new ArrayList<>();

        for (ProductForewarningStatictisDataPO statictisDataPO : productForewarningStatictisDataPOS) {
            WorkOrderStatisticDataBO workOrderStatisticDataBO = new WorkOrderStatisticDataBO();
            workOrderStatisticDataBO.setDate(statictisDataPO.getCreateDate());
            workOrderStatisticDataBO.setGameId(statictisDataPO.getGameId());
            workOrderStatisticDataBO.setOnShelfType(statictisDataPO.getOnShelfType());
            workOrderStatisticDataBO.setCustomerCareId(statictisDataPO.getAuditUserId());
            workOrderStatisticDataBO.setFollowerId(statictisDataPO.getFollowerId());
            workOrderStatisticDataBO.setArtDesignerId(statictisDataPO.getArtDesignerId());
            //工单状态,1:待接单,2:已接单,3:待跟进
            if (1 == searchBO.getWorkOrderStatus()) {
                workOrderStatisticDataBO.setProcessTotalCount(statictisDataPO.getWaitWorkOrderCnt());
                workOrderStatisticDataBO.setCompleteCount(statictisDataPO.getWaitCompletedWorkOrderCnt());
                workOrderStatisticDataBO.setInvalidCount(0L);
                workOrderStatisticDataBO.setWillTimeoutCount(statictisDataPO.getWaitTimeoutingWorkOrderCnt());
                workOrderStatisticDataBO.setTimeoutCount(statictisDataPO.getWaitTimeoutedWorkOrderCnt());
            } else if (2 == searchBO.getWorkOrderStatus()) {
                workOrderStatisticDataBO.setProcessTotalCount(statictisDataPO.getAcceptedWorkOrderCnt());
                workOrderStatisticDataBO.setCompleteCount(statictisDataPO.getAcceptedCompletedWorkOrderCnt());
                workOrderStatisticDataBO.setInvalidCount(statictisDataPO.getAcceptedTerminatedWorkOrderCnt());
                workOrderStatisticDataBO.setWillTimeoutCount(statictisDataPO.getAcceptedTimeoutingWorkOrderCnt());
                workOrderStatisticDataBO.setTimeoutCount(statictisDataPO.getAcceptedTimeoutedWorkOrderCnt());
            } else if (3 == searchBO.getWorkOrderStatus()) {
                workOrderStatisticDataBO.setProcessTotalCount(statictisDataPO.getFollowedWorkOrderCnt());
                workOrderStatisticDataBO.setCompleteCount(statictisDataPO.getFollowedCompletedWorkOrderCnt());
                workOrderStatisticDataBO.setInvalidCount(statictisDataPO.getFollowedTerminatedWorkOrderCnt());
                workOrderStatisticDataBO.setWillTimeoutCount(statictisDataPO.getFollowedTimeoutingWorkOrderCnt());
                workOrderStatisticDataBO.setTimeoutCount(statictisDataPO.getFollowedTimeoutedWorkOrderCnt());
            } else {
                throw new BizException("工单状态不在允许范围内");
            }
            statisticDataBOS.add(workOrderStatisticDataBO);
        }
        return statisticDataBOS;
    }
}

