package com.pxb7.mall.workorder.app.model;

import java.util.List;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 提醒服务预警计划(RemindPlan)实体类
 *
 * <AUTHOR>
 * @since 2025-03-24 21:12:16
 */
public class AssRemindPlanRespDTO {

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class DetailDTO {

        /**
         * 预警计划Id
         */
        private Long id;

        /**
         * 预警计划名称
         */
        private String planName;

        /**
         * 业务类型 1:账号交付服务 2:商品工单服务 3:售后工单 4客诉工单
         */
        private Integer serviceType;

        /**
         * 订单来源(会员类型)：1 散户工单 2 号商工单 3 3A工单，服务类型为商品工单有值
         */
        private Integer membership;

        /**
         * 工单类型 1:找回 2:纠纷 服务类型为售后工单有值
         */
        private Integer workOrderType;

        /**
         * 预警游戏配置
         */
        private List<AssPlanGameConfigDTO> planGameConfigs;

        /**
         * 预警规则
         */
        private List<PlanRuleDTO> planRules;

        /**
         * 免打扰时间段 {"startTime":"22:00","endTime":"08:00"}
         */
        // @NotBlank(message = "免打扰时间段不能为空")
        private NotDisturbPeriodDTO notDisturbPeriod;
    }
}
