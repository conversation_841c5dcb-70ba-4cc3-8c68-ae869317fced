package com.pxb7.mall.workorder.infra.repository.db.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pxb7.mall.workorder.infra.model.RemindPlanReqPO;
import com.pxb7.mall.workorder.infra.repository.db.entity.RemindPlan;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 提醒服务预警计划(RemindPlan)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-03-24 21:09:51
 */
@Mapper
public interface RemindPlanMapper extends BaseMapper<RemindPlan> {
    /**
     * 批量新增数据
     *
     * @param entities List<RemindPlan> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<RemindPlan> entities);

    /**
     * 分页查询数据--多表联查（RemindPlan + RemindSubPlan + RemindPlanGameConfig）
     * @param page
     * @param param
     * @return
     */
    Page<RemindPlan> page(Page<RemindPlan> page, @Param("param") RemindPlanReqPO.PagePO param);

}

