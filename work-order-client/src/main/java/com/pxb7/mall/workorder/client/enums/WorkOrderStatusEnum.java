package com.pxb7.mall.workorder.client.enums;

import lombok.Getter;
import lombok.ToString;

/**
 * 工单状态
 * <AUTHOR>
 */
@Getter
@ToString
public enum WorkOrderStatusEnum {

    /**
     * 工单状态：1:待接单，2:已接单，3:待跟进
     */
    PENDING(1, 1, "待接单"),
    RECEIVED(2, 2, "已接单-已接单"),
    TO_BE_FOLLOWED(3,  6, "已接单-待跟进");

    private final Integer code;
    private final Integer workOrderStatus;
    private final String desc;

    WorkOrderStatusEnum(Integer code, Integer workOrderStatus, String desc) {
        this.code = code;
        this.workOrderStatus = workOrderStatus;
        this.desc = desc;
    }

    public static WorkOrderStatusEnum getByWorkOrderStatus(Integer workOrderStatus) {
        for (WorkOrderStatusEnum workOrderStatusEnum : WorkOrderStatusEnum.values()) {
            if (workOrderStatusEnum.getWorkOrderStatus().equals(workOrderStatus)) {
                return workOrderStatusEnum;
            }
        }
        return null;
    }

    public static WorkOrderStatusEnum getByCode(Integer code) {
        for (WorkOrderStatusEnum workOrderStatusEnum : WorkOrderStatusEnum.values()) {
            if (workOrderStatusEnum.getCode().equals(code)) {
                return workOrderStatusEnum;
            }
        }
        return null;
    }

}
