package com.pxb7.mall.workorder.domain.model;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.List;

@Getter
@Setter
@Accessors(chain = true)
@ToString
public class RemindMethodConfigBO {

    /**
     * 提醒方式:feishu,im...
     * @see com.pxb7.mall.workorder.client.enums.RemindMethodEnum
     */
    private String method;

    /**
     * 通知对象列表
     */
    private List<RemindObjectBO> objects;


    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class RemindObjectBO {

        /**
         * 提醒对象： 飞书个人、飞书群消息....
         * @see com.pxb7.mall.workorder.client.enums.RemindObjectEnum
         */
        private String object;

        /**
         * 飞书机器人webhook
         */
        private String webhook;

        /**
         * 自定义其他提醒对象
         */
        private String customUserIds;

    }

}
