package com.pxb7.mall.workorder.infra.model;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@ToString
public class GameBasePO {

    /**
     * 游戏业务ID
     */
    private String gameId;
    /**
     * 游戏名称
     */
    private String gameName;


    /**
     * 游戏别名
     */
    private String gameAlias;

    /**
     * 游戏编码
     */
    //private String gameCode;

    /**
     * 是否隐藏 0 否 1 是
     */
    //private Boolean hidden;

    /**
     * 厂商 1 网易系  2 腾讯系  3 米哈游  4其他
     */
    private Integer maker;
}
