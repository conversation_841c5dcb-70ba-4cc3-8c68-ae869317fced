package com.pxb7.mall.workorder.app.handler.bargain;

import com.pxb7.mall.workorder.domain.message.BargainTicketMessage;
import com.pxb7.mall.workorder.domain.service.BargainTicketDomainService;
import com.pxb7.mall.workorder.infra.repository.db.BargainAcceptanceCustomerRepository;
import com.pxb7.mall.workorder.infra.repository.db.entity.BargainAcceptanceCustomer;
import com.pxb7.mall.workorder.infra.repository.es.mapper.BargainTicketDocEsRepository;
import com.pxb7.mall.workorder.infra.repository.es.mapper.BargainTicketDocRepository;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version : AbstractBargainTicketSyncProcessor.java, v 0.1 2025年04月21日 16:32 yang.xuexi Exp $
 */
@Component
@Slf4j
public abstract class AbstractBargainTicketSyncProcessor implements BargainTicketSyncProcessor {

    @Resource
    protected BargainTicketDomainService bargainTicketDomainService;

    @Resource
    protected BargainTicketDocEsRepository bargainTicketDocEsRepository;

    @Resource
    protected BargainTicketDocRepository bargainTicketDocRepository;

    @Resource
    protected BargainAcceptanceCustomerRepository bargainAcceptanceCustomerRepository;


    @java.lang.Override
    public void process(BargainTicketMessage message) {
        BargainAcceptanceCustomer byReceiveId = bargainAcceptanceCustomerRepository.getByReceiveId(message.getReceiveId());
        if (Objects.isNull(byReceiveId)) {
            //未查询到客服接单信息直接跳过
            log.warn("未查询到客服接单信息直接跳过 byReceiveId:{}", message.getReceiveId());
            return;
        }
        doProcess(message, byReceiveId);
    }

    protected abstract void doProcess(BargainTicketMessage message, BargainAcceptanceCustomer byReceiveId);
}
