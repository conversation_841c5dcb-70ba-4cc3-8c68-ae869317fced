package com.pxb7.mall.workorder.app.service;

import static com.pxb7.mall.workorder.client.enums.BizErrorCodeEnum.PARAM_ERROR;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.pxb7.mall.workorder.domain.model.*;
import com.pxb7.mall.workorder.infra.constant.RedisKeyConstants;
import com.pxb7.mall.workorder.infra.util.RedissonUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.cola.exception.Assert;
import com.alibaba.cola.exception.BizException;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pxb7.mall.trade.ass.client.dto.response.afc.DisputeWORespDTO;
import com.pxb7.mall.trade.ass.client.dto.response.afc.RetrieveWORespDTO;
import com.pxb7.mall.workorder.app.mapping.RemindAfterSaleAppMapping;
import com.pxb7.mall.workorder.app.model.AfterSalePageDetailDTO;
import com.pxb7.mall.workorder.app.model.AfterSaleStatisticDataDTO;
import com.pxb7.mall.workorder.app.model.AfterSaleStatisticDataSearchDTO;
import com.pxb7.mall.workorder.app.model.RemindAfterSaleReqDTO;
import com.pxb7.mall.workorder.app.util.RemindPlanPageBuildUtil;
import com.pxb7.mall.workorder.app.util.TimeTipUtil;
import com.pxb7.mall.workorder.client.enums.BizTypeEnum;
import com.pxb7.mall.workorder.client.enums.CompleteStatusEnum;
import com.pxb7.mall.workorder.domain.service.RemindAfterSaleDomainService;
import com.pxb7.mall.workorder.domain.service.RemindPlanGameConfigDomainService;
import com.pxb7.mall.workorder.domain.service.RemindPlanRecordDomainService;
import com.pxb7.mall.workorder.infra.aop.ClusterRedisLock;
import com.pxb7.mall.workorder.infra.enums.RemindPlanWorkOrderTypeEnum;
import com.pxb7.mall.workorder.infra.model.GameBasePO;
import com.pxb7.mall.workorder.infra.model.SysUserRespPO;
import com.pxb7.mall.workorder.infra.repository.gateway.dubbo.ass.AfcWorkOrderGateway;
import com.pxb7.mall.workorder.infra.repository.gateway.dubbo.order.OrderInfoGateway;
import com.pxb7.mall.workorder.infra.repository.gateway.dubbo.product.GameGateway;
import com.pxb7.mall.workorder.infra.repository.gateway.dubbo.product.ProductGateway;
import com.pxb7.mall.workorder.infra.repository.gateway.dubbo.user.SysUserGateway;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

/**
 * 售后工单预警记录app服务
 *
 * <AUTHOR>
 * @since 2025-04-24 23:33:20
 */

@Slf4j
@Service
public class RemindAfterSaleAppService {

    @Resource
    private RemindPlanRecordDomainService planRecordDomainService;

    @Resource
    private RemindPlanGameConfigDomainService gameConfigDomainService;

    @Resource
    private RemindAfterSaleDomainService afterSaleDomainService;

    @Resource
    private AfcWorkOrderGateway afcWorkOrderGateway;

    @Resource
    private OrderInfoGateway orderInfoGateway;

    @Resource
    private ProductGateway productGateway;

    @Resource
    private SysUserGateway sysUserGateway;

    @Resource
    private GameGateway gameGateway;

    /**
     * 处理售后工单(找回,纠纷)
     * @param workOrderId
     */
    @Transactional(rollbackFor = Exception.class)
    @ClusterRedisLock(prefix = "ass_work_order_plan", value = "#workOrderId")
    public void dealAfterSaleWorkOrder(String workOrderId, Integer workOrderType) {
        RemindAfterSaleBO remindAfterSaleBO = new RemindAfterSaleBO();
        remindAfterSaleBO.setWorkOrderType(workOrderType);
        if (RemindPlanWorkOrderTypeEnum.RETRIEVE.eq(workOrderType)) {
            //根据工单ID查询找回工单信息
            RetrieveWORespDTO retrieveWORespDTO = afcWorkOrderGateway.getRetrieveWorkOrderInfo(workOrderId);
            if (Objects.isNull(retrieveWORespDTO)) {
                log.error("Failed to query retrieve work order info. workOrderId:{}", workOrderId);
                return;
            }
            BeanUtils.copyProperties(retrieveWORespDTO, remindAfterSaleBO);
            remindAfterSaleBO.setProductCode(retrieveWORespDTO.getProductUniqueNo());
            remindAfterSaleBO.setRetrieveUserId(retrieveWORespDTO.getDealUserId());
            remindAfterSaleBO.setCompleteStatus(retrieveWORespDTO.getStatus());
        } else {
            //根据工单ID查询纠纷工单信息
            DisputeWORespDTO disputeWORespDTO = afcWorkOrderGateway.getDisputeWorkOrderInfo(workOrderId);
            if (Objects.isNull(disputeWORespDTO)) {
                log.error("Failed to query dispute work order info. workOrderId:{}", workOrderId);
                return;
            }
            BeanUtils.copyProperties(disputeWORespDTO, remindAfterSaleBO);
            remindAfterSaleBO.setProductCode(disputeWORespDTO.getProductUniqueNo());
            remindAfterSaleBO.setCompleteStatus(disputeWORespDTO.getStatus());
        }

        if (CompleteStatusEnum.IN_PROCESS.eq(remindAfterSaleBO.getCompleteStatus())) {
            //生成预警执行计划
            generateRemindPlan(remindAfterSaleBO);
        } else {
            //工单状态变更，修改预警执行计划状态
            changeRemindPlanStatus(remindAfterSaleBO);
        }
    }

    /**
     * 查询卖家身份（1:散户，2:号商，3:3A）
     * @param gameId
     * @param productId
     * @return
     */
    private Integer getSellerMembership(String gameId, String productId) {
        String sellerId = productGateway.getSellerId(gameId, productId);
        if (StringUtils.isBlank(sellerId)) {
            log.error("Failed to query seller id. productId:{}", productId);
            return null;
        }
        return sysUserGateway.getSellerMembership(sellerId);
    }

    /**
     * 根据售后工单生成提醒计划
     *
     * @param remindAfterSaleBO 售后工单信息
     */
    public void generateRemindPlan(RemindAfterSaleBO remindAfterSaleBO) {
        String workOrderId = remindAfterSaleBO.getWorkOrderId();
        String orderItemId = remindAfterSaleBO.getOrderItemId();
        if (StringUtils.isBlank(orderItemId)) {
            log.warn("订单id为空, workOrderId:{}", workOrderId);
            return;
        }
        String groupId = orderInfoGateway.getGroupId(orderItemId);
        if (Objects.isNull(groupId)) {
            log.warn("订单对应的房间id为空, workOrderId:{}, orderItemId:{}", workOrderId, orderItemId);
            return;
        }
        remindAfterSaleBO.setGroupId(groupId);
        //游戏id
        String gameId = remindAfterSaleBO.getGameId();
        if (StringUtils.isBlank(gameId)) {
            log.warn("游戏id不能为空, workOrderId:{}", workOrderId);
            return;
        }
        //查询卖家身份（1:散户，2:号商，3:3A）
        Integer membership = RemindPlanWorkOrderTypeEnum.RETRIEVE.eq(remindAfterSaleBO.getWorkOrderType()) ?
                getSellerMembership(remindAfterSaleBO.getGameId(), remindAfterSaleBO.getProductId()) : null;
        //使用gameId查询适配的预警计划
        RemindPlanGameConfigBO remindPlanGameConfig = gameConfigDomainService.getAssRemindPlanGameConfig(
                gameId, remindAfterSaleBO.getWorkOrderType(), membership);
        if (Objects.isNull(remindPlanGameConfig)) {
            log.warn("游戏id对应的预警计划不存在, gameId:{}, workOrderType:{}, membership:{}",
                    gameId, remindAfterSaleBO.getWorkOrderType(), membership);
            return;
        }
        TimeConfigBO expectCompleteTimeConfig = remindPlanGameConfig.getExpectCompleteTimeConfig();
        if (Objects.isNull(expectCompleteTimeConfig)
                || (Objects.isNull(expectCompleteTimeConfig.getHours()) && Objects.isNull(expectCompleteTimeConfig.getMinutes()))) {
            log.warn("游戏id对应的预警计划预期完结时间没有配置, gameId:{}, remindPlanId:{}", gameId, remindPlanGameConfig.getRemindPlanId());
            return;
        }
        //生成售后工单预警记录
        RemindAfterSaleReqBO.AddBO remindAfterSale =
                afterSaleDomainService.generateRemindAfterSale(remindAfterSaleBO, remindPlanGameConfig);
        if (Objects.isNull(remindAfterSale)) {
            log.warn("售后工单对应的预警记录已存在, workOrderId:{}", remindAfterSaleBO.getWorkOrderId());
            return;
        }
        //生成售后工单预警执行计划记录
        generateAfterSalePlanRecords(remindAfterSale);
    }

    /**
     * 生成账号交付预警执行计划记录
     * @param remindAfterSale
     */
    private void generateAfterSalePlanRecords(RemindAfterSaleReqBO.AddBO remindAfterSale) {
        RemindPlanRecordBO remindPlanRecordBO = new RemindPlanRecordBO()
                .setRecordIdPrefix("ASP")
                .setBizType(BizTypeEnum.AFTER_SALE.getType())
                .setBizId(remindAfterSale.getWorkOrderId())
                .setRemindId(remindAfterSale.getRemindId())
                .setRemindPlanId(remindAfterSale.getRemindPlanId())
                .setRemindSubPlanId(remindAfterSale.getRemindSubPlanId())
                .setExpectCompleteTime(remindAfterSale.getExpectCompleteTime());
        //生成预警执行计划记录
        planRecordDomainService.generatePlanRecords(remindPlanRecordBO);
    }

    /**
     * 工单状态变更，修改预警执行计划状态
     * @param remindAfterSaleBO
     */
    public void changeRemindPlanStatus(RemindAfterSaleBO remindAfterSaleBO) {
        //更新售后工单预警记录状态
        RemindAfterSaleReqBO.UpdateBO updateBO = new RemindAfterSaleReqBO.UpdateBO();
        updateBO.setWorkOrderId(remindAfterSaleBO.getWorkOrderId());
        updateBO.setCompleteStatus(remindAfterSaleBO.getCompleteStatus());
        updateBO.setCompleteTime(LocalDateTime.now());
        afterSaleDomainService.updateByWorkOrderId(updateBO);
        //作废售后工单预警执行计划
        planRecordDomainService.invalidPlanRecord(
                BizTypeEnum.AFTER_SALE.getType(), remindAfterSaleBO.getWorkOrderId());

        //删除redis群组超时状态
        RemindAfterSaleRespBO.DetailBO remindAfterSale =
                afterSaleDomainService.findByWorkOrderId(remindAfterSaleBO.getWorkOrderId(), null);
        if (Objects.nonNull(remindAfterSale)) {
            String groupTimoutKey = String.format(RedisKeyConstants.TIMEOUT_GROUP_KEY,
                    remindAfterSale.getGroupId(), remindAfterSale.getDisputeUserId());
            RedissonUtils.deleteObject(groupTimoutKey);
        }
    }

    public Page<AfterSalePageDetailDTO> page(RemindAfterSaleReqDTO.PageDTO param) {

        RemindAfterSaleReqBO.PageBO pageBO = RemindAfterSaleAppMapping.INSTANCE.remindAfterSaleDTO2PageBO(param);
        Page<RemindAfterSaleBO> page = afterSaleDomainService.pageEs(pageBO);
        Page<AfterSalePageDetailDTO> pageDTO = RemindAfterSaleAppMapping.INSTANCE.remindAfterSaleBO2PageDTO(page);
        // 提取并处理工作订单列表中的用户ID，用于后续查询用户信息
        List<AfterSalePageDetailDTO> records = Optional.of(pageDTO).map(Page::getRecords).orElse(new ArrayList<>());

        Set<String> userIds = RemindPlanPageBuildUtil.extractUserIdsFromAfterSale(records);
        List<SysUserRespPO> sysUserInfoList = sysUserGateway.getSysUserInfoList(userIds);
        Map<String, SysUserRespPO> sysUserRespPOMap =
            sysUserInfoList.stream().collect(Collectors.toMap(SysUserRespPO::getUserId, Function.identity()));

        List<String> gameIds = records.stream().map(AfterSalePageDetailDTO::getGameId).toList();
        List<GameBasePO> gameBasePOList = gameGateway.getGameInfoByIds(gameIds);
        Map<String, GameBasePO> gameBasePOMap =
            gameBasePOList.stream().collect(Collectors.toMap(GameBasePO::getGameId, Function.identity()));

        // 遍历工作订单列表，设置用户名称信息、提示信息等
        records.forEach(a -> {
            if (StringUtils.isNotBlank(a.getRetrieveUserId())) {
                String userName = Optional.of(sysUserRespPOMap).map(s -> s.get(a.getRetrieveUserId()))
                    .map(SysUserRespPO::getUserName).orElse(null);
                a.setRetrieveUserName(userName);
            }
            if (StringUtils.isNotBlank(a.getDisputeUserId())) {
                String userName = Optional.of(sysUserRespPOMap).map(s -> s.get(a.getDisputeUserId()))
                    .map(SysUserRespPO::getUserName).orElse(null);
                a.setDisputeUserName(userName);
            }
            if (StringUtils.isNotBlank(a.getGameId())) {
                String userName =
                    Optional.of(gameBasePOMap).map(s -> s.get(a.getGameId())).map(GameBasePO::getGameName).orElse(null);
                a.setGameName(userName);
            }
            a.setDurationTip(TimeTipUtil.getDurationTip(a.getCreateTime(),
                null == a.getCompleteTime() ? LocalDateTime.now() : a.getCompleteTime()));
            a.setCountDownTip(TimeTipUtil.getCountDownTip(
                null == a.getCompleteTime() ? LocalDateTime.now() : a.getCompleteTime(), a.getExpectCompleteTime()));
        });

        // 返回设置有用户名称信息的工作订单分页列表
        return pageDTO.setRecords(records);

    }

    public List<AfterSaleStatisticDataDTO> getStatisticList(AfterSaleStatisticDataSearchDTO param) {

        if (StringUtils.isBlank(param.getStartDate()) || StringUtils.isBlank(param.getEndDate())) {
            LocalDateTime nowDateTime = LocalDateTime.now();
            LocalDateTime starDateTime = nowDateTime.minusDays(31);
            param.setStartDate(starDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            param.setEndDate(nowDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        } else {
            LocalDate startDate;
            LocalDate endDate;
            try {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                startDate = LocalDate.parse(param.getStartDate(), formatter);
                endDate = LocalDate.parse(param.getEndDate(), formatter);
            } catch (Exception e) {
                throw new BizException(PARAM_ERROR.getErrCode(), "日期格式不正确");
            }
            Assert.isTrue(!startDate.isAfter(endDate), PARAM_ERROR.getErrCode(), "创建开始时间要早于创建结束时间");
            long daysBetween = Math.abs(ChronoUnit.DAYS.between(startDate, endDate));
            Assert.isTrue(daysBetween <= 31, PARAM_ERROR.getErrCode(), "筛选的时间间隔不能超过31天");
        }

        AfterSaleStatisticDataSearchBO searchBO = RemindAfterSaleAppMapping.INSTANCE.statisticParam2BO(param);
        List<AfterSaleStatisticDataBO> list = afterSaleDomainService.getStatisticList(searchBO);
        if (!CollectionUtils.isEmpty(list)) {
            AfterSaleStatisticDataBO dataBO = new AfterSaleStatisticDataBO();
            dataBO.setCreateDate("汇总");
            dataBO.setWorkOrderCnt(list.stream().filter(a -> a.getWorkOrderCnt() != null)
                .mapToLong(AfterSaleStatisticDataBO::getWorkOrderCnt).sum());
            dataBO.setCompleteWorkOrderCnt(list.stream().filter(a -> a.getCompleteWorkOrderCnt() != null)
                .mapToLong(AfterSaleStatisticDataBO::getCompleteWorkOrderCnt).sum());
            dataBO.setProcessingWorkOrderCnt(list.stream().filter(a -> a.getProcessingWorkOrderCnt() != null)
                .mapToLong(AfterSaleStatisticDataBO::getProcessingWorkOrderCnt).sum());
            dataBO.setTimeoutingWorkOrderCnt(list.stream().filter(a -> a.getTimeoutingWorkOrderCnt() != null)
                .mapToLong(AfterSaleStatisticDataBO::getTimeoutingWorkOrderCnt).sum());
            dataBO.setTimeoutedWorkOrderCnt(list.stream().filter(a -> a.getTimeoutedWorkOrderCnt() != null)
                .mapToLong(AfterSaleStatisticDataBO::getTimeoutedWorkOrderCnt).sum());

            list.add(dataBO);
        }
        return RemindAfterSaleAppMapping.INSTANCE.statisticResul2DTOList(list);
    }

    public LocalDateTime getUpdateTime() {
        return afterSaleDomainService.getLastDataInsertTime();
    }
}

