package com.pxb7.mall.workorder.app.service;


import com.alibaba.cola.exception.Assert;
import com.alibaba.cola.exception.BizException;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pxb7.mall.trade.ass.client.dto.response.afc.ComplaintWORespDTO;
import com.pxb7.mall.workorder.app.mapping.RemindComplaintAppMapping;
import com.pxb7.mall.workorder.app.model.ComplaintPageDetailDTO;
import com.pxb7.mall.workorder.app.model.ComplaintStatisticDataDTO;
import com.pxb7.mall.workorder.app.model.ComplaintStatisticDataSearchDTO;
import com.pxb7.mall.workorder.app.model.RemindComplaintReqDTO;
import com.pxb7.mall.workorder.app.util.RemindPlanPageBuildUtil;
import com.pxb7.mall.workorder.app.util.TimeTipUtil;
import com.pxb7.mall.workorder.client.enums.BizTypeEnum;
import com.pxb7.mall.workorder.client.enums.CompleteStatusEnum;
import com.pxb7.mall.workorder.domain.model.*;
import com.pxb7.mall.workorder.domain.service.RemindComplaintDomainService;
import com.pxb7.mall.workorder.domain.service.RemindPlanGameConfigDomainService;
import com.pxb7.mall.workorder.domain.service.RemindPlanRecordDomainService;
import com.pxb7.mall.workorder.infra.aop.ClusterRedisLock;
import com.pxb7.mall.workorder.infra.constant.RedisKeyConstants;
import com.pxb7.mall.workorder.infra.model.SysUserRespPO;
import com.pxb7.mall.workorder.infra.repository.gateway.dubbo.ass.AfcWorkOrderGateway;
import com.pxb7.mall.workorder.infra.repository.gateway.dubbo.user.SysUserGateway;
import com.pxb7.mall.workorder.infra.util.RedissonUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.pxb7.mall.workorder.client.enums.BizErrorCodeEnum.PARAM_ERROR;

/**
 * 客诉工单预警记录app服务
 *
 * <AUTHOR>
 * @since 2025-04-24 23:33:20
 */
@Slf4j
@Service
public class RemindComplaintAppService {

    @Resource
    private RemindComplaintDomainService complaintDomainService;

    @Resource
    private RemindPlanRecordDomainService planRecordDomainService;

    @Resource
    private RemindPlanGameConfigDomainService gameConfigDomainService;

    @Resource
    private AfcWorkOrderGateway afcWorkOrderGateway;

    @Resource
    private SysUserGateway sysUserGateway;

    /**
     * 处理客诉工单
     * @param workOrderId
     */
    @Transactional(rollbackFor = Exception.class)
    @ClusterRedisLock(prefix = "complaint_work_order_plan", value = "#workOrderId")
    public void dealComplaintWorkOrder(String workOrderId) {
        //根据工单ID查询客诉工单信息
        ComplaintWORespDTO complaintWORespDTO = afcWorkOrderGateway.getComplaintWorkOrderInfo(workOrderId);
        if (Objects.isNull(complaintWORespDTO)) {
            log.error("Failed to query dispute work order info. workOrderId:{}", workOrderId);
            return;
        }
        if (CompleteStatusEnum.IN_PROCESS.eq(complaintWORespDTO.getWorkOrderStatus())) {
            //生成预警执行计划
            generateRemindPlan(complaintWORespDTO);
        } else {
            //工单状态变更，修改预警执行计划状态
            changeRemindPlanStatus(complaintWORespDTO);
        }
    }


    /**
     * 根据客诉工单生成提醒计划
     *
     * @param complaintWORespDTO
     */
    public void generateRemindPlan(ComplaintWORespDTO complaintWORespDTO) {
        //查询适配的预警计划
        RemindPlanGameConfigBO remindPlanGameConfig = gameConfigDomainService.getComplaintRemindPlanGameConfig(
                complaintWORespDTO.getComplaintLevel(), complaintWORespDTO.getComplaintChannel());
        if (Objects.isNull(remindPlanGameConfig)) {
            log.warn("渠道对应的预警计划不存在, complaintLevel:{}, complaintChannel:{}",
                    complaintWORespDTO.getComplaintLevel(), complaintWORespDTO.getComplaintChannel());
            return;
        }
        TimeConfigBO expectCompleteTimeConfig = remindPlanGameConfig.getExpectCompleteTimeConfig();
        if (Objects.isNull(expectCompleteTimeConfig)
                || (Objects.isNull(expectCompleteTimeConfig.getHours()) && Objects.isNull(expectCompleteTimeConfig.getMinutes()))) {
            log.warn("渠道对应的预警计划预期完结时间没有配置, remindPlanId:{}", remindPlanGameConfig.getRemindPlanId());
            return;
        }
        //生成客诉工单预警记录
        RemindComplaintReqBO.AddBO remindComplaint =
                complaintDomainService.generateRemindComplaint(complaintWORespDTO, remindPlanGameConfig);
        if (Objects.isNull(remindComplaint)) {
            log.warn("客诉工单对应的预警记录已存在, workOrderId:{}", complaintWORespDTO.getWorkOrderId());
            return;
        }
        //生成客诉工单预警执行计划记录
        generateComplaintPlanRecords(remindComplaint);
    }

    /**
     * 生成预警执行计划记录
     * @param remindComplaint
     */
    private void generateComplaintPlanRecords(RemindComplaintReqBO.AddBO remindComplaint) {
        RemindPlanRecordBO remindPlanRecordBO = new RemindPlanRecordBO()
                .setRecordIdPrefix("CPP")
                .setBizType(BizTypeEnum.COMPLAINT.getType())
                .setBizId(remindComplaint.getWorkOrderId())
                .setRemindId(remindComplaint.getRemindId())
                .setRemindPlanId(remindComplaint.getRemindPlanId())
                .setRemindSubPlanId(remindComplaint.getRemindSubPlanId())
                .setExpectCompleteTime(remindComplaint.getExpectCompleteTime());
        //生成预警执行计划记录
        planRecordDomainService.generatePlanRecords(remindPlanRecordBO);
    }

    /**
     * 工单状态变更，修改预警执行计划状态
     * @param complaintWORespDTO
     */
    public void changeRemindPlanStatus(ComplaintWORespDTO complaintWORespDTO) {
        //更新客诉工单预警记录状态
        RemindComplaintReqBO.UpdateBO updateBO = new RemindComplaintReqBO.UpdateBO();
        updateBO.setWorkOrderId(complaintWORespDTO.getWorkOrderId());
        updateBO.setCompleteStatus(complaintWORespDTO.getWorkOrderStatus());
        updateBO.setCompleteTime(LocalDateTime.now());
        complaintDomainService.updateByWorkOrderId(updateBO);
        //作废客诉工单预警执行计划
        planRecordDomainService.invalidPlanRecord(
                BizTypeEnum.COMPLAINT.getType(), complaintWORespDTO.getWorkOrderId());

        //删除redis群组超时状态
        RemindComplaintRespBO.DetailBO remindComplaint =
                complaintDomainService.findByWorkOrderId(complaintWORespDTO.getWorkOrderId(), null);
        if (Objects.nonNull(remindComplaint)) {
            String groupTimoutKey = String.format(RedisKeyConstants.TIMEOUT_GROUP_KEY,
                    remindComplaint.getGroupId(), remindComplaint.getHandleUserId());
            RedissonUtils.deleteObject(groupTimoutKey);
        }
    }

    public Page<ComplaintPageDetailDTO> page(RemindComplaintReqDTO.PageDTO param) {

        // 将查询参数转换为业务对象，以便在领域服务中使用
        RemindComplaintReqBO.PageBO pageBO = RemindComplaintAppMapping.INSTANCE.remindComplaintDTO2PageBO(param);
        // 调用领域服务执行分页查询，获取工作订单列表
        Page<RemindComplaintBO> page = complaintDomainService.pageEs(pageBO);

        // 将查询结果转换为DTO形式，以便返回给客户端
        Page<ComplaintPageDetailDTO> pageDTO = RemindComplaintAppMapping.INSTANCE.remindComplaintBO2PageDTO(page);

        // 提取并处理工作订单列表中的用户ID，用于后续查询用户信息
        List<ComplaintPageDetailDTO> records = Optional.of(pageDTO).map(Page::getRecords).orElse(new ArrayList<>());

        Set<String> userIds = RemindPlanPageBuildUtil.extractUserIdsFromComplaint(records);
        List<SysUserRespPO> sysUserInfoList = sysUserGateway.getSysUserInfoList(userIds);
        Map<String, SysUserRespPO> sysUserRespPOMap =
            sysUserInfoList.stream().collect(Collectors.toMap(SysUserRespPO::getUserId, Function.identity()));

        // 定义一个双函数，用于根据用户ID获取用户名
        BiFunction<Map<String, SysUserRespPO>, String, String> biFunction =
            (Map<String, SysUserRespPO> userRespPOMap, String userId) -> {
                if (StringUtils.isBlank(userId)) {
                    return null;
                }
                return Optional.ofNullable(userRespPOMap).map(a -> a.get(userId)).map(SysUserRespPO::getUserName)
                    .orElse(null);
            };

        // 遍历工作订单列表，设置用户名称信息、提示信息等
        records.forEach(a -> {
            a.setHandleUserName(biFunction.apply(sysUserRespPOMap, a.getHandleUserId()));

            a.setDurationTip(TimeTipUtil.getDurationTip(a.getCreateTime(),
                null == a.getCompleteTime() ? LocalDateTime.now() : a.getCompleteTime()));
            a.setCountDownTip(TimeTipUtil.getCountDownTip(
                null == a.getCompleteTime() ? LocalDateTime.now() : a.getCompleteTime(), a.getExpectCompleteTime()));
        });

        // 返回设置有用户名称信息的工作订单分页列表
        return pageDTO.setRecords(records);
    }

    public List<ComplaintStatisticDataDTO> getStatisticList(ComplaintStatisticDataSearchDTO param) {

        if (StringUtils.isBlank(param.getStartDate()) || StringUtils.isBlank(param.getEndDate())) {
            LocalDateTime nowDateTime = LocalDateTime.now();
            LocalDateTime starDateTime = nowDateTime.minusDays(31);
            param.setStartDate(starDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            param.setEndDate(nowDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        } else {
            LocalDate startDate;
            LocalDate endDate;
            try {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                startDate = LocalDate.parse(param.getStartDate(), formatter);
                endDate = LocalDate.parse(param.getEndDate(), formatter);
            } catch (Exception e) {
                throw new BizException(PARAM_ERROR.getErrCode(), "日期格式不正确");
            }
            Assert.isTrue(!startDate.isAfter(endDate), PARAM_ERROR.getErrCode(), "创建开始时间要早于创建结束时间");
            long daysBetween = Math.abs(ChronoUnit.DAYS.between(startDate, endDate));
            Assert.isTrue(daysBetween <= 31, PARAM_ERROR.getErrCode(), "筛选的时间间隔不能超过31天");
        }

        ComplaintStatisticDataSearchBO searchBO = RemindComplaintAppMapping.INSTANCE.statisticParam2BO(param);
        List<ComplaintStatisticDataBO> list = complaintDomainService.getStatisticList(searchBO);
        if (!CollectionUtils.isEmpty(list)) {
            ComplaintStatisticDataBO dataBO = new ComplaintStatisticDataBO();
            dataBO.setCreateDate("汇总");
            dataBO.setWorkOrderCnt(list.stream().filter(a -> a.getWorkOrderCnt() != null)
                .mapToLong(ComplaintStatisticDataBO::getWorkOrderCnt).sum());
            dataBO.setCompleteWorkOrderCnt(
                list.stream().filter(a -> a.getCompleteWorkOrderCnt() != null)
                    .mapToLong(ComplaintStatisticDataBO::getCompleteWorkOrderCnt).sum());
            dataBO.setProcessingWorkOrderCnt(
                list.stream().filter(a -> a.getProcessingWorkOrderCnt() != null)
                    .mapToLong(ComplaintStatisticDataBO::getProcessingWorkOrderCnt).sum());
            dataBO.setTimeoutingWorkOrderCnt(
                list.stream().filter(a -> a.getTimeoutingWorkOrderCnt() != null)
                    .mapToLong(ComplaintStatisticDataBO::getTimeoutingWorkOrderCnt).sum());
            dataBO.setTimeoutedWorkOrderCnt(
                list.stream().filter(a -> a.getTimeoutedWorkOrderCnt() != null)
                    .mapToLong(ComplaintStatisticDataBO::getTimeoutedWorkOrderCnt).sum());

            list.add(dataBO);
        }

        return RemindComplaintAppMapping.INSTANCE.statisticResul2DTOList(list);
    }

    public LocalDateTime getUpdateTime() {
        return complaintDomainService.getLastDataInsertTime();
    }
}

