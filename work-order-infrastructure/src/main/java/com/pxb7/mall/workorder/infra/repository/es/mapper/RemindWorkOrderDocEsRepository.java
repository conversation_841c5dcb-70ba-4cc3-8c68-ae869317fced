package com.pxb7.mall.workorder.infra.repository.es.mapper;

import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.Query;
import co.elastic.clients.elasticsearch._types.query_dsl.QueryBuilders;
import co.elastic.clients.elasticsearch._types.query_dsl.TermQuery;
import com.pxb7.mall.workorder.client.enums.CompleteStatusEnum;
import com.pxb7.mall.workorder.client.enums.TimeoutStatusEnum;
import com.pxb7.mall.workorder.infra.model.RemindWorkOrderReqPO;
import com.pxb7.mall.workorder.infra.repository.es.entity.RemindWorkOrderDoc;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.elasticsearch.client.elc.ElasticsearchTemplate;
import org.springframework.data.elasticsearch.client.elc.NativeQuery;
import org.springframework.data.elasticsearch.client.elc.NativeQueryBuilder;
import org.springframework.data.elasticsearch.core.SearchHitSupport;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.SearchPage;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Repository
public class RemindWorkOrderDocEsRepository {

    @Resource
    private ElasticsearchTemplate elasticsearchTemplate;



    public SearchPage<RemindWorkOrderDoc> pageQueryWorkOrder(RemindWorkOrderReqPO.PagePO workOrderPagePO) {

        // 构造查询条件
        List<Query> mustQueryList = new ArrayList<>();

        if (StringUtils.isNotBlank(workOrderPagePO.getWorkOrderId())) {
            TermQuery workOrderIdQuery = QueryBuilders.term().field("work_order_id")
                .value(workOrderPagePO.getWorkOrderId()).build();
            mustQueryList.add(workOrderIdQuery._toQuery());
        }
        // 构造排序条件
        Sort sort = Sort.by(Sort.Order.desc("create_time"));

        // 构建最终查询结构
        var nativeQuery = NativeQuery.builder().withQuery(QueryBuilders.bool().must(mustQueryList).build()._toQuery())
            .withPageable(PageRequest.of((int)workOrderPagePO.getPageIndex() - 1, (int)workOrderPagePO.getPageSize()))
            .withSort(sort).withTrackTotalHits(true).build();

        log.info("pageQueryWorkOrder DSL语句：{}", nativeQuery.getQuery().toString());

        SearchHits<RemindWorkOrderDoc> searchHits = elasticsearchTemplate.search(nativeQuery, RemindWorkOrderDoc.class);
        return SearchHitSupport.searchPageFor(searchHits, nativeQuery.getPageable());

    }

    /**
     * 查询超时工单数量
     * @param timoutStatus
     * @return
     */
    public Long countWorkOrder(TimeoutStatusEnum timoutStatus) {
        NativeQueryBuilder nativeQueryBuilder = new NativeQueryBuilder();
        BoolQuery.Builder boolFilter = QueryBuilders.bool();
        boolFilter.filter(QueryBuilders.term(a -> a.field("complete_status").value(CompleteStatusEnum.IN_PROCESS.getCode())));
        boolFilter.filter(QueryBuilders.term(a -> a.field("time_out_status").value(timoutStatus.getCode())));

        nativeQueryBuilder.withQuery(boolFilter.build()._toQuery());

        SearchHits<RemindWorkOrderDoc> searchHits = elasticsearchTemplate.search(nativeQueryBuilder.build(), RemindWorkOrderDoc.class);
        return searchHits.getTotalHits();
    }

}
