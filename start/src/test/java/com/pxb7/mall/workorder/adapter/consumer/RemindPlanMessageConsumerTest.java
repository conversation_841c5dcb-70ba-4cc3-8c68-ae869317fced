package com.pxb7.mall.workorder.adapter.consumer;

import com.alibaba.cola.exception.Assert;
import com.pxb7.mall.workorder.app.service.RemindPlanRecordAppService;
import com.pxb7.mall.workorder.client.enums.BizErrorCodeEnum;
import com.pxb7.mall.workorder.client.enums.PlanExecuteStatusEnum;
import com.pxb7.mall.workorder.client.message.RemindPlanMessage;
import com.pxb7.mall.workorder.domain.helper.TransactionHelper;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.apache.rocketmq.client.java.message.MessageIdImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

@SpringBootTest
public class RemindPlanMessageConsumerTest {

    @InjectMocks
    private RemindPlanMessageConsumer consumer;

    @Mock
    private RemindPlanRecordAppService planRecordAppService;

    @Mock
    private TransactionHelper transactionHelper;

    @Mock
    private MessageView messageView;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        // 设置私有字段
        ReflectionTestUtils.setField(consumer, "planRecordAppService", planRecordAppService);
        ReflectionTestUtils.setField(consumer, "transactionHelper", transactionHelper);

        when(messageView.getMessageId()).thenReturn(new MessageIdImpl("version","test-message-id"));
    }

    /**
     * TC01: 正常消费流程
     */
    @Test
    public void testConsume_Success() {
        RemindPlanMessage message = new RemindPlanMessage();
        message.setRemindId("111");

        PlanExecuteStatusEnum status = PlanExecuteStatusEnum.DONE;

        when(planRecordAppService.handleRemindMessage(message)).thenReturn(status);
        doAnswer(invocation -> {
            Runnable callback = invocation.getArgument(0);
            callback.run();
            return null;
        }).when(transactionHelper).execute(any(Runnable.class), any(), any());

        ConsumeResult result = consumer.consume(messageView, message);

        assertEquals(ConsumeResult.SUCCESS, result);
        verify(planRecordAppService, times(1)).handleRemindMessage(message);
    }

    /**
     * TC02: handleRemindMessage 抛出异常
     */
    @Test
    public void testConsume_ExceptionInHandleMessage() {
        RemindPlanMessage message = new RemindPlanMessage();

        when(planRecordAppService.handleRemindMessage(message)).thenThrow(new RuntimeException("Simulated error"));

        ConsumeResult result = consumer.consume(messageView, message);

        assertEquals(ConsumeResult.FAILURE, result);
        verify(planRecordAppService, times(1)).handleRemindMessage(message);
    }

    /**
     * TC04: 事务回调中 flag 为 false，触发断言失败
     */
    @Test
    public void testConsume_TransactionCallbackFail() {
        RemindPlanMessage message = new RemindPlanMessage();
        PlanExecuteStatusEnum status = PlanExecuteStatusEnum.FAILED;

        when(planRecordAppService.handleRemindMessage(message)).thenReturn(status);
        doAnswer(invocation -> {
            Assert.isTrue(false,
                    BizErrorCodeEnum.REMIND_PLAN_RECORD_STATUS_UPDATE_ERROR.getErrCode(),
                    BizErrorCodeEnum.REMIND_PLAN_RECORD_STATUS_UPDATE_ERROR.getErrDesc());
            return null;
        }).when(transactionHelper).execute(any(Runnable.class), any(), any());

        ConsumeResult result = consumer.consume(messageView, message);

        assertEquals(ConsumeResult.FAILURE, result);
    }
}
