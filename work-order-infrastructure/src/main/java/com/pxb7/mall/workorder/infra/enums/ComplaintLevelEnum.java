package com.pxb7.mall.workorder.infra.enums;

import lombok.Getter;
import lombok.ToString;

/**
 * 投诉级别
 * <AUTHOR>
 */
@Getter
@ToString
public enum ComplaintLevelEnum {

    /**
     * 投诉级别：1:一级，2:二级，3:三级，4:四级，5:五级，6:六级
     */
    ONE(1, "一级"),
    TWO(2, "二级"),
    THREE(3,  "三级"),
    FOUR(4, "四级"),
    FIVE(5, "五级"),
    SIX(6,  "六级");

    private final Integer code;
    private final String desc;

    ComplaintLevelEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ComplaintLevelEnum getByCode(Integer code) {
        for (ComplaintLevelEnum complaintLevelEnum : ComplaintLevelEnum.values()) {
            if (complaintLevelEnum.getCode().equals(code)) {
                return complaintLevelEnum;
            }
        }
        return null;
    }

}
