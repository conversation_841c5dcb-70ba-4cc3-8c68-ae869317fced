package com.pxb7.mall.workorder.infra.repository.db;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pxb7.mall.workorder.infra.model.RemindPlanOperateRecordReqPO;
import com.pxb7.mall.workorder.infra.repository.db.entity.RemindPlanOperateRecord;

/**
 * 预警计划操作记录表(RemindPlanOperateRecord)表服务接口
 *
 * <AUTHOR>
 * @since 2025-03-31 20:36:21
 */
public interface RemindPlanOperateRecordRepository extends IService<RemindPlanOperateRecord> {

    boolean insert(RemindPlanOperateRecordReqPO.AddPO param);

    boolean update(RemindPlanOperateRecordReqPO.UpdatePO param);

    boolean deleteById(RemindPlanOperateRecordReqPO.DelPO param);

    RemindPlanOperateRecord findById(Long id);

    List<RemindPlanOperateRecord> list(RemindPlanOperateRecordReqPO.SearchPO param);

    Page<RemindPlanOperateRecord> page(RemindPlanOperateRecordReqPO.PagePO param);

    boolean saveBatch(List<RemindPlanOperateRecordReqPO.AddPO> params);

}

