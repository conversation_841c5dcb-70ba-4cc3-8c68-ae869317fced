package com.pxb7.mall.workorder.app.mapping;

import com.pxb7.mall.workorder.app.model.RemindPlanOperateRecordReqDTO;
import com.pxb7.mall.workorder.app.model.RemindPlanOperateRecordRespDTO;
import com.pxb7.mall.workorder.domain.model.RemindPlanOperateRecordReqBO;
import com.pxb7.mall.workorder.domain.model.RemindPlanOperateRecordRespBO;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;


@Mapper
public interface RemindPlanOperateRecordAppMapping {

    RemindPlanOperateRecordAppMapping INSTANCE = Mappers.getMapper(RemindPlanOperateRecordAppMapping.class);


    RemindPlanOperateRecordReqBO.AddBO remindPlanOperateRecordDTO2AddBO(RemindPlanOperateRecordReqDTO.AddDTO source);

    RemindPlanOperateRecordReqBO.UpdateBO remindPlanOperateRecordDTO2UpdateBO(RemindPlanOperateRecordReqDTO.UpdateDTO source);

    RemindPlanOperateRecordReqBO.DelBO remindPlanOperateRecordDTO2DelBO(RemindPlanOperateRecordReqDTO.DelDTO source);

    RemindPlanOperateRecordReqBO.SearchBO remindPlanOperateRecordDTO2SearchBO(RemindPlanOperateRecordReqDTO.SearchDTO source);

    RemindPlanOperateRecordReqBO.PageBO remindPlanOperateRecordDTO2PageBO(RemindPlanOperateRecordReqDTO.PageDTO source);

    RemindPlanOperateRecordRespDTO.DetailDTO remindPlanOperateRecordBO2DetailDTO(RemindPlanOperateRecordRespBO.DetailBO source);

    List<RemindPlanOperateRecordRespDTO.DetailDTO> remindPlanOperateRecordBO2ListDTO(List<RemindPlanOperateRecordRespBO.DetailBO> source);

    Page<RemindPlanOperateRecordRespDTO.DetailDTO> remindPlanOperateRecordBO2PageDTO(Page<RemindPlanOperateRecordRespBO.DetailBO> source);

}


