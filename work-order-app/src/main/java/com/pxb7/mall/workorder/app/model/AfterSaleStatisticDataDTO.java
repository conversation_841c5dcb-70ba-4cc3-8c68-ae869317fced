package com.pxb7.mall.workorder.app.model;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Getter
@Setter
@Accessors(chain = true)
@ToString
public class AfterSaleStatisticDataDTO {

    /**
     * 日期
     */
    private String createDate;

    /**
     * 找回/纠纷工单数
     */
    private Integer workOrderCnt;

    /**
     * 已完结工单数
     */
    private Integer completeWorkOrderCnt;

    /**
     * 处理中工单数
     */
    private Integer processingWorkOrderCnt;

    /**
     * 即将超时数
     */
    private Integer timeoutingWorkOrderCnt;

    /**
     * 已超时数
     */
    private Integer timeoutedWorkOrderCnt;

    /**
     * 超时率（已超时数/找回或纠纷工单数）
     */
    private BigDecimal timeoutRate;

}
