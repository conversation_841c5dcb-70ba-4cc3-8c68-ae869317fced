package com.pxb7.mall.workorder.app.service;


import java.util.List;

import com.pxb7.mall.workorder.app.mapping.RemindPlanOperateRecordAppMapping;
import com.pxb7.mall.workorder.app.model.RemindPlanOperateRecordReqDTO;
import com.pxb7.mall.workorder.app.model.RemindPlanOperateRecordRespDTO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import com.pxb7.mall.workorder.domain.model.RemindPlanOperateRecordReqBO;
import com.pxb7.mall.workorder.domain.model.RemindPlanOperateRecordRespBO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pxb7.mall.workorder.domain.service.RemindPlanOperateRecordDomainService;


/**
 * 预警计划操作记录表app服务
 *
 * <AUTHOR>
 * @since 2025-03-31 20:37:07
 */
@Service
public class RemindPlanOperateRecordAppService {

    @Resource
    private RemindPlanOperateRecordDomainService remindPlanOperateRecordDomainService;

    public boolean insert(RemindPlanOperateRecordReqDTO.AddDTO param) {
        RemindPlanOperateRecordReqBO.AddBO addBO = RemindPlanOperateRecordAppMapping.INSTANCE.remindPlanOperateRecordDTO2AddBO(param);
        return remindPlanOperateRecordDomainService.insert(addBO);
    }

    public boolean update(RemindPlanOperateRecordReqDTO.UpdateDTO param) {
        RemindPlanOperateRecordReqBO.UpdateBO updateBO = RemindPlanOperateRecordAppMapping.INSTANCE.remindPlanOperateRecordDTO2UpdateBO(param);
        return remindPlanOperateRecordDomainService.update(updateBO);
    }

    public boolean deleteById(RemindPlanOperateRecordReqDTO.DelDTO param) {
        RemindPlanOperateRecordReqBO.DelBO delBO = RemindPlanOperateRecordAppMapping.INSTANCE.remindPlanOperateRecordDTO2DelBO(param);
        return remindPlanOperateRecordDomainService.deleteById(delBO);
    }

    public RemindPlanOperateRecordRespDTO.DetailDTO findById(Long id) {
        RemindPlanOperateRecordRespBO.DetailBO detailBO = remindPlanOperateRecordDomainService.findById(id);
        return RemindPlanOperateRecordAppMapping.INSTANCE.remindPlanOperateRecordBO2DetailDTO(detailBO);
    }

    public List<RemindPlanOperateRecordRespDTO.DetailDTO> list(RemindPlanOperateRecordReqDTO.SearchDTO param) {
        RemindPlanOperateRecordReqBO.SearchBO searchBO = RemindPlanOperateRecordAppMapping.INSTANCE.remindPlanOperateRecordDTO2SearchBO(param);
        List<RemindPlanOperateRecordRespBO.DetailBO> list = remindPlanOperateRecordDomainService.list(searchBO);
        return RemindPlanOperateRecordAppMapping.INSTANCE.remindPlanOperateRecordBO2ListDTO(list);
    }

    public Page<RemindPlanOperateRecordRespDTO.DetailDTO> page(RemindPlanOperateRecordReqDTO.PageDTO param) {
        RemindPlanOperateRecordReqBO.PageBO pageBO = RemindPlanOperateRecordAppMapping.INSTANCE.remindPlanOperateRecordDTO2PageBO(param);
        Page<RemindPlanOperateRecordRespBO.DetailBO> page = remindPlanOperateRecordDomainService.page(pageBO);
        return RemindPlanOperateRecordAppMapping.INSTANCE.remindPlanOperateRecordBO2PageDTO(page);
    }

}

