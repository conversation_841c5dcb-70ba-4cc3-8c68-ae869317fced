package com.pxb7.mall.workorder.adapter.consumer;

import com.alibaba.fastjson.JSON;
import com.pxb7.mall.components.rocketmq.RocketMQListenerExt;
import com.pxb7.mall.trade.order.client.message.OrderItemStatusChangeMQDTO;
import com.pxb7.mall.workorder.app.service.RemindDeliveryProductAppService;
import com.pxb7.mall.workorder.infra.constant.RMQConstant;
import com.pxb7.mall.workorder.infra.enums.OrderItemStatusEnum;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.annotation.RocketMQMessageListener;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.springframework.stereotype.Service;

/**
 * 监听订单状态变更消息
 * <AUTHOR>
 */
@Slf4j
@Service
@RocketMQMessageListener(
        consumerGroup = RMQConstant.ORDER_STATUS_CHANGE_GROUP,
        topic = RMQConstant.ORDER_STATUS_CHANGE_TOPIC,
        tag = RMQConstant.ALL_TAG)
public class OrderStatusMessageConsumer implements RocketMQListenerExt<OrderItemStatusChangeMQDTO> {

    @Resource
    private RemindDeliveryProductAppService remindDeliveryProductAppService;

    @Override
    public ConsumeResult consume(MessageView messageView, OrderItemStatusChangeMQDTO orderStatusChangeDTO) {
        log.info("监听到订单状态变更消息: messageId = {},message = {}", messageView.getMessageId(), JSON.toJSONString(orderStatusChangeDTO));
        String orderId = orderStatusChangeDTO.getOrderId();
        String orderItemId = orderStatusChangeDTO.getOrderItemId();
        try {
            if (StringUtils.isBlank(orderId) || StringUtils.isBlank(orderItemId)) {
                return ConsumeResult.SUCCESS;
            }
            if (OrderItemStatusEnum.INIT.eq(orderStatusChangeDTO.getOrderItemStatus())
                    ||  OrderItemStatusEnum.WAIT_PAY.eq(orderStatusChangeDTO.getOrderItemStatus())
                    ||  OrderItemStatusEnum.PER_SETTLEMENT.eq(orderStatusChangeDTO.getOrderItemStatus())) {
                return ConsumeResult.SUCCESS;
            }
            //监听订单状态消息
            if (OrderItemStatusEnum.DEALING.eq(orderStatusChangeDTO.getOrderItemStatus())) {
                //生成预警执行计划
                remindDeliveryProductAppService.generateRemindPlan(orderStatusChangeDTO);
            } else {
                //订单状态变更，修改预警执行计划状态
                remindDeliveryProductAppService.changeRemindPlanStatus(orderStatusChangeDTO);
            }
            return ConsumeResult.SUCCESS;
        } catch (Exception e) {
            log.error("error in consumed from order_status_change_topic: orderId = {}", orderId, e);
            return ConsumeResult.FAILURE;
        }
    }

}

