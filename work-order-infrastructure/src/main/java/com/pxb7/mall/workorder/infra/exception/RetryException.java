
package com.pxb7.mall.workorder.infra.exception;

/**
 * Function: 重试异常
 *
 * <AUTHOR>
 */
public class RetryException extends RuntimeException {

    private static final long serialVersionUID = 7050465979712223031L;

    private String code = "-1";
    private String message = "异常重试";

    public RetryException() {

    }

    public RetryException(String message) {
        super(message);
        this.message = message;
    }

    public RetryException(String code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    @Override
    public String getMessage() {
        return message;
    }

}