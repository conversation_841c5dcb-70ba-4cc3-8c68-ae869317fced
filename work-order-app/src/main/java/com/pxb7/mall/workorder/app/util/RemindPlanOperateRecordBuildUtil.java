package com.pxb7.mall.workorder.app.util;

import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;


import com.pxb7.mall.auth.dto.AdminUserDTO;
import com.pxb7.mall.workorder.domain.model.*;
import com.pxb7.mall.workorder.infra.enums.RemindPlanDataTypeEnum;
import com.pxb7.mall.workorder.infra.enums.RemindPlanOperateTypeEnum;
import org.apache.skywalking.apm.toolkit.trace.TraceContext;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

public class RemindPlanOperateRecordBuildUtil {

    public static List<RemindPlanOperateRecordBO> buildAddOperateRecordList(RemindPlanBO remindPlanBO,
                                                                            AdminUserDTO adminUser) {

        ArrayList<RemindPlanOperateRecordBO> recordBOS = new ArrayList<>();

        Long remindPlanId = remindPlanBO.getId();

        RemindPlanBO newData = buildOnlyRemindAndSubPlan(remindPlanBO);
        String newContent = JSON.toJSONString(newData);
        RemindPlanOperateRecordBO operateRecordBO = new RemindPlanOperateRecordBO();
        operateRecordBO.setRemindPlanId(remindPlanId);
        operateRecordBO.setOptType(RemindPlanOperateTypeEnum.ADD.getValue());
        operateRecordBO.setDataType(RemindPlanDataTypeEnum.PLAN_AND_SUBPLAN.getValue());
        operateRecordBO.setOriginContent("");
        operateRecordBO.setNewContent(newContent);
        operateRecordBO.setTraceId(TraceContext.traceId());
        operateRecordBO.setOptUserId(adminUser.getUserId());
        recordBOS.add(operateRecordBO);

        List<RemindPlanGameConfigBO> allRemindPlanGameConfigs = remindPlanBO.getAllRemindPlanGameConfigs();
        List<List<RemindPlanGameConfigBO>> partition = Lists.partition(allRemindPlanGameConfigs, 50);
        for (List<RemindPlanGameConfigBO> gameConfigBOList : partition) {
            RemindPlanOperateRecordBO gameConfigOperateRecord = new RemindPlanOperateRecordBO();
            gameConfigOperateRecord.setRemindPlanId(remindPlanId);
            gameConfigOperateRecord.setOptType(RemindPlanOperateTypeEnum.ADD.getValue());
            gameConfigOperateRecord.setDataType(RemindPlanDataTypeEnum.GAME_CONFIG.getValue());
            gameConfigOperateRecord.setOriginContent("");
            gameConfigOperateRecord.setNewContent(JSON.toJSONString(gameConfigBOList));
            gameConfigOperateRecord.setTraceId(TraceContext.traceId());
            gameConfigOperateRecord.setOptUserId(adminUser.getUserId());
            recordBOS.add(gameConfigOperateRecord);
        }

        List<RemindPlanRuleBO> remindPlanRules = remindPlanBO.getRemindPlanRules();
        List<List<RemindPlanRuleBO>> partition1 = Lists.partition(remindPlanRules, 10);
        for (List<RemindPlanRuleBO> ruleBOList : partition1) {
            RemindPlanOperateRecordBO ruleOperateRecord = new RemindPlanOperateRecordBO();
            ruleOperateRecord.setRemindPlanId(remindPlanId);
            ruleOperateRecord.setOptType(RemindPlanOperateTypeEnum.ADD.getValue());
            ruleOperateRecord.setDataType(RemindPlanDataTypeEnum.PLAN_RULE.getValue());
            ruleOperateRecord.setOriginContent("");
            ruleOperateRecord.setNewContent(JSON.toJSONString(ruleBOList));
            ruleOperateRecord.setTraceId(TraceContext.traceId());
            ruleOperateRecord.setOptUserId(adminUser.getUserId());
            recordBOS.add(ruleOperateRecord);
        }
        return recordBOS;
    }

    private static RemindPlanBO buildOnlyRemindAndSubPlan(RemindPlanBO remindPlanBO) {
        Long remindPlanId = remindPlanBO.getId();
        RemindPlanBO onlyRemindPlanAndSubPlan = new RemindPlanBO();
        onlyRemindPlanAndSubPlan.setId(remindPlanId);
        onlyRemindPlanAndSubPlan.setPlanName(remindPlanBO.getPlanName());
        onlyRemindPlanAndSubPlan.setServiceType(remindPlanBO.getServiceType());
        onlyRemindPlanAndSubPlan.setNotDisturbPeriod(remindPlanBO.getNotDisturbPeriod());

        onlyRemindPlanAndSubPlan.setRemindPlanRules(Lists.newArrayList());
        onlyRemindPlanAndSubPlan.setCreateUserId(remindPlanBO.getCreateUserId());
        onlyRemindPlanAndSubPlan.setUpdateUserId(remindPlanBO.getUpdateUserId());
        onlyRemindPlanAndSubPlan.setCreateTime(remindPlanBO.getCreateTime());
        onlyRemindPlanAndSubPlan.setUpdateTime(remindPlanBO.getUpdateTime());
        List<RemindSubPlanBO> onlyRemindSubPlans = new ArrayList<>();
        for (RemindSubPlanBO remindSubPlanBO : remindPlanBO.getRemindSubPlans()) {
            RemindSubPlanBO onlyRemindSubPlan = new RemindSubPlanBO();
            onlyRemindSubPlan.setId(remindSubPlanBO.getId());
            onlyRemindSubPlan.setRemindPlanId(remindSubPlanBO.getRemindPlanId());
            onlyRemindSubPlan.setBusinessType(remindSubPlanBO.getBusinessType());
            onlyRemindSubPlan.setWorkOrderStatus(remindSubPlanBO.getWorkOrderStatus());
            onlyRemindSubPlan.setOnShelfType(remindSubPlanBO.getOnShelfType());
            onlyRemindSubPlan.setCreateUserId(remindSubPlanBO.getCreateUserId());
            onlyRemindSubPlan.setUpdateUserId(remindSubPlanBO.getUpdateUserId());
            // 游戏配置 字段过大，暂不记录
            onlyRemindSubPlan.setRemindPlanGameConfigs(Lists.newArrayList());
            onlyRemindSubPlans.add(onlyRemindSubPlan);
        }
        onlyRemindPlanAndSubPlan.setRemindSubPlans(onlyRemindSubPlans);
        return onlyRemindPlanAndSubPlan;
    }

    public static List<RemindPlanOperateRecordBO> buildUpdateOperateRecordList(
            RemindPlanUpdateContextBO remindPlanUpdateContextBO, AdminUserDTO adminUser) {
        RemindPlanBO originRemindPlanBO = remindPlanUpdateContextBO.getOriginRemindPlan();
        RemindPlanBO remindPlanBO = remindPlanUpdateContextBO.getRemindPlan();
        RemindPlanRuleRefreshBO remindPlanRuleRefreshBO = remindPlanUpdateContextBO.getRemindPlanRuleRefresh();
        RemindPlanGameConfigRefreshBO remindPlanGameConfigRefreshBO = remindPlanUpdateContextBO.getRemindPlanGameConfigRefresh();

        Long remindPlanId = originRemindPlanBO.getId();

        ArrayList<RemindPlanOperateRecordBO> recordBOS = new ArrayList<>();

        RemindPlanBO originData = buildOnlyRemindAndSubPlan(originRemindPlanBO);
        RemindPlanBO newData = buildOnlyRemindAndSubPlan(remindPlanBO);
        RemindPlanOperateRecordBO operateRecordBO = new RemindPlanOperateRecordBO();
        operateRecordBO.setRemindPlanId(remindPlanId);
        operateRecordBO.setOptType(RemindPlanOperateTypeEnum.UPDATE.getValue());
        operateRecordBO.setDataType(RemindPlanDataTypeEnum.PLAN_AND_SUBPLAN.getValue());
        operateRecordBO.setOriginContent(JSON.toJSONString(originData));
        operateRecordBO.setNewContent(JSON.toJSONString(newData));
        operateRecordBO.setTraceId(TraceContext.traceId());
        operateRecordBO.setOptUserId(adminUser.getUserId());
        recordBOS.add(operateRecordBO);

        List<RemindPlanGameConfigBO> needAddPlanGameConfigs = remindPlanGameConfigRefreshBO.getNeedAddPlanGameConfigs();
        if (!CollectionUtils.isEmpty(needAddPlanGameConfigs)) {
            List<List<RemindPlanGameConfigBO>> partition = Lists.partition(needAddPlanGameConfigs, 50);
            for (List<RemindPlanGameConfigBO> gameConfigBOList : partition) {
                RemindPlanOperateRecordBO gameConfigOperateRecord = new RemindPlanOperateRecordBO();
                gameConfigOperateRecord.setRemindPlanId(remindPlanId);
                gameConfigOperateRecord.setOptType(RemindPlanOperateTypeEnum.REFRESH_ADD.getValue());
                gameConfigOperateRecord.setDataType(RemindPlanDataTypeEnum.GAME_CONFIG.getValue());
                gameConfigOperateRecord.setOriginContent("");
                gameConfigOperateRecord.setNewContent(JSON.toJSONString(gameConfigBOList));
                gameConfigOperateRecord.setTraceId(TraceContext.traceId());
                gameConfigOperateRecord.setOptUserId(adminUser.getUserId());
                recordBOS.add(gameConfigOperateRecord);
            }
        }

        List<RemindPlanGameConfigBO> needDeletePlanGameConfigs =
            remindPlanGameConfigRefreshBO.getNeedDeletePlanGameConfigs();
        if (!CollectionUtils.isEmpty(needDeletePlanGameConfigs)) {
            List<List<RemindPlanGameConfigBO>> partition = Lists.partition(needDeletePlanGameConfigs, 50);
            for (List<RemindPlanGameConfigBO> gameConfigBOList : partition) {
                RemindPlanOperateRecordBO gameConfigOperateRecord = new RemindPlanOperateRecordBO();
                gameConfigOperateRecord.setRemindPlanId(remindPlanId);
                gameConfigOperateRecord.setOptType(RemindPlanOperateTypeEnum.REFRESH_DELETE.getValue());
                gameConfigOperateRecord.setDataType(RemindPlanDataTypeEnum.GAME_CONFIG.getValue());
                gameConfigOperateRecord.setOriginContent(JSON.toJSONString(gameConfigBOList));
                gameConfigOperateRecord.setNewContent("");
                gameConfigOperateRecord.setTraceId(TraceContext.traceId());
                gameConfigOperateRecord.setOptUserId(adminUser.getUserId());
                recordBOS.add(gameConfigOperateRecord);
            }
        }
        List<RemindPlanGameConfigBO> needUpdatePlanGameConfigs =
            remindPlanGameConfigRefreshBO.getNeedUpdatePlanGameConfigs();
        if (!CollectionUtils.isEmpty(needUpdatePlanGameConfigs)) {
            List<List<RemindPlanGameConfigBO>> partition = Lists.partition(needDeletePlanGameConfigs, 50);
            for (List<RemindPlanGameConfigBO> gameConfigBOList : partition) {
                List<Long> updateIds = gameConfigBOList.stream().map(RemindPlanGameConfigBO::getId).toList();
                List<RemindPlanGameConfigBO> originGameConfigList =
                    originRemindPlanBO.getAllRemindPlanGameConfigs().stream().filter(a -> updateIds.contains(a.getId()))
                        .toList();
                RemindPlanOperateRecordBO gameConfigOperateRecord = new RemindPlanOperateRecordBO();
                gameConfigOperateRecord.setRemindPlanId(remindPlanId);
                gameConfigOperateRecord.setOptType(RemindPlanOperateTypeEnum.REFRESH_DELETE.getValue());
                gameConfigOperateRecord.setDataType(RemindPlanDataTypeEnum.GAME_CONFIG.getValue());
                gameConfigOperateRecord.setOriginContent(JSON.toJSONString(originGameConfigList));
                gameConfigOperateRecord.setNewContent(JSON.toJSONString(gameConfigBOList));
                gameConfigOperateRecord.setTraceId(TraceContext.traceId());
                gameConfigOperateRecord.setOptUserId(adminUser.getUserId());
                recordBOS.add(gameConfigOperateRecord);
            }
        }

        List<RemindPlanRuleBO> needAddPlanRules = remindPlanRuleRefreshBO.getNeedAddPlanRules();
        if (!CollectionUtils.isEmpty(needAddPlanRules)) {
            List<List<RemindPlanRuleBO>> partition1 = Lists.partition(needAddPlanRules, 10);
            for (List<RemindPlanRuleBO> ruleBOList : partition1) {
                RemindPlanOperateRecordBO ruleOperateRecord = new RemindPlanOperateRecordBO();
                ruleOperateRecord.setRemindPlanId(remindPlanId);
                ruleOperateRecord.setOptType(RemindPlanOperateTypeEnum.REFRESH_ADD.getValue());
                ruleOperateRecord.setDataType(RemindPlanDataTypeEnum.PLAN_RULE.getValue());
                ruleOperateRecord.setOriginContent("");
                ruleOperateRecord.setNewContent(JSON.toJSONString(ruleBOList));
                ruleOperateRecord.setTraceId(TraceContext.traceId());
                ruleOperateRecord.setOptUserId(adminUser.getUserId());
                recordBOS.add(ruleOperateRecord);
            }
        }
        List<RemindPlanRuleBO> needDeletePlanRules = remindPlanRuleRefreshBO.getNeedDeletePlanRules();
        if (!CollectionUtils.isEmpty(needDeletePlanRules)) {
            List<List<RemindPlanRuleBO>> partitionList = Lists.partition(needDeletePlanRules, 10);
            for (List<RemindPlanRuleBO> ruleBOList : partitionList) {
                RemindPlanOperateRecordBO ruleOperateRecord = new RemindPlanOperateRecordBO();
                ruleOperateRecord.setRemindPlanId(remindPlanId);
                ruleOperateRecord.setOptType(RemindPlanOperateTypeEnum.REFRESH_DELETE.getValue());
                ruleOperateRecord.setDataType(RemindPlanDataTypeEnum.PLAN_RULE.getValue());
                ruleOperateRecord.setOriginContent(JSON.toJSONString(ruleBOList));
                ruleOperateRecord.setNewContent("");
                ruleOperateRecord.setTraceId(TraceContext.traceId());
                ruleOperateRecord.setOptUserId(adminUser.getUserId());
                recordBOS.add(ruleOperateRecord);
            }
        }
        List<RemindPlanRuleBO> needUpdatePlanRules = remindPlanRuleRefreshBO.getNeedUpdatePlanRules();
        if (!CollectionUtils.isEmpty(needUpdatePlanRules)) {
            List<List<RemindPlanRuleBO>> partitionList = Lists.partition(needUpdatePlanRules, 10);
            for (List<RemindPlanRuleBO> ruleBOList : partitionList) {
                List<Long> updateIds = ruleBOList.stream().map(RemindPlanRuleBO::getId).toList();
                List<RemindPlanRuleBO> originRuleList =
                    originRemindPlanBO.getRemindPlanRules().stream().filter(a -> updateIds.contains(a.getId()))
                        .toList();

                RemindPlanOperateRecordBO ruleOperateRecord = new RemindPlanOperateRecordBO();
                ruleOperateRecord.setRemindPlanId(remindPlanId);
                ruleOperateRecord.setOptType(RemindPlanOperateTypeEnum.REFRESH_UPDATE.getValue());
                ruleOperateRecord.setDataType(RemindPlanDataTypeEnum.PLAN_RULE.getValue());
                ruleOperateRecord.setOriginContent(JSON.toJSONString(originRuleList));
                ruleOperateRecord.setNewContent(JSON.toJSONString(ruleBOList));
                ruleOperateRecord.setTraceId(TraceContext.traceId());
                ruleOperateRecord.setOptUserId(adminUser.getUserId());
                recordBOS.add(ruleOperateRecord);
            }
        }
        return recordBOS;
    }


    public static List<RemindPlanOperateRecordBO> buildDeleteAllOperateRecordList(
        RemindPlanBO remindPlanBO, AdminUserDTO adminUser) {

        Long remindPlanId = remindPlanBO.getId();

        ArrayList<RemindPlanOperateRecordBO> recordBOS = new ArrayList<>();

        RemindPlanBO originData = buildOnlyRemindAndSubPlan(remindPlanBO);
        RemindPlanOperateRecordBO operateRecordBO = new RemindPlanOperateRecordBO();
        operateRecordBO.setRemindPlanId(remindPlanId);
        operateRecordBO.setOptType(RemindPlanOperateTypeEnum.DELETE.getValue());
        operateRecordBO.setDataType(RemindPlanDataTypeEnum.PLAN_AND_SUBPLAN.getValue());
        operateRecordBO.setOriginContent(JSON.toJSONString(originData));
        operateRecordBO.setNewContent("");
        operateRecordBO.setTraceId(TraceContext.traceId());
        operateRecordBO.setOptUserId(adminUser.getUserId());
        recordBOS.add(operateRecordBO);

        List<RemindPlanGameConfigBO> needDeletePlanGameConfigs = remindPlanBO.getAllRemindPlanGameConfigs();
        if (!CollectionUtils.isEmpty(needDeletePlanGameConfigs)) {
            List<List<RemindPlanGameConfigBO>> partition = Lists.partition(needDeletePlanGameConfigs, 50);
            for (List<RemindPlanGameConfigBO> gameConfigBOList : partition) {
                RemindPlanOperateRecordBO gameConfigOperateRecord = new RemindPlanOperateRecordBO();
                gameConfigOperateRecord.setRemindPlanId(remindPlanId);
                gameConfigOperateRecord.setOptType(RemindPlanOperateTypeEnum.DELETE.getValue());
                gameConfigOperateRecord.setDataType(RemindPlanDataTypeEnum.GAME_CONFIG.getValue());
                gameConfigOperateRecord.setOriginContent(JSON.toJSONString(gameConfigBOList));
                gameConfigOperateRecord.setNewContent("");
                gameConfigOperateRecord.setTraceId(TraceContext.traceId());
                gameConfigOperateRecord.setOptUserId(adminUser.getUserId());
                recordBOS.add(gameConfigOperateRecord);
            }
        }
        List<RemindPlanRuleBO> needDeletePlanRules = remindPlanBO.getRemindPlanRules();
        if (!CollectionUtils.isEmpty(needDeletePlanRules)) {
            List<List<RemindPlanRuleBO>> partitionList = Lists.partition(needDeletePlanRules, 10);
            for (List<RemindPlanRuleBO> ruleBOList : partitionList) {
                RemindPlanOperateRecordBO ruleOperateRecord = new RemindPlanOperateRecordBO();
                ruleOperateRecord.setRemindPlanId(remindPlanId);
                ruleOperateRecord.setOptType(RemindPlanOperateTypeEnum.DELETE.getValue());
                ruleOperateRecord.setDataType(RemindPlanDataTypeEnum.PLAN_RULE.getValue());
                ruleOperateRecord.setOriginContent(JSON.toJSONString(ruleBOList));
                ruleOperateRecord.setNewContent("");
                ruleOperateRecord.setTraceId(TraceContext.traceId());
                ruleOperateRecord.setOptUserId(adminUser.getUserId());
                recordBOS.add(ruleOperateRecord);
            }
        }

        return recordBOS;
    }



}
