package com.pxb7.mall.workorder.domain.model;

import java.util.Date;
import java.time.*;
import java.math.BigDecimal;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.ToString;


/**
 * 预警计划操作记录表(RemindPlanOperateRecord)实体类
 *
 * <AUTHOR>
 * @since 2025-03-31 20:36:47
 */
public class RemindPlanOperateRecordReqBO {

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class AddBO {


        private String remindPlanId;


        private Integer optType;


        private Integer dataType;


        private String originContent;


        private String newContent;


        private String traceId;


        private String optUserId;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class UpdateBO {


        private Long id;


        private String remindPlanId;


        private Integer optType;


        private Integer dataType;


        private String originContent;


        private String newContent;


        private String traceId;


        private String optUserId;


    }


    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class DelBO {
        private Long id;
    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class SearchBO {

        private String remindPlanId;


        private Integer optType;


        private Integer dataType;


        private String originContent;


        private String newContent;


        private String traceId;


        private String optUserId;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class PageBO {


        private String remindPlanId;


        private Integer optType;


        private Integer dataType;


        private String originContent;


        private String newContent;


        private String traceId;


        private String optUserId;


        /**
         * 页码，从1开始
         */
        private Integer pageIndex;
        /**
         * 每页数量
         */
        private Integer pageSize;

    }

}

