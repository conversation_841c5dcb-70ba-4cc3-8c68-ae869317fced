package com.pxb7.mall.workorder.app.service;


import java.util.List;

import com.pxb7.mall.workorder.app.mapping.RemindPlanRuleAppMapping;
import com.pxb7.mall.workorder.app.model.PlanRuleDTO;
import com.pxb7.mall.workorder.app.model.RemindPlanRuleReqDTO;
import com.pxb7.mall.workorder.app.model.RemindPlanRuleRespDTO;
import com.pxb7.mall.workorder.domain.model.RemindPlanRuleBO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import com.pxb7.mall.workorder.domain.model.RemindPlanRuleReqBO;
import com.pxb7.mall.workorder.domain.model.RemindPlanRuleRespBO;
import com.pxb7.mall.workorder.domain.service.RemindPlanRuleDomainService;


/**
 * 预警计划提醒规则配置app服务
 *
 * <AUTHOR>
 * @since 2025-03-24 21:12:18
 */
@Service
public class RemindPlanRuleAppService {

    @Resource
    private RemindPlanRuleDomainService remindPlanRuleDomainService;

    public boolean insert(RemindPlanRuleReqDTO.AddDTO param) {
        RemindPlanRuleBO addBO = RemindPlanRuleAppMapping.INSTANCE.remindPlanRuleDTO2AddBO(param);
        return remindPlanRuleDomainService.insert(addBO);
    }

    public boolean update(RemindPlanRuleReqDTO.UpdateDTO param) {
        RemindPlanRuleBO updateBO = RemindPlanRuleAppMapping.INSTANCE.remindPlanRuleDTO2UpdateBO(param);
        return remindPlanRuleDomainService.update(updateBO);
    }

    public boolean deleteById(RemindPlanRuleReqDTO.DelDTO param) {
        RemindPlanRuleReqBO.DelBO delBO = RemindPlanRuleAppMapping.INSTANCE.remindPlanRuleDTO2DelBO(param);
        return remindPlanRuleDomainService.deleteById(delBO);
    }

    public RemindPlanRuleRespDTO.DetailDTO findById(Long id) {
        RemindPlanRuleRespBO.DetailBO detailBO = remindPlanRuleDomainService.findById(id);
        return RemindPlanRuleAppMapping.INSTANCE.remindPlanRuleBO2DetailDTO(detailBO);
    }

    public List<PlanRuleDTO> list(RemindPlanRuleReqDTO.SearchDTO param) {
        RemindPlanRuleReqBO.SearchBO searchBO = RemindPlanRuleAppMapping.INSTANCE.remindPlanRuleDTO2SearchBO(param);
        List<RemindPlanRuleBO> list = remindPlanRuleDomainService.list(searchBO);
        return RemindPlanRuleAppMapping.INSTANCE.remindPlanRuleBO2ListDTO(list);
    }
}

