package com.pxb7.mall.workorder.domain.model;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@Accessors(chain = true)
@ToString
public class RemindPlanBO {

    private Long id;


    /**
     * 预警计划名称
     */
    private String planName;

    /**
     * 业务类型
     */
    private Integer serviceType;

    /**
     * 免打扰时间段 {"startTime":"22:00","endTime":"08:00"}
     */
    private NotDisturbPeriodBO notDisturbPeriod;



    /**
     * 预警规则
     */
    private List<RemindPlanRuleBO> remindPlanRules;

    /**
     * 提醒子计划
     */
    private List<RemindSubPlanBO> remindSubPlans;


    private String createUserId;


    private String updateUserId;

    private LocalDateTime createTime;

    /**
     *  最近编辑时间
     */
    private LocalDateTime updateTime;


    public List<RemindPlanGameConfigBO> getAllRemindPlanGameConfigs() {
        List<RemindPlanGameConfigBO> allRemindPlanGameConfigs = new ArrayList<>();
        if (remindSubPlans != null) {
            for (RemindSubPlanBO remindSubPlanBO : remindSubPlans) {
                if (remindSubPlanBO.getRemindPlanGameConfigs() == null) {
                    continue;
                }
                allRemindPlanGameConfigs.addAll(remindSubPlanBO.getRemindPlanGameConfigs());
            }
        }
        return allRemindPlanGameConfigs;
    }

    public void putRemindSubPlan(RemindSubPlanBO remindSubPlanBO) {
        if (remindSubPlans == null) {
            remindSubPlans = new ArrayList<>();
        }
        remindSubPlans.add(remindSubPlanBO);
    }

    public RemindSubPlanBO tryGetRemindSubPlan(Long remindSubPlanId) {

        if (remindSubPlans == null || remindSubPlanId == null) {
            return null;
        }
        return remindSubPlans.stream().filter(remindSubPlanBO -> remindSubPlanId.equals(remindSubPlanBO.getId()))
            .findFirst().orElse(null);
    }

    public void putRemindPlanRule(RemindPlanRuleBO remindPlanRuleBO) {
        if (remindPlanRules == null) {
            remindPlanRules = new ArrayList<>();
        }
        remindPlanRules.add(remindPlanRuleBO);
    }


}
