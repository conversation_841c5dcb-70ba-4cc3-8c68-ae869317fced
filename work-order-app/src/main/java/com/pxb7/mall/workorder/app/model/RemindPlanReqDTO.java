package com.pxb7.mall.workorder.app.model;

import java.util.List;

import com.pxb7.mall.workorder.infra.enums.RemindPlanBusinessTypeEnum;
import com.pxb7.mall.workorder.infra.enums.RemindPlanOnShelfTypeEnum;
import com.pxb7.mall.workorder.infra.enums.RemindPlanServiceTypeEnum;
import com.pxb7.mall.workorder.infra.enums.RemindPlanWorkOrderStatusEnum;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Min;
import lombok.ToString;

/**
 * 提醒服务预警计划(RemindPlan)实体类
 *
 * <AUTHOR>
 * @since 2025-03-24 21:12:15
 */
public class RemindPlanReqDTO {

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class AddDTO {


        /**
         * 预警计划名称
         */
        @NotBlank(message = "预警计划名称不能为空")
        private String planName;

        /**
         * 业务类型 1:账号交付服务 2:商品工单服务
         * @see RemindPlanServiceTypeEnum
         */
        @NotNull(message = "业务类型不能为空")
        private Integer serviceType;


        /**
         * 交易类型，1:代售，2:中介，服务类型为账号交付有值
         * @see RemindPlanBusinessTypeEnum
         */
        private List<Integer> businessTypes;

        /**
         *  工单状态：1:待接单，2:已接单，3:待跟进，服务类型为商品工单有值
         * @see RemindPlanWorkOrderStatusEnum
         */
        private List<Integer> workOrderStatuses;

        /**
         * 订单来源(会员类型)：1 散户工单 2 号商工单 3 3A工单，服务类型为商品工单有值
         */
        private List<Integer> memberships;

        /**
         * 上架方式：1:官方截图，2:自主截图，服务类型为商品工单且 不为“待接单”时有值
         * @see RemindPlanOnShelfTypeEnum
         */
        @Deprecated
        private List<Integer> onShelfTypes;
        /**
         * 预警游戏配置
         */
        @Valid
        @NotEmpty(message = "预警设置不能为空")
        private List<PlanGameConfigDTO> planGameConfigs;

        /**
         * 预警规则
         */
        @Valid
        @NotEmpty(message = "提醒规则不能为空")
        private List<PlanRuleDTO> planRules;


        /**
         * 免打扰时间段 {"startTime":"22:00","endTime":"08:00"}
         */
        //@NotNull(message = "免打扰时间段不能为空")
        private NotDisturbPeriodDTO notDisturbPeriod;

    }







    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class UpdateDTO {

        @NotNull(message = "预警计划Id不能为空")
        private Long id;

        /**
         * 预警计划名称
         */
        @NotBlank(message = "预警计划名称不能为空")
        private String planName;

        /**
         * 业务类型 1:账号交付服务 2:商品工单服务
         * @see RemindPlanServiceTypeEnum
         */
        @NotNull(message = "业务类型不能为空")
        private Integer serviceType;


        /**
         * 业务类型，1:代售，2:中介，服务类型为账号交付有值
         * @see RemindPlanBusinessTypeEnum
         */
        private List<Integer> businessTypes;

        /**
         *  工单状态：1:待接单，2:已接单，3:待跟进，服务类型为商品工单有值
         *  @see RemindPlanWorkOrderStatusEnum
         */
        private List<Integer> workOrderStatuses;


        /**
         * 订单来源(会员类型)：1 散户工单 2 号商工单 3 3A工单，服务类型为商品工单有值
         */
        private List<Integer> memberships;


        /**
         * 上架方式：1:官方截图，2:自主截图，服务类型为商品工单且 不为“待接单”时有值
         *  @see RemindPlanOnShelfTypeEnum
         */
        @Deprecated
        private List<Integer> onShelfTypes;


        /**
         * 预警游戏配置
         */
        @Valid
        @NotEmpty(message = "预警设置不能为空")
        private List<PlanGameConfigDTO> planGameConfigs;

        /**
         * 预警规则
         */
        @Valid
        @NotEmpty(message = "提醒规则不能为空")
        private List<PlanRuleDTO> planRules;


        /**
         * 免打扰时间段 {"startTime":"22:00","endTime":"08:00"}
         */
        //@NotBlank(message = "免打扰时间段不能为空")
        private NotDisturbPeriodDTO notDisturbPeriod;


    }


    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class DelDTO {
        @NotNull(message = "id不能为空")
        private Long id;
    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class SearchDTO {

        /**
         * 预警计划名称
         */
        @NotBlank(message = "预警计划名称不能为空")
        private String planName;

        /**
         * 业务类型
         */
        @NotNull(message = "业务类型不能为空")
        private Integer serviceType;

        /**
         * 免打扰时间段
         */
        @NotBlank(message = "notDisturbPeriod不能为空")
        private String notDisturbPeriod;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class PageDTO {

        /**
         * 预警计划名称
         */
        private String planName;

        /**
         * 服务类型: 1：账号交付 2:商品工单
         */
        @NotNull(message = "服务类型不能为空")
        private Integer serviceType;


        /**
         * 游戏厂商列表
         */
        private List<Integer> makers;

        /**
         * 游戏id列表
         */
        private List<String> gameIds;

        /**
         * 业务类型，1:代售，2:中介，服务类型为账号交付时可以传入
         */
        private List<Integer> businessTypes;


        /**
         *  工单状态：1:待接单，2:已接单，3:待跟进，服务类型为商品工单时可以传入
         */
        private List<Integer> workOrderStatuses;

        /**
         * 上架方式：1:官方截图，2:自主截图，服务类型为商品工单时可以传入
         */
        private List<Integer> onShelfTypes;


        /**
         * 订单来源(会员类型)：1 散户工单 2 号商工单 3 3A工单，服务类型为商品工单有值
         */
        private List<Integer> memberships;


        /**
         * 页码，从1开始
         */
        @NotNull(message = "页码不能为空")
        @Min(value = 1, message = "页码不能小于1")
        private Integer pageIndex;
        /**
         * 每页数量
         */
        @NotNull(message = "每页数量不能为空")
        @Min(value = 1, message = "每页数量不能小于1")
        private Integer pageSize;

    }







}

