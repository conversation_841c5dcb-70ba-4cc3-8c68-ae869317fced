package com.pxb7.mall.workorder;

import com.alibaba.fastjson2.JSON;
import com.pxb7.mall.workorder.app.service.DbRecordSyncAppService;
import com.pxb7.mall.workorder.client.dto.DbRecordSyncDTO;
import jakarta.annotation.Resource;
import org.junit.Test;

public class DbRecordSyncMessageConsumerTest extends AbstractBaseSpringService {

    @Resource
    private DbRecordSyncAppService dbRecordSyncAppService;

    @Test
    public void testConsume() {
        String jsonString = "{\"operateKeys\":[143459235373061,143459235373062,143459235373063,143459235373064],\"tableName\":\"remind_delivery_product\"}";
        DbRecordSyncDTO dbRecordSyncDTO = JSON.parseObject(jsonString, DbRecordSyncDTO.class);
        dbRecordSyncAppService.syncDbRecord(dbRecordSyncDTO);
    }


}
