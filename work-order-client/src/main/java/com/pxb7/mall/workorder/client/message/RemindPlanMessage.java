package com.pxb7.mall.workorder.client.message;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 预警计划消息
 * <AUTHOR>
 * @date 2025/4/9 13:52
 */

@Data
public class RemindPlanMessage implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 预警业务类型，1:账号交付，2:商品工单，3:售后工单，4:客诉工单
     */
    private Integer bizType;

    /**
     * 预警业务数据id（order_item_id或work_order_id）
     */
    private String bizId;

    /**
     * 预警记录id（remind_delivery_product或remind_work_order）
     */
    private String planRecordId;
    /**
     * 第几次提醒
     */
    private Integer whichTime;
    /**
     * 是否是超时前最后一条预警 0:否, 1:是
     */
    private Boolean lastBeforeTimeout;
    /**
     * 提醒时间点
     */
    private LocalDateTime remindTime;
    /**
     * 预警记录id
     */
    private String remindId;
    /**
     * 预警计划规则配置id,remind_plan_rule
     */
    private Long planRuleId;
    /**
     * 预警计划id,remind_plan
     */
    private Long remindPlanId;
    /**
     * 预警子计划id,remind_sub_plan
     */
    private Long remindSubPlanId;

}
