package com.pxb7.mall.workorder.infra.repository.db.entity;

import java.io.Serializable;
import java.time.*;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import com.baomidou.mybatisplus.annotation.*;

/**
 * 预警计划游戏配置(RemindPlanGameConfig)实体类
 *
 * <AUTHOR>
 * @since 2025-03-24 21:09:56
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "remind_plan_game_config")
@ToString
public class RemindPlanGameConfig implements Serializable {
    private static final long serialVersionUID = -65887808522754999L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 游戏ID
     */
    @TableField(value = "game_id")
    private String gameId;
    /**
     * 游戏名称
     */
    @TableField(value = "game_name")
    private String gameName;
    /**
     * 厂商 1 网易系  2 腾讯系  3 米哈游  4其他
     */
    @TableField(value = "maker")
    private Integer maker;
    /**
     * 投诉渠道，1:im,2:支付宝,3:咸鱼,4:12315,5:消费宝,6:连连支付
     */
    @TableField(value = "channel")
    private Integer channel;
    /**
     * 预期完结时间,{"hours":"20","minutes":"30"}
     */
    @TableField(value = "expect_complete_time_config")
    private String expectCompleteTimeConfig;
    /**
     * im客服端倒计时,{"hours":"10","minutes":"30"}
     */
    @TableField(value = "im_count_down_time_config")
    private String imCountDownTimeConfig;
    /**
     * 预警计划id,remind_plan
     */
    @TableField(value = "remind_plan_id")
    private Long remindPlanId;
    /**
     * 预警子计划id,remind_sub_plan
     */
    @TableField(value = "remind_sub_plan_id")
    private Long remindSubPlanId;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;
    /**
     * 创建人
     */
    @TableField(value = "create_user_id")
    private String createUserId;
    /**
     * 更新人
     */
    @TableField(value = "update_user_id")
    private String updateUserId;
    /**
     * 是否删除 0:否, 1:是
     */
    @TableLogic
    @TableField(value = "is_deleted")
    private Boolean deleted;


}

