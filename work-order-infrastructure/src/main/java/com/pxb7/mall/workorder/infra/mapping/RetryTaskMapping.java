package com.pxb7.mall.workorder.infra.mapping;

import com.pxb7.mall.workorder.infra.model.RemindPlanRetryTaskReqPO;
import com.pxb7.mall.workorder.infra.repository.db.entity.RemindPlanRetryTask;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/3 17:34
 */

@Mapper
public interface RetryTaskMapping {

    RetryTaskMapping INSTANCE = Mappers.getMapper(RetryTaskMapping.class);

    RemindPlanRetryTask addDto2PO(RemindPlanRetryTaskReqPO.AddPO source);

    List<RemindPlanRetryTask> updateDto2POList(List<RemindPlanRetryTaskReqPO.UpdatePO> source);

}
