package com.pxb7.mall.workorder.adapter.consumer;

import com.pxb7.mall.product.client.enums.OptLogMsgTagEnum;
import com.pxb7.mall.workorder.app.service.RemindWorkOrderAppService;
import com.pxb7.mall.workorder.domain.message.ProductEventMessage;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.apache.rocketmq.client.java.message.MessageIdImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

class WorkOrderStatusMessageConsumerTest {

    @InjectMocks
    private WorkOrderStatusMessageConsumer consumer;

    @Mock
    private RemindWorkOrderAppService remindWorkOrderAppService;

    @Mock
    private MessageView messageView;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        when(messageView.getMessageId()).thenReturn(new MessageIdImpl("version", "mocked-message-id"));
    }

    @Test
    void testConsume_WithBlankWorkOrderId_ReturnsSuccess() {
        // Arrange
        ProductEventMessage message = new ProductEventMessage();
        message.setWorkOrderId(null);
        message.setOptLogMsgTag(OptLogMsgTagEnum.PUB.getTag());

        // Act
        ConsumeResult result = consumer.consume(messageView, message);

        // Assert
        assertEquals(ConsumeResult.SUCCESS, result);
        verify(remindWorkOrderAppService, never()).generateRemindPlan(anyString());
    }

    @Test
    void testConsume_WithInvalidTag_ReturnsSuccess() {
        // Arrange
        ProductEventMessage message = new ProductEventMessage();
        message.setWorkOrderId("WO123");
        message.setOptLogMsgTag("OTHER");

        // Act
        ConsumeResult result = consumer.consume(messageView, message);

        // Assert
        assertEquals(ConsumeResult.SUCCESS, result);
        verify(remindWorkOrderAppService, never()).generateRemindPlan(anyString());
    }

    @Test
    void testConsume_WithTagPub_CallsGenerateAndReturnsSuccess() {
        // Arrange
        ProductEventMessage message = new ProductEventMessage();
        message.setWorkOrderId("WO123");
        message.setOptLogMsgTag(OptLogMsgTagEnum.PUB.getTag());

        // Act
        ConsumeResult result = consumer.consume(messageView, message);

        // Assert
        assertEquals(ConsumeResult.SUCCESS, result);
        verify(remindWorkOrderAppService).generateRemindPlan("WO123");
    }

    @Test
    void testConsume_WithTagWorkOrder_CallsGenerateAndReturnsSuccess() {
        // Arrange
        ProductEventMessage message = new ProductEventMessage();
        message.setWorkOrderId("WO123");
        message.setOptLogMsgTag(OptLogMsgTagEnum.WORK_ORDER.getTag());

        // Act
        ConsumeResult result = consumer.consume(messageView, message);

        // Assert
        assertEquals(ConsumeResult.SUCCESS, result);
        verify(remindWorkOrderAppService).generateRemindPlan("WO123");
    }

    @Test
    void testConsume_WhenExceptionThrown_ReturnsFailure() {
        // Arrange
        ProductEventMessage message = new ProductEventMessage();
        message.setWorkOrderId("WO123");
        message.setOptLogMsgTag(OptLogMsgTagEnum.PUB.getTag());

        doThrow(new RuntimeException("Simulated error")).when(remindWorkOrderAppService).generateRemindPlan("WO123");

        // Act
        ConsumeResult result = consumer.consume(messageView, message);

        // Assert
        assertEquals(ConsumeResult.FAILURE, result);
    }
}
