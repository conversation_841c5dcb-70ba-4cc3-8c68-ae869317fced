package com.pxb7.mall.workorder.infra.model;

import java.time.LocalDateTime;
import java.util.List;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.ToString;


/**
 * 提醒服务预警计划(RemindPlan)实体类
 *
 * <AUTHOR>
 * @since 2025-03-24 21:09:55
 */
public class RemindPlanReqPO {

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class AddPO {

        private Long id;

        private String planName;


        private Integer serviceType;


        private String notDisturbPeriod;


        private String createUserId;


        private String updateUserId;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class UpdatePO {

        private Long id;


        private String planName;


        private Integer serviceType;


        private String notDisturbPeriod;


        private String createUserId;


        private String updateUserId;

        private LocalDateTime updateTime;
    }


    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class DelPO {

        private Long id;
    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class SearchPO {

        private String planName;


        private Integer serviceType;


        private String notDisturbPeriod;


        private String createUserId;


        private String updateUserId;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class PagePO {


        /**
         * 预警计划名称
         */
        private String planName;

        /**
         * 服务类型: 1：账号交付 2:商品工单
         */
        private Integer serviceType;


        /**
         * 游戏厂商列表
         */
        private List<Integer> makers;

        /**
         * 游戏id列表
         */
        private List<String> gameIds;

        /**
         * 业务类型，1:代售，2:中介，服务类型为账号交付时可以传入
         */
        private List<Integer> businessTypes;

        /**
         * 工单类型，1:找回，2:纠纷
         */
        private Integer workOrderType;

        /**
         *  工单状态：1:待接单，2:已接单，3:待跟进，服务类型为商品工单时可以传入
         */
        private List<Integer> workOrderStatuses;

        /**
         * 上架方式：1:官方截图，2:自主截图，服务类型为商品工单时可以传入
         */
        private List<Integer> onShelfTypes;

        /**
         * 投诉级别：1:一级，2:二级，3:三级，4:四级，5:五级，6:六级
         */
        private Integer complaintLevel;

        /**
         * 订单来源(会员类型)：1 散户工单 2 号商工单 3 3A工单，服务类型为商品工单有值
         */
        private List<Integer> memberships;


        /**
         * 页码，从1开始
         */
        private Integer pageIndex;
        /**
         * 每页数量
         */
        private Integer pageSize;

    }

}

