<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pxb7.mall.workorder.infra.repository.db.mapper.RemindPlanRuleMapper">
    <resultMap id="BaseResultMap" type="com.pxb7.mall.workorder.infra.repository.db.entity.RemindPlanRule">
        <result column="id" jdbcType="BIGINT" property="id"/>
        <result column="node_number" jdbcType="INTEGER" property="nodeNumber"/>
        <result column="remind_time_config" jdbcType="VARCHAR" property="remindTimeConfig"/>
        <result column="remind_method_config" jdbcType="VARCHAR" property="remindMethodConfig"/>
        <result column="remind_plan_id" jdbcType="BIGINT" property="remindPlanId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_user_id" jdbcType="VARCHAR" property="createUserId"/>
        <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId"/>
        <result column="is_deleted" jdbcType="BOOLEAN" property="deleted"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into
        remind_plan_rule(node_number,remind_time_config,remind_method_config,remind_plan_id,create_user_id,update_user_id)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.nodeNumber},#{entity.remindTimeConfig},#{entity.remindMethodConfig},#{entity.remindPlanId},#{entity.createUserId},#{entity.updateUserId})
        </foreach>
    </insert>
</mapper>

