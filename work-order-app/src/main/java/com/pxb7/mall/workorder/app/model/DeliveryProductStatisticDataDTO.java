package com.pxb7.mall.workorder.app.model;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Getter
@Setter
@Accessors(chain = true)
@ToString
public class DeliveryProductStatisticDataDTO {

    /**
     * 日期
     */
    private String date;


    /**
     * 处理总数量 (交付房间数)
     */
    private Integer processTotalCount;

    /**
     * 已完成数量 (已完结房间数)
     */
    private Integer completeCount;


    /**
     * 处理中数量 （交付中房间数量）
     */
    private Integer processingCount;


    /**
     * 即将超时
     */
    private Integer willTimeoutCount;

    /**
     * 超时数量
     */
    private Integer timeoutCount;


    /**
     * 超时率
     */
    private BigDecimal timeoutRate;


    /**
     * 游戏id 列表
     */
    private String gameId;


    /**
     * 游戏名称
     */
    private String gameName;


    /**
     * 交易类型，1:代售，2:中介
     */
    private Integer businessType;

    /**
     * 交付客服id
     */
    private String deliveryCustomerCareId;

    /**
     * 交付客服id
     */
    private String deliveryCustomerCareName;
}
