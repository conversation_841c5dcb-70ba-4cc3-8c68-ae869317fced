package com.pxb7.mall.workorder.infra.repository.db;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pxb7.mall.workorder.infra.model.RemindDeliveryProductReqPO;
import com.pxb7.mall.workorder.infra.repository.db.entity.RemindDeliveryProduct;

/**
 * 账号交付预警记录(RemindDeliveryProduct)表服务接口
 *
 * <AUTHOR>
 * @since 2025-03-27 20:07:08
 */
public interface RemindDeliveryProductRepository extends IService<RemindDeliveryProduct> {

    boolean insert(RemindDeliveryProductReqPO.AddPO param);

    boolean updateByOrderItemId(RemindDeliveryProductReqPO.UpdatePO param);

    RemindDeliveryProduct findByOrderItemId(String orderItemId, String remindId);

    RemindDeliveryProduct getByRemindId(String remindId);

    Page<RemindDeliveryProduct> page(RemindDeliveryProductReqPO.PagePO pagePO);

    boolean batchUpdateTimeoutStatusByIds(List<Long> ids, Integer status);

}

