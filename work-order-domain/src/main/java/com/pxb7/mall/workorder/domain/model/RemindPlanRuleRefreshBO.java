package com.pxb7.mall.workorder.domain.model;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;
import java.util.List;


@Getter
@Setter
@Accessors(chain = true)
@ToString
public class RemindPlanRuleRefreshBO {


    private List<RemindPlanRuleBO> needAddPlanRules;

    private List<RemindPlanRuleBO> needUpdatePlanRules;

    private List<RemindPlanRuleBO> needDeletePlanRules;
}
