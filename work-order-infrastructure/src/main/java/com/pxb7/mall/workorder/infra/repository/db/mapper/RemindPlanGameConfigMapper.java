package com.pxb7.mall.workorder.infra.repository.db.mapper;

import com.pxb7.mall.workorder.infra.repository.db.entity.RemindPlanGameConfig;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 预警计划游戏配置(RemindPlanGameConfig)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-03-24 21:09:56
 */
@Mapper
public interface RemindPlanGameConfigMapper extends BaseMapper<RemindPlanGameConfig> {
    /**
     * 批量新增数据
     *
     * @param entities List<RemindPlanGameConfig> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<RemindPlanGameConfig> entities);

    /**
     * 根据gameId和businessType查询预警计划游戏配置
     * @param gameId
     * @param businessType
     * @return
     */
    RemindPlanGameConfig selectByGameIdAndBusinessType(@Param("gameId") String gameId,
                                                       @Param("businessType") Integer businessType);

    /**
     * 根据gameId和工单信息查询预警计划游戏配置
     * @param gameId
     * @param workOrderStatus
     * @param onShelfType
     * @param membership
     * @return
     */
    RemindPlanGameConfig selectByGameIdAndWorkOrderStatus(
            @Param("gameId") String gameId, @Param("workOrderStatus") Integer workOrderStatus,
            @Param("onShelfType") Integer onShelfType, @Param("membership") Integer membership);

    /**
     * 根据gameId和工单类型查询预警计划游戏配置
     * @param gameId
     * @param workOrderType
     * @param membership
     * @return
     */
    RemindPlanGameConfig selectByGameIdAndWorkOrderType(@Param("gameId") String gameId,
                                                        @Param("workOrderType") Integer workOrderType,
                                                        @Param("membership") Integer membership);

    /**
     * 根据complaintLevel和complaintChannel查询预警计划游戏配置
     * @param complaintLevel
     * @param complaintChannel
     * @return
     */
    RemindPlanGameConfig selectByComplaintLevelAndChannel(@Param("complaintLevel") Integer complaintLevel,
                                                          @Param("complaintChannel") Integer complaintChannel);

}

