package com.pxb7.mall.workorder.infra.repository.db;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pxb7.mall.components.env.profile.PxProfileHelper;
import com.pxb7.mall.workorder.client.enums.BizTypeEnum;
import com.pxb7.mall.workorder.client.enums.PlanExecuteStatusEnum;
import com.pxb7.mall.workorder.infra.mapping.PlanRecordMapping;
import com.pxb7.mall.workorder.infra.model.RemindPlanRecordReqPO;
import com.pxb7.mall.workorder.infra.repository.db.entity.RemindPlanRecord;
import com.pxb7.mall.workorder.infra.repository.db.mapper.RemindPlanRecordMapper;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

/**
 * 预警执行计划记录(RemindPlanRecord)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-04-12 14:13:00
 */
@Slf4j
@Repository
public class RemindPlanRecordDbRepository extends ServiceImpl<RemindPlanRecordMapper, RemindPlanRecord> implements RemindPlanRecordRepository {


    @Override
    public boolean insertBatch(List<RemindPlanRecordReqPO.AddPO> param) {
        List<RemindPlanRecord> records = PlanRecordMapping.INSTANCE.addDto2POList(param);
        return this.getBaseMapper().insertBatch(records) > 0;
    }

    @Override
    public Page<RemindPlanRecord> page(RemindPlanRecordReqPO.PagePO param) {
        LambdaQueryWrapper<RemindPlanRecord> queryWrapper = new LambdaQueryWrapper<>();
        // 返回分页查询结果
        queryWrapper.eq(RemindPlanRecord::getEnvProfile, PxProfileHelper.getProfile().getProfileValue());
        queryWrapper.gt(RemindPlanRecord::getId, param.getId());
        queryWrapper.lt(RemindPlanRecord::getRemindTime, param.getRemindTime());
        queryWrapper.eq(RemindPlanRecord::getStatus, param.getStatus());
        queryWrapper.orderByAsc(RemindPlanRecord::getId);
        return this.page(new Page<>(param.getPageIndex(), param.getPageSize()), queryWrapper);
    }

    /**
     * 更新状态
     * @param param
     * @return
     */
    @Override
    public boolean updateStatusByBizIdAndType(RemindPlanRecordReqPO.UpdatePO param) {
        LambdaUpdateWrapper<RemindPlanRecord> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(RemindPlanRecord::getBizType, param.getBizType());
        updateWrapper.eq(RemindPlanRecord::getBizId, param.getBizId());
        if (Objects.nonNull(param.getPlanRecordId())) {
            updateWrapper.eq(RemindPlanRecord::getPlanRecordId, param.getPlanRecordId());
        }
        updateWrapper.in(RemindPlanRecord::getStatus,
                PlanExecuteStatusEnum.WILL_EXECUTE.getCode(), PlanExecuteStatusEnum.IN_PROCESS.getCode());
        updateWrapper.set(RemindPlanRecord::getStatus, param.getStatus());
        return this.update(updateWrapper);
    }

    /**
     * 批量账号交付，暂停和开启的更新状态
     * @param planRecordIds
     * @param status
     * @return
     */
    @Override
    public boolean batchUpdateStatusByRemindIds(List<String> remindIds, Integer status,Long addSeconds) {
        LambdaUpdateWrapper<RemindPlanRecord> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(RemindPlanRecord::getRemindId, remindIds);
        //业务场景必须是账号交付
        updateWrapper.eq(RemindPlanRecord::getBizType, BizTypeEnum.DELIVERY_PRODUCT.getType());

        if (PlanExecuteStatusEnum.PAUSE.getCode().equals(status)) {
            //如果设置成暂停，
            updateWrapper.in(RemindPlanRecord::getStatus, List.of(PlanExecuteStatusEnum.WILL_EXECUTE.getCode(),PlanExecuteStatusEnum.IN_PROCESS.getCode()));
        }else if (PlanExecuteStatusEnum.WILL_EXECUTE.getCode().equals(status)) {
            //如果设置成开始，
            updateWrapper.eq(RemindPlanRecord::getStatus, PlanExecuteStatusEnum.PAUSE.getCode());
        }
        updateWrapper.set(RemindPlanRecord::getStatus, status);
        //如果从暂停设置成未执行
        if (PlanExecuteStatusEnum.WILL_EXECUTE.getCode().equals(status) && Objects.nonNull(addSeconds)) {
            updateWrapper.setSql("remind_time = DATE_ADD(remind_time, INTERVAL " + addSeconds + " SECOND)");
        }
        return this.update(updateWrapper);
    }
    /**
     * 批量更新状态
     * @param planRecordIds
     * @param status
     * @return
     */
    @Override
    public boolean batchUpdateStatusByIds(List<Long> planRecordIds, Integer status) {
        return this.getBaseMapper().batchUpdateStatusByIds(planRecordIds, status) > 0;
    }
    /**
     * 根据业务id删除记录
     * @param bizId
     * @return
     */
    @Override
    public boolean deleteByBizId(String bizId) {
        if (StringUtils.isBlank(bizId)) {
            return false;
        }
        return this.remove(
                new LambdaQueryWrapper<RemindPlanRecord>()
                        .eq(RemindPlanRecord::getBizId, bizId)
                        .in(RemindPlanRecord::getStatus,
                                PlanExecuteStatusEnum.WILL_EXECUTE.getCode(),
                                PlanExecuteStatusEnum.IN_PROCESS.getCode()));
    }
}
