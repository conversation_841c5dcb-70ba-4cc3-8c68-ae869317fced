<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pxb7.mall.workorder.infra.repository.db.mapper.RemindPlanMapper">
    <resultMap id="BaseResultMap" type="com.pxb7.mall.workorder.infra.repository.db.entity.RemindPlan">
        <result column="id" jdbcType="BIGINT" property="id"/>
        <result column="plan_name" jdbcType="VARCHAR" property="planName"/>
        <result column="service_type" jdbcType="INTEGER" property="serviceType"/>
        <result column="not_disturb_period" jdbcType="VARCHAR" property="notDisturbPeriod"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_user_id" jdbcType="VARCHAR" property="createUserId"/>
        <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId"/>
        <result column="is_deleted" jdbcType="BOOLEAN" property="deleted"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into
        remind_plan(plan_name,service_type,not_disturb_period,create_user_id,update_user_id)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.planName},#{entity.serviceType},#{entity.notDisturbPeriod},#{entity.createUserId},#{entity.updateUserId})
        </foreach>
    </insert>


    <select id="page" resultType="com.pxb7.mall.workorder.infra.repository.db.entity.RemindPlan">
        select
            distinct a.*
        from remind_plan as a
        left join remind_sub_plan as b on a.id=b.remind_plan_id
        left join remind_plan_game_config as c on b.id =c.remind_sub_plan_id
        <where>
            a.is_deleted = 0 and b.is_deleted=0 and c.is_deleted=0
            <if test="param.serviceType != null and param.serviceType != ''">
                and a.service_type=#{param.serviceType}
            </if>
            <if test="param.planName != null and param.planName != ''">
                and a.plan_name like CONCAT('%', #{param.planName}, '%')
            </if>
            <if test="param.workOrderType != null and param.workOrderType != ''">
                and b.work_order_type=#{param.workOrderType}
            </if>
            <if test="param.complaintLevel != null and param.complaintLevel != ''">
                and b.complaint_level=#{param.complaintLevel}
            </if>
            <if test="param.onShelfTypes != null and !param.onShelfTypes.isEmpty()">
                and b.on_shelf_type in
                <foreach collection="param.onShelfTypes" item="onShelfType" open="(" separator="," close=")">
                    #{onShelfType}
                </foreach>
            </if>
            <if test="param.memberships != null and !param.memberships.isEmpty()">
                and b.membership in
                <foreach collection="param.memberships" item="membership" open="(" separator="," close=")">
                    #{membership}
                </foreach>
            </if>

            <if test="param.workOrderStatuses != null and !param.workOrderStatuses.isEmpty()">
                and b.work_order_status in
                <foreach collection="param.workOrderStatuses" item="workOrderStatus" open="(" separator="," close=")">
                    #{workOrderStatus}
                </foreach>
            </if>
            <if test="param.businessTypes != null and !param.businessTypes.isEmpty()">
                and b.business_type in
                <foreach collection="param.businessTypes" item="businessType" open="(" separator="," close=")">
                    #{businessType}
                </foreach>
            </if>
            <if test="param.gameIds != null and !param.gameIds.isEmpty()">
                and c.game_id in
                <foreach collection="param.gameIds" item="gameId" open="(" separator="," close=")">
                    #{gameId}
                </foreach>
            </if>
            <if test="param.makers != null and !param.makers.isEmpty()">
                and c.maker in
                <foreach collection="param.makers" item="maker" open="(" separator="," close=")">
                    #{maker}
                </foreach>
            </if>
            order by a.update_time desc
        </where>
    </select>
</mapper>

