package com.pxb7.mall.workorder.domain.model;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;


/**
 * 生成提醒计划异常重试表(RemindPlanRetryTask)实体类
 *
 * <AUTHOR>
 * @since 2025-05-06 20:54:42
 */
public class RemindPlanRetryTaskReqBO {

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class AddBO {


        private String bizId;


        private Integer serviceType;


        private String recordInfo;


        private Integer retryTimes;


        private Integer status;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class UpdateBO {


        private Long id;


        private String bizId;


        private Integer serviceType;


        private String recordInfo;


        private Integer retryTimes;


        private Integer status;


    }


    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class DelBO {
        private Long id;
    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class SearchBO {

        private String bizId;


        private Integer serviceType;


        private String recordInfo;


        private Integer retryTimes;


        private Integer status;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class PageBO {

        private Long id;


        private String bizId;


        private Integer serviceType;


        private String recordInfo;


        private Integer retryTimes;


        private Integer status;


        /**
         * 页码，从1开始
         */
        private Long pageIndex;
        /**
         * 每页数量
         */
        private Long pageSize;

    }

}

