package com.pxb7.mall.workorder.app.model;

import java.util.Date;
import java.time.*;
import java.math.BigDecimal;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Min;
import lombok.ToString;


/**
 * 预警计划操作记录表(RemindPlanOperateRecord)实体类
 *
 * <AUTHOR>
 * @since 2025-03-31 20:37:07
 */
public class RemindPlanOperateRecordReqDTO {

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class AddDTO {


        @NotBlank(message = "remindPlanId不能为空")
        private String remindPlanId;


        @NotNull(message = "optType不能为空")
        private Integer optType;


        @NotNull(message = "dataType不能为空")
        private Integer dataType;


        @NotBlank(message = "originContent不能为空")
        private String originContent;


        @NotBlank(message = "newContent不能为空")
        private String newContent;


        @NotBlank(message = "traceId不能为空")
        private String traceId;


        @NotBlank(message = "optUserId不能为空")
        private String optUserId;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class UpdateDTO {


        @NotNull(message = "id不能为空")
        private Long id;


        @NotBlank(message = "remindPlanId不能为空")
        private String remindPlanId;


        @NotNull(message = "optType不能为空")
        private Integer optType;


        @NotNull(message = "dataType不能为空")
        private Integer dataType;


        @NotBlank(message = "originContent不能为空")
        private String originContent;


        @NotBlank(message = "newContent不能为空")
        private String newContent;


        @NotBlank(message = "traceId不能为空")
        private String traceId;


        @NotBlank(message = "optUserId不能为空")
        private String optUserId;


    }


    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class DelDTO {
        @NotNull(message = "id不能为空")
        private Long id;
    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class SearchDTO {

        @NotBlank(message = "remindPlanId不能为空")
        private String remindPlanId;


        @NotNull(message = "optType不能为空")
        private Integer optType;


        @NotNull(message = "dataType不能为空")
        private Integer dataType;


        @NotBlank(message = "originContent不能为空")
        private String originContent;


        @NotBlank(message = "newContent不能为空")
        private String newContent;


        @NotBlank(message = "traceId不能为空")
        private String traceId;


        @NotBlank(message = "optUserId不能为空")
        private String optUserId;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class PageDTO {


        @NotBlank(message = "remindPlanId不能为空")
        private String remindPlanId;


        @NotNull(message = "optType不能为空")
        private Integer optType;


        @NotNull(message = "dataType不能为空")
        private Integer dataType;


        @NotBlank(message = "originContent不能为空")
        private String originContent;


        @NotBlank(message = "newContent不能为空")
        private String newContent;


        @NotBlank(message = "traceId不能为空")
        private String traceId;


        @NotBlank(message = "optUserId不能为空")
        private String optUserId;


        /**
         * 页码，从1开始
         */
        @NotNull(message = "pageIndex不能为空")
        @Min(value = 1, message = "pageIndex不能小于1")
        private Integer pageIndex;
        /**
         * 每页数量
         */
        @NotNull(message = "pageSize不能为空")
        private Integer pageSize;

    }

}

