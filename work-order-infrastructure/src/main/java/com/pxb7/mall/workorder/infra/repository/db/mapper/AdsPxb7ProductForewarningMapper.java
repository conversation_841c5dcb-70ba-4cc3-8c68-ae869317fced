package com.pxb7.mall.workorder.infra.repository.db.mapper;


import com.pxb7.mall.workorder.infra.model.DeliveryStatictisDataPO;
import com.pxb7.mall.workorder.infra.model.DeliveryStatictisSearchPO;
import com.pxb7.mall.workorder.infra.model.ProductForewarningStatictisDataPO;
import com.pxb7.mall.workorder.infra.model.ProductForewarningStatictisSearchPO;
import com.pxb7.mall.workorder.infra.repository.db.entity.AdsPxb7ProductForewarning;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.Pageable;
import java.util.List;

/**
 * 商品预警统计(AdsPxb7ProductForewarning)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-04-17 22:45:15
 */
@Mapper
public interface AdsPxb7ProductForewarningMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    AdsPxb7ProductForewarning queryById(Long id);

    /**
     * 查询指定行数据
     *
     * @param adsPxb7ProductForewarning 查询条件
     * @param pageable         分页对象
     * @return 对象列表
     */
    List<AdsPxb7ProductForewarning> queryAllByLimit(AdsPxb7ProductForewarning adsPxb7ProductForewarning, @Param("pageable") Pageable pageable);

    /**
     * 统计总行数
     *
     * @param adsPxb7ProductForewarning 查询条件
     * @return 总行数
     */
    long count(AdsPxb7ProductForewarning adsPxb7ProductForewarning);

    /**
     * 新增数据
     *
     * @param adsPxb7ProductForewarning 实例对象
     * @return 影响行数
     */
    int insert(AdsPxb7ProductForewarning adsPxb7ProductForewarning);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<AdsPxb7ProductForewarning> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<AdsPxb7ProductForewarning> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<AdsPxb7ProductForewarning> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<AdsPxb7ProductForewarning> entities);

    /**
     * 修改数据
     *
     * @param adsPxb7ProductForewarning 实例对象
     * @return 影响行数
     */
    int update(AdsPxb7ProductForewarning adsPxb7ProductForewarning);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Long id);


    List<ProductForewarningStatictisDataPO> countBySearchParam(@Param("param") ProductForewarningStatictisSearchPO param);
}

