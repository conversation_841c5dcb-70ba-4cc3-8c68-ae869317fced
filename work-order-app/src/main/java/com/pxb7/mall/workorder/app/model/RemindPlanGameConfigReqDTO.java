package com.pxb7.mall.workorder.app.model;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Min;
import lombok.ToString;


/**
 * 预警计划游戏配置(RemindPlanGameConfig)实体类
 *
 * <AUTHOR>
 * @since 2025-03-24 21:12:17
 */
public class RemindPlanGameConfigReqDTO {

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class AddDTO {


        @NotBlank(message = "gameId不能为空")
        private String gameId;


        @NotBlank(message = "gameName不能为空")
        private String gameName;


        @NotNull(message = "maker不能为空")
        private Integer maker;


        @NotBlank(message = "expectCompleteTimeConfig不能为空")
        private TimeConfigDTO expectCompleteTimeConfig;


        @NotBlank(message = "imCountDownTimeConfig不能为空")
        private TimeConfigDTO imCountDownTimeConfig;


        @NotNull(message = "remindPlanId不能为空")
        private Long remindPlanId;


        @NotNull(message = "remindSubPlanId不能为空")
        private Long remindSubPlanId;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class UpdateDTO {


        @NotNull(message = "id不能为空")
        private Long id;


        @NotBlank(message = "gameId不能为空")
        private String gameId;


        @NotBlank(message = "gameName不能为空")
        private String gameName;


        @NotNull(message = "maker不能为空")
        private Integer maker;


        @NotBlank(message = "expectCompleteTimeConfig不能为空")
        private TimeConfigDTO expectCompleteTimeConfig;


        @NotBlank(message = "imCountDownTimeConfig不能为空")
        private TimeConfigDTO imCountDownTimeConfig;


        @NotNull(message = "remindPlanId不能为空")
        private Long remindPlanId;


        @NotNull(message = "remindSubPlanId不能为空")
        private Long remindSubPlanId;


    }


    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class DelDTO {
        @NotNull(message = "id不能为空")
        private Long id;
    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class SearchDTO {

        @NotBlank(message = "gameId不能为空")
        private String gameId;


        @NotBlank(message = "gameName不能为空")
        private String gameName;


        @NotNull(message = "maker不能为空")
        private Integer maker;


        @NotBlank(message = "expectCompleteTimeConfig不能为空")
        private TimeConfigDTO expectCompleteTimeConfig;


        @NotBlank(message = "imCountDownTimeConfig不能为空")
        private TimeConfigDTO imCountDownTimeConfig;


        @NotNull(message = "remindPlanId不能为空")
        private Long remindPlanId;


        @NotNull(message = "remindSubPlanId不能为空")
        private Long remindSubPlanId;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class PageDTO {


        @NotBlank(message = "gameId不能为空")
        private String gameId;


        @NotBlank(message = "gameName不能为空")
        private String gameName;


        @NotNull(message = "maker不能为空")
        private Integer maker;


        @NotBlank(message = "expectCompleteTimeConfig不能为空")
        private TimeConfigDTO expectCompleteTimeConfig;


        @NotBlank(message = "imCountDownTimeConfig不能为空")
        private TimeConfigDTO imCountDownTimeConfig;


        @NotNull(message = "remindPlanId不能为空")
        private Long remindPlanId;


        @NotNull(message = "remindSubPlanId不能为空")
        private Long remindSubPlanId;


        /**
         * 页码，从1开始
         */
        @NotNull(message = "pageIndex不能为空")
        @Min(value = 1, message = "pageIndex不能小于1")
        private Integer pageIndex;
        /**
         * 每页数量
         */
        @NotNull(message = "pageSize不能为空")
        private Integer pageSize;

    }

}

