package com.pxb7.mall.workorder.infra.repository.gateway.dubbo.order;

import com.alibaba.cola.dto.SingleResponse;
import com.pxb7.mall.trade.order.client.api.OrderImDubboServiceI;
import com.pxb7.mall.trade.order.client.api.OrderInfoDubboServiceI;
import com.pxb7.mall.trade.order.client.dto.request.orderim.request.ImInfoReqDTO;
import com.pxb7.mall.trade.order.client.dto.response.order.RoomOrderDetailsRespDTO;
import com.pxb7.mall.trade.order.client.dto.response.order.dubbo.OrderInfoDubboRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 */
@Slf4j
@Repository
public class OrderInfoGateway {

    @DubboReference
    private OrderInfoDubboServiceI orderInfoDubboService;

    @DubboReference
    private OrderImDubboServiceI orderImDubboService;


    /**
     * 获取订单信息
     * @param roomId
     * @return
     */
    public RoomOrderDetailsRespDTO getRoomOrderDetails(String roomId) {

        try {
            SingleResponse<RoomOrderDetailsRespDTO> response = orderInfoDubboService.getRoomOrderDetails(roomId);
            if (response == null || !response.isSuccess()) {
                log.error("Failed to retrieve data from the OrderInfoDubboServiceI#getRoomOrderDetails,roomId:{}",
                    roomId);
                return null;
            }
            return response.getData();
        } catch (Exception e) {
            log.error("An exception occurred while calling OrderInfoDubboServiceI#getRoomOrderDetails.roomId:{}",
                roomId, e);
            return null;
        }
    }

    /**
     * 获取订单交付房间
     * @param orderItemId
     * @return
     */
    public String getGroupId(String orderItemId) {
        try {
            ImInfoReqDTO reqDTO = new ImInfoReqDTO();
            reqDTO.setOrderItemId(orderItemId);
            SingleResponse<String> response = orderImDubboService.getImRoomId(reqDTO);
            if (response == null || !response.isSuccess()) {
                return null;
            }
            return response.getData();
        } catch (Exception e) {
            log.warn("An exception occurred while calling OrderImDubboServiceI#getImRoomId.orderItemId:{}", orderItemId, e);
            return null;
        }
    }

    /**
     * 获取订单信息
     * @param orderItemId
     * @return
     */
    public OrderInfoDubboRespDTO getOrderInfo(String orderItemId) {
        try {
            SingleResponse<OrderInfoDubboRespDTO> response = orderInfoDubboService.getOrderInfo(orderItemId);
            if (response == null || !response.isSuccess()) {
                log.warn("Failed to retrieve data from the orderInfoDubboService#getOrderInfo,orderItemId:{}",
                        orderItemId);
                return null;
            }
            return response.getData();
        } catch (Exception e) {
            log.warn("An exception occurred while calling OrderInfoDubboServiceI#getRoomOrderDetails.orderItemId:{}",
                    orderItemId, e);
            return null;
        }
    }

}
