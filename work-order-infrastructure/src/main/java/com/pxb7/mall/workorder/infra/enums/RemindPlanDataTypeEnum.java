package com.pxb7.mall.workorder.infra.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum RemindPlanDataTypeEnum {

    PLAN_AND_SUBPLAN(1, "预警计划及子计划"),
    GAME_CONFIG(2, "游戏预警配置"),
    PLAN_RULE(3, "预警规则"),
    ;

    private final Integer value;

    private final String label;

    public static String getLabel(Integer value) {
        return Arrays.stream(RemindPlanDataTypeEnum.values()).filter(e -> e.getValue().equals(value))
            .map(RemindPlanDataTypeEnum::getLabel).findAny().orElse("");
    }

    public static RemindPlanDataTypeEnum getEnum(Integer value) {
        if (null == value) {
            return null;
        }
        return Arrays.stream(RemindPlanDataTypeEnum.values()).filter(e -> value.equals(e.getValue())).findFirst()
            .orElse(null);
    }

}
