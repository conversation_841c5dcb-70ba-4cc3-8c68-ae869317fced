package com.pxb7.mall.workorder.app.model;

import java.util.Date;
import java.time.*;
import java.math.BigDecimal;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.ToString;


/**
 * 售后工单预警记录(RemindAfterSale)实体类
 *
 * <AUTHOR>
 * @since 2025-04-24 23:33:20
 */
public class RemindAfterSaleRespDTO {


    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class DetailDTO {
        private Long id;
        private String remindId;
        private String workOrderId;
        private String productId;
        private String productCode;
        private String gameId;
        private String groupId;
        private String orderId;
        private String orderItemId;
        private Integer completeStatus;
        private Integer timeOutStatus;
        private String retrieveUserId;
        private String disputeUserId;
        private LocalDateTime expectCompleteTime;
        private LocalDateTime completeTime;
        private Long gameConfigId;
        private Long remindPlanId;
        private Long remindSubPlanId;
    }
}

