package com.pxb7.mall.workorder.domain.mapping;

import com.pxb7.mall.workorder.domain.model.RemindPlanOperateRecordBO;
import com.pxb7.mall.workorder.domain.model.RemindPlanOperateRecordReqBO;
import com.pxb7.mall.workorder.infra.model.RemindPlanOperateRecordReqPO;
import com.pxb7.mall.workorder.infra.repository.db.entity.RemindPlanOperateRecord;
import com.pxb7.mall.workorder.domain.model.RemindPlanOperateRecordRespBO;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;


@Mapper
public interface RemindPlanOperateRecordDomainMapping {

    RemindPlanOperateRecordDomainMapping INSTANCE = Mappers.getMapper(RemindPlanOperateRecordDomainMapping.class);


    RemindPlanOperateRecordReqPO.AddPO remindPlanOperateRecordBO2AddPO(RemindPlanOperateRecordReqBO.AddBO source);

    RemindPlanOperateRecordReqPO.AddPO remindPlanOperateRecordBO2AddPO(RemindPlanOperateRecordBO source);

    RemindPlanOperateRecordReqPO.UpdatePO remindPlanOperateRecordBO2UpdatePO(RemindPlanOperateRecordReqBO.UpdateBO source);

    RemindPlanOperateRecordReqPO.DelPO remindPlanOperateRecordBO2DelPO(RemindPlanOperateRecordReqBO.DelBO source);

    RemindPlanOperateRecordReqPO.SearchPO remindPlanOperateRecordBO2SearchPO(RemindPlanOperateRecordReqBO.SearchBO source);

    RemindPlanOperateRecordReqPO.PagePO remindPlanOperateRecordBO2PagePO(RemindPlanOperateRecordReqBO.PageBO source);

    RemindPlanOperateRecordRespBO.DetailBO remindPlanOperateRecordPO2DetailBO(RemindPlanOperateRecord source);

    List<RemindPlanOperateRecordRespBO.DetailBO> remindPlanOperateRecordPO2ListBO(List<RemindPlanOperateRecord> source);

    Page<RemindPlanOperateRecordRespBO.DetailBO> remindPlanOperateRecordPO2PageBO(Page<RemindPlanOperateRecord> source);

}


