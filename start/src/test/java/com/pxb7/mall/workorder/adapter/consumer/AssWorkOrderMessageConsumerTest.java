package com.pxb7.mall.workorder.adapter.consumer;

import com.pxb7.mall.workorder.app.service.RemindAfterSaleAppService;
import com.pxb7.mall.workorder.app.service.RemindComplaintAppService;
import com.pxb7.mall.workorder.domain.message.AssWorkOrderMessage;
import com.pxb7.mall.workorder.infra.enums.RemindPlanWorkOrderTypeEnum;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.*;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

class AssWorkOrderMessageConsumerTest {

    @InjectMocks
    private AssWorkOrderMessageConsumer consumer;

    @Mock
    private RemindAfterSaleAppService afterSaleAppService;

    @Mock
    private RemindComplaintAppService complaintAppService;

    @Captor
    private ArgumentCaptor<String> stringArgumentCaptor;

    @Captor
    private ArgumentCaptor<Integer> integerArgumentCaptor;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testConsume_withNullParams_returnsSuccess() {
        // Arrange
        MessageView messageView = mock(MessageView.class);
        AssWorkOrderMessage workOrderMessage = new AssWorkOrderMessage();

        // Act
        ConsumeResult result = consumer.consume(messageView, workOrderMessage);

        // Assert
        assertEquals(ConsumeResult.SUCCESS, result);
        verifyNoInteractions(afterSaleAppService, complaintAppService);
    }

    @Test
    void testConsume_withRetrieveType_callsAfterSaleService() {
        // Arrange
        MessageView messageView = mock(MessageView.class);
        AssWorkOrderMessage workOrderMessage = new AssWorkOrderMessage();
        workOrderMessage.setWorkOrderId("WO123");
        workOrderMessage.setWorkOrderType(RemindPlanWorkOrderTypeEnum.RETRIEVE.getValue());

        doNothing().when(afterSaleAppService).dealAfterSaleWorkOrder(stringArgumentCaptor.capture(), integerArgumentCaptor.capture());

        // Act
        ConsumeResult result = consumer.consume(messageView, workOrderMessage);

        // Assert
        assertEquals(ConsumeResult.SUCCESS, result);
        assertEquals("WO123", stringArgumentCaptor.getValue());
        assertEquals(RemindPlanWorkOrderTypeEnum.RETRIEVE.getValue(), integerArgumentCaptor.getValue());
        verify(afterSaleAppService).dealAfterSaleWorkOrder(anyString(), anyInt());
        verifyNoMoreInteractions(complaintAppService);
    }

    @Test
    void testConsume_withDisputeType_callsAfterSaleService() {
        // Arrange
        MessageView messageView = mock(MessageView.class);
        AssWorkOrderMessage workOrderMessage = new AssWorkOrderMessage();
        workOrderMessage.setWorkOrderId("WO456");
        workOrderMessage.setWorkOrderType(RemindPlanWorkOrderTypeEnum.DISPUTE.getValue());

        doNothing().when(afterSaleAppService).dealAfterSaleWorkOrder(stringArgumentCaptor.capture(), integerArgumentCaptor.capture());

        // Act
        ConsumeResult result = consumer.consume(messageView, workOrderMessage);

        // Assert
        assertEquals(ConsumeResult.SUCCESS, result);
        assertEquals("WO456", stringArgumentCaptor.getValue());
        assertEquals(RemindPlanWorkOrderTypeEnum.DISPUTE.getValue(), integerArgumentCaptor.getValue());
        verify(afterSaleAppService).dealAfterSaleWorkOrder(anyString(), anyInt());
        verifyNoMoreInteractions(complaintAppService);
    }

    @Test
    void testConsume_withComplaintType_callsComplaintService() {
        // Arrange
        MessageView messageView = mock(MessageView.class);
        AssWorkOrderMessage workOrderMessage = new AssWorkOrderMessage();
        workOrderMessage.setWorkOrderId("WO789");
        workOrderMessage.setWorkOrderType(RemindPlanWorkOrderTypeEnum.COMPLAINT.getValue());

        doNothing().when(complaintAppService).dealComplaintWorkOrder(stringArgumentCaptor.capture());

        // Act
        ConsumeResult result = consumer.consume(messageView, workOrderMessage);

        // Assert
        assertEquals(ConsumeResult.SUCCESS, result);
        assertEquals("WO789", stringArgumentCaptor.getValue());
        verify(complaintAppService).dealComplaintWorkOrder(anyString());
        verifyNoMoreInteractions(afterSaleAppService);
    }

    @Test
    void testConsume_whenExceptionThrown_returnsFailure() {
        // Arrange
        MessageView messageView = mock(MessageView.class);
        AssWorkOrderMessage workOrderMessage = new AssWorkOrderMessage();
        workOrderMessage.setWorkOrderId("WO999");
        workOrderMessage.setWorkOrderType(RemindPlanWorkOrderTypeEnum.COMPLAINT.getValue());

        doThrow(new RuntimeException("模拟异常")).when(complaintAppService).dealComplaintWorkOrder(anyString());

        // Act
        ConsumeResult result = consumer.consume(messageView, workOrderMessage);

        // Assert
        assertEquals(ConsumeResult.FAILURE, result);
    }
}
