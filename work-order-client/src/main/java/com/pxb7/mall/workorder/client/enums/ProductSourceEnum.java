package com.pxb7.mall.workorder.client.enums;

import lombok.Getter;
import lombok.ToString;

/**
 * 商品来源
 * <AUTHOR>
 */
@Getter
@ToString
public enum ProductSourceEnum {

    /**
     * 商品来源：1:散户,2:号商,3:3A号商
     */
    RETAIL(1, "散户"),
    MERCHANT(2, "号商"),
    AAA_MERCHANT(3,  "3A号商");

    private final Integer code;
    private final String desc;

    ProductSourceEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ProductSourceEnum getByCode(Integer code) {
        for (ProductSourceEnum productSourceEnum : ProductSourceEnum.values()) {
            if (productSourceEnum.getCode().equals(code)) {
                return productSourceEnum;
            }
        }
        return null;
    }

}
