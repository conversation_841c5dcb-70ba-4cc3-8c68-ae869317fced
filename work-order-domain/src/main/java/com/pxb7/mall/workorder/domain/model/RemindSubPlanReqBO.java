package com.pxb7.mall.workorder.domain.model;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.List;


/**
 * 提醒服务预警子计划(RemindSubPlan)实体类
 *
 * <AUTHOR>
 * @since 2025-03-24 21:11:14
 */
public class RemindSubPlanReqBO {

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class AddBO {


        private Integer businessType;


        private Integer workOrderStatus;


        private Integer onShelfType;


        private String createUserId;


        private String updateUserId;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class UpdateBO {


        private Long id;


        private Integer businessType;


        private Integer workOrderStatus;


        private Integer onShelfType;


        private String createUserId;


        private String updateUserId;


    }


    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class DelBO {
        private Long id;
    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class SearchBO {

        private Integer businessType;


        private Integer workOrderStatus;


        private Integer onShelfType;


        private Integer membership;


        private Integer workOrderType;


        private Integer complaintLevel;


        private String createUserId;


        private String updateUserId;

        private List<Integer> businessTypes;


        private List<Integer> workOrderStatuses;


        private List<Integer> onShelfTypes;

        private List<Integer> memberships;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class PageBO {


        private Integer businessType;


        private Integer workOrderStatus;


        private Integer onShelfType;


        private String createUserId;


        private String updateUserId;


        /**
         * 页码，从1开始
         */
        private Integer pageIndex;
        /**
         * 每页数量
         */
        private Integer pageSize;

    }

}

