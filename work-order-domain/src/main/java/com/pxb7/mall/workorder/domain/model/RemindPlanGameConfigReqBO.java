package com.pxb7.mall.workorder.domain.model;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.ToString;


/**
 * 预警计划游戏配置(RemindPlanGameConfig)实体类
 *
 * <AUTHOR>
 * @since 2025-03-24 21:11:14
 */
public class RemindPlanGameConfigReqBO {

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class AddBO {


        private String gameId;


        private String gameName;


        private Integer maker;


        private Integer channel;


        private TimeConfigBO expectCompleteTimeConfig;


        private TimeConfigBO imCountDownTimeConfig;


        private Long remindPlanId;


        private Long remindSubPlanId;


        private String createUserId;


        private String updateUserId;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class UpdateBO {


        private Long id;


        private String gameId;


        private String gameName;


        private Integer maker;


        private Integer channel;


        private TimeConfigBO expectCompleteTimeConfig;


        private TimeConfigBO imCountDownTimeConfig;


        private Long remindPlanId;


        private Long remindSubPlanId;


        private String createUserId;


        private String updateUserId;


    }


    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class DelBO {
        private Long id;
    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class SearchBO {

        private String gameId;


        private String gameName;


        private Integer maker;


        private Integer channel;


        private TimeConfigBO expectCompleteTimeConfig;


        private TimeConfigBO imCountDownTimeConfig;


        private Long remindPlanId;


        private Long remindSubPlanId;


        private String createUserId;


        private String updateUserId;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class PageBO {


        private String gameId;


        private String gameName;


        private Integer maker;


        private Integer channel;


        private TimeConfigBO expectCompleteTimeConfig;


        private TimeConfigBO imCountDownTimeConfig;


        private Long remindPlanId;


        private Long remindSubPlanId;


        private String createUserId;


        private String updateUserId;


        /**
         * 页码，从1开始
         */
        private Integer pageIndex;
        /**
         * 每页数量
         */
        private Integer pageSize;

    }

}

