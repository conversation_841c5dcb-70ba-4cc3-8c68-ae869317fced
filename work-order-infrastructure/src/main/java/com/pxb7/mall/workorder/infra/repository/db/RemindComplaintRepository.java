package com.pxb7.mall.workorder.infra.repository.db;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.pxb7.mall.workorder.infra.model.RemindComplaintReqPO;
import com.pxb7.mall.workorder.infra.repository.db.entity.RemindComplaint;

import java.util.List;

/**
 * 客诉工单预警记录(RemindComplaint)表服务接口
 *
 * <AUTHOR>
 * @since 2025-04-24 23:31:56
 */
public interface RemindComplaintRepository extends IService<RemindComplaint> {

    boolean insert(RemindComplaintReqPO.AddPO param);

    boolean updateByWorkOrderId(RemindComplaintReqPO.UpdatePO param);

    RemindComplaint findByWorkOrderId(String workOrderId, String remindId);

    Page<RemindComplaint> page(RemindComplaintReqPO.PagePO pagePO);

    boolean batchUpdateTimeoutStatusByIds(List<Long> ids, Integer status);

    RemindComplaint getByRemindId(String remindId);
}

