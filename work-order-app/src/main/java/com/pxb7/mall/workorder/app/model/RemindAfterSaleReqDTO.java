package com.pxb7.mall.workorder.app.model;

import java.time.*;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Min;
import lombok.ToString;
import org.hibernate.validator.constraints.Range;

/**
 * 售后工单预警记录(RemindAfterSale)实体类
 *
 * <AUTHOR>
 * @since 2025-04-24 23:33:20
 */
public class RemindAfterSaleReqDTO {

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class AddDTO {


        @NotBlank(message = "remindId不能为空")
        private String remindId;


        @NotBlank(message = "workOrderId不能为空")
        private String workOrderId;


        @NotBlank(message = "productId不能为空")
        private String productId;


        @NotBlank(message = "productCode不能为空")
        private String productCode;


        @NotBlank(message = "gameId不能为空")
        private String gameId;


        @NotBlank(message = "groupId不能为空")
        private String groupId;


        @NotBlank(message = "orderId不能为空")
        private String orderId;


        @NotBlank(message = "orderItemId不能为空")
        private String orderItemId;


        @NotNull(message = "completeStatus不能为空")
        private Integer completeStatus;


        @NotNull(message = "timeOutStatus不能为空")
        private Integer timeOutStatus;


        @NotBlank(message = "retrieveUserId不能为空")
        private String retrieveUserId;


        @NotBlank(message = "disputeUserId不能为空")
        private String disputeUserId;


        @NotNull(message = "expectCompleteTime不能为空")
        private LocalDateTime expectCompleteTime;


        @NotNull(message = "completeTime不能为空")
        private LocalDateTime completeTime;


        @NotNull(message = "gameConfigId不能为空")
        private Long gameConfigId;


        @NotNull(message = "remindPlanId不能为空")
        private Long remindPlanId;


        @NotNull(message = "remindSubPlanId不能为空")
        private Long remindSubPlanId;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class UpdateDTO {


        @NotNull(message = "id不能为空")
        private Long id;


        @NotBlank(message = "remindId不能为空")
        private String remindId;


        @NotBlank(message = "workOrderId不能为空")
        private String workOrderId;


        @NotBlank(message = "productId不能为空")
        private String productId;


        @NotBlank(message = "productCode不能为空")
        private String productCode;


        @NotBlank(message = "gameId不能为空")
        private String gameId;


        @NotBlank(message = "groupId不能为空")
        private String groupId;


        @NotBlank(message = "orderId不能为空")
        private String orderId;


        @NotBlank(message = "orderItemId不能为空")
        private String orderItemId;


        @NotNull(message = "completeStatus不能为空")
        private Integer completeStatus;


        @NotNull(message = "timeOutStatus不能为空")
        private Integer timeOutStatus;


        @NotBlank(message = "retrieveUserId不能为空")
        private String retrieveUserId;


        @NotBlank(message = "disputeUserId不能为空")
        private String disputeUserId;


        @NotNull(message = "expectCompleteTime不能为空")
        private LocalDateTime expectCompleteTime;


        @NotNull(message = "completeTime不能为空")
        private LocalDateTime completeTime;


        @NotNull(message = "gameConfigId不能为空")
        private Long gameConfigId;


        @NotNull(message = "remindPlanId不能为空")
        private Long remindPlanId;


        @NotNull(message = "remindSubPlanId不能为空")
        private Long remindSubPlanId;


    }


    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class DelDTO {
        @NotNull(message = "id不能为空")
        private Long id;
    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class SearchDTO {

        @NotBlank(message = "remindId不能为空")
        private String remindId;


        @NotBlank(message = "workOrderId不能为空")
        private String workOrderId;


        @NotBlank(message = "productId不能为空")
        private String productId;


        @NotBlank(message = "productCode不能为空")
        private String productCode;


        @NotBlank(message = "gameId不能为空")
        private String gameId;


        @NotBlank(message = "groupId不能为空")
        private String groupId;


        @NotBlank(message = "orderId不能为空")
        private String orderId;


        @NotBlank(message = "orderItemId不能为空")
        private String orderItemId;


        @NotNull(message = "completeStatus不能为空")
        private Integer completeStatus;


        @NotNull(message = "timeOutStatus不能为空")
        private Integer timeOutStatus;


        @NotBlank(message = "retrieveUserId不能为空")
        private String retrieveUserId;


        @NotBlank(message = "disputeUserId不能为空")
        private String disputeUserId;


        @NotNull(message = "expectCompleteTime不能为空")
        private LocalDateTime expectCompleteTime;


        @NotNull(message = "completeTime不能为空")
        private LocalDateTime completeTime;


        @NotNull(message = "gameConfigId不能为空")
        private Long gameConfigId;


        @NotNull(message = "remindPlanId不能为空")
        private Long remindPlanId;


        @NotNull(message = "remindSubPlanId不能为空")
        private Long remindSubPlanId;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class PageDTO {

        /**
         * 商品编号
         */
        private String productCode;

        /**
         * 订单编号
         */
        private String orderItemId;

        /**
         * 找回工单编号
         */
        private String workOrderId;

        /**
         * 工单类型，1:找回，2:纠纷
         */
        @NotNull(message = "工单类型不能为空")
        @Range(min = 1,max = 2,message = "工单类型参数错误")
        private Integer workOrderType;

        /**
         * 页码，从1开始
         */
        @NotNull(message = "pageIndex不能为空")
        @Min(value = 1, message = "pageIndex不能小于1")
        private Integer pageIndex;
        /**
         * 每页数量 默认10
         */
        @NotNull(message = "pageSize不能为空")
        private Integer pageSize;

    }

}

