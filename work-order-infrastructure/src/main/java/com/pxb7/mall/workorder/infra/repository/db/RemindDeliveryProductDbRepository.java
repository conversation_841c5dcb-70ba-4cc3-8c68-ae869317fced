package com.pxb7.mall.workorder.infra.repository.db;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pxb7.mall.components.env.profile.PxProfileHelper;
import com.pxb7.mall.workorder.infra.mapping.DeliveryProductMapping;
import com.pxb7.mall.workorder.infra.model.RemindDeliveryProductReqPO;
import com.pxb7.mall.workorder.infra.repository.db.entity.RemindDeliveryProduct;
import com.pxb7.mall.workorder.infra.repository.db.mapper.RemindDeliveryProductMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Objects;

/**
 * 账号交付预警记录(RemindDeliveryProduct)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-27 20:07:09
 */
@Slf4j
@Repository
public class RemindDeliveryProductDbRepository extends ServiceImpl<RemindDeliveryProductMapper, RemindDeliveryProduct> implements RemindDeliveryProductRepository {

    @Override
    public boolean insert(RemindDeliveryProductReqPO.AddPO param) {
        RemindDeliveryProduct entity = DeliveryProductMapping.INSTANCE.addDto2PO(param);
        return this.save(entity);
    }

    @Override
    public boolean updateByOrderItemId(RemindDeliveryProductReqPO.UpdatePO param) {
        LambdaUpdateWrapper<RemindDeliveryProduct> updateWrapper = new LambdaUpdateWrapper<>();
        //where
        updateWrapper.eq(RemindDeliveryProduct::getOrderItemId, param.getOrderItemId());
        if (StringUtils.isNotBlank(param.getRemindId())) {
            updateWrapper.eq(RemindDeliveryProduct::getRemindId, param.getRemindId());
        }
        //set
        if (Objects.nonNull(param.getCompleteStatus())) {
            updateWrapper.set(RemindDeliveryProduct::getCompleteStatus, param.getCompleteStatus());
        }
        if (Objects.nonNull(param.getDeliveryStatus())) {
            updateWrapper.set(RemindDeliveryProduct::getDeliveryStatus, param.getDeliveryStatus());
        }
        if (Objects.nonNull(param.getTimeOutStatus())) {
            updateWrapper.set(RemindDeliveryProduct::getTimeOutStatus, param.getTimeOutStatus());
        }
        if (Objects.nonNull(param.getCompleteTime())) {
            updateWrapper.set(RemindDeliveryProduct::getCompleteTime, param.getCompleteTime());
        }
        if (Objects.nonNull(param.getPauseTime())) {
            updateWrapper.set(RemindDeliveryProduct::getPauseTime, param.getPauseTime());
        }
        if (Objects.nonNull(param.getExpectCompleteTime())) {
            updateWrapper.set(RemindDeliveryProduct::getExpectCompleteTime, param.getExpectCompleteTime());
        }
        if (Objects.nonNull(param.getDeliveryCustomerCare())) {
            updateWrapper.set(RemindDeliveryProduct::getDeliveryCustomerCare, param.getDeliveryCustomerCare());
        }
        if (Objects.nonNull(param.getGroupId())) {
            updateWrapper.set(RemindDeliveryProduct::getGroupId, param.getGroupId());
        }
        return this.update(updateWrapper);
    }

    /**
     * 账号交付预警记录
     * @param orderItemId
     * @return
     */
    @Override
    public RemindDeliveryProduct findByOrderItemId(String orderItemId, String remindId) {
        LambdaQueryWrapper<RemindDeliveryProduct> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RemindDeliveryProduct::getOrderItemId, orderItemId);
        if (StringUtils.isNotBlank(remindId)) {
            queryWrapper.eq(RemindDeliveryProduct::getRemindId, remindId);
        }
        return this.getOne(queryWrapper);
    }

    @Override
    public RemindDeliveryProduct getByRemindId(String remindId) {
        if (StringUtils.isBlank(remindId)) {
            return null;
        }
        return this.lambdaQuery().eq(RemindDeliveryProduct::getRemindId, remindId).one();
    }


    @Override
    public Page<RemindDeliveryProduct> page(RemindDeliveryProductReqPO.PagePO param) {
        LambdaQueryWrapper<RemindDeliveryProduct> queryWrapper = new LambdaQueryWrapper<>();
        // 返回分页查询结果
        queryWrapper.eq(RemindDeliveryProduct::getEnvProfile, PxProfileHelper.getProfile().getProfileValue());
        queryWrapper.gt(RemindDeliveryProduct::getId, param.getId());
        queryWrapper.lt(RemindDeliveryProduct::getExpectCompleteTime, param.getExpectCompleteTime());
        queryWrapper.eq(RemindDeliveryProduct::getCompleteStatus, param.getCompleteStatus());
        queryWrapper.ne(RemindDeliveryProduct::getTimeOutStatus, param.getTimeOutStatus());
        queryWrapper.orderByAsc(RemindDeliveryProduct::getId);
        return this.page(new Page<>(param.getPageIndex(), param.getPageSize()), queryWrapper);
    }

    /**
     * 批量更新状态
     * @param ids
     * @param status
     * @return
     */
    @Override
    public boolean batchUpdateTimeoutStatusByIds(List<Long> ids, Integer status) {
        return this.getBaseMapper().batchUpdateTimeoutStatusByIds(ids, status) > 0;
    }

}
