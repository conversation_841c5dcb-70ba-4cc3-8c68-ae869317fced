package com.pxb7.mall.workorder.infra.repository.db.mapper;

import com.pxb7.mall.workorder.infra.model.AfterSaleStatisticDataPO;
import com.pxb7.mall.workorder.infra.model.AfterSaleStatisticSearchPO;
import com.pxb7.mall.workorder.infra.repository.db.entity.AdsPxb7AfterSaleForewarning;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 售后找回纠纷预警统计(AdsPxb7AfterSaleForewarning)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-05-08 18:13:53
 */
@Mapper
public interface AdsPxb7AfterSaleForewarningMapper extends BaseMapper<AdsPxb7AfterSaleForewarning> {
    /**
     * 批量新增数据
     *
     * @param entities List<AdsPxb7AfterSaleForewarning> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<AdsPxb7AfterSaleForewarning> entities);

    List<AfterSaleStatisticDataPO> countBySearchParam(@Param("param") AfterSaleStatisticSearchPO param);

    LocalDateTime getLastDataInsertTime();
}
