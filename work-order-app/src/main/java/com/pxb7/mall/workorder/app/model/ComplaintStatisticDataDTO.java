package com.pxb7.mall.workorder.app.model;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Getter
@Setter
@Accessors(chain = true)
@ToString
public class ComplaintStatisticDataDTO {

    /**
     * 日期
     */
    private String createDate;

    /**
     * 客诉工单数
     */
    private Integer workOrderCnt;

    /**
     * 已完结工单数
     */
    private Integer completeWorkOrderCnt;

    /**
     * 处理中工单数
     */
    private Integer processingWorkOrderCnt;

    /**
     * 即将超时 (待接单即将超时/已接单即将超时/待跟进即将超时...)
     */
    private Integer timeoutingWorkOrderCnt;

    /**
     * 已超时数量 (待接单已超时/已接单已超时/待跟进已超时...)
     */
    private Integer timeoutedWorkOrderCnt;

    /**
     * 超时率 (待接单超时率/已接单超时率/待跟进超时率...)
     */
    private BigDecimal timeoutRate;

}
