package com.pxb7.mall.workorder.app.model;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Getter
@Setter
@Accessors(chain = true)
@ToString
public class WorkOrderStatisticDataDTO {

    /**
     * 日期
     */
    private String date;


    /**
     * 处理总数量（待接单总数/已跟进总数...）
     */
    private Integer processTotalCount;

    /**
     * 已完成数量 （已接单数/已完结数...）
     */
    private Integer completeCount;


    /**
     *  已失效数量
     */
    private Integer invalidCount;


    /**
     * 即将超时 (待接单即将超时/已接单即将超时/待跟进即将超时...)
     */
    private Integer willTimeoutCount;

    /**
     * 已超时数量  (待接单已超时/已接单已超时/待跟进已超时...)
     */
    private Integer timeoutCount;


    /**
     * 超时率  (待接单超时率/已接单超时率/待跟进超时率...)
     */
    private BigDecimal timeoutRate;


    /**
     * 游戏id
     */
    private String gameId;

    /**
     * 游戏名称
     */
    private String gameName;


    /**
     * 上架方式：1:官方截图，2:自主截图，
     */
    private String onShelfType;


    /**
     * 客服id
     */
    private String customerCareId;

    /**
     * 客服名称
     */
    private String customerCareName;


    /**
     * 接单美工Id
     */
    private String artDesignerId;

    /**
     * 接单美工名称
     */
    private String artDesignerName;

    /**
     * 跟进人Id
     */
    private String followerId;

    /**
     * 跟进人名称
     */
    private String followerName;

}
