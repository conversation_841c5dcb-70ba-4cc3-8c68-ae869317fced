package com.pxb7.mall.workorder.app.handler;

import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Component
public class DbRecordSyncHandlerFactory {

    @Resource
    private List<AbstractDbRecordSyncHandler> syncHandlerList;

    public AbstractDbRecordSyncHandler getSyncHandler(String tableName) {
        if (StringUtils.isBlank(tableName)) {
            return null;
        }
        if (CollectionUtils.isEmpty(syncHandlerList)) {
            return null;
        }

        for (AbstractDbRecordSyncHandler handler : syncHandlerList) {
            if (handler.getTableName().equals(tableName)) {
                return handler;
            }
        }
        return null;
    }

}
