package com.pxb7.mall.workorder.infra.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum RemindPlanServiceTypeEnum {

    DELIVERY_PRODUCT(1, "账号交付服务"),
    WORK_ORDER(2, "商品工单服务"),
    ASS_ORDER(3, "售后工单服务"),
    COMPLAIN_ORDER(4, "客诉工单服务"),
    ;

    private final Integer value;

    private final String label;

    public static String getLabel(Integer value) {
        return Arrays.stream(RemindPlanServiceTypeEnum.values()).filter(e -> e.getValue().equals(value))
            .map(RemindPlanServiceTypeEnum::getLabel).findAny().orElse("");
    }

    public static RemindPlanServiceTypeEnum getEnum(Integer value) {
        if (null == value) {
            return null;
        }
        return Arrays.stream(RemindPlanServiceTypeEnum.values()).filter(e -> value.equals(e.getValue())).findFirst()
            .orElse(null);
    }

}
