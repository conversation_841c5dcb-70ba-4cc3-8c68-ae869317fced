package com.pxb7.mall.workorder.adapter.consumer;

import com.alibaba.cola.exception.Assert;
import com.alibaba.fastjson.JSON;
import com.pxb7.mall.components.rocketmq.RocketMQListenerExt;
import com.pxb7.mall.workorder.app.service.RemindPlanRecordAppService;
import com.pxb7.mall.workorder.client.enums.BizErrorCodeEnum;
import com.pxb7.mall.workorder.client.enums.PlanExecuteStatusEnum;
import com.pxb7.mall.workorder.client.message.RemindPlanMessage;
import com.pxb7.mall.workorder.domain.helper.TransactionHelper;
import com.pxb7.mall.workorder.infra.constant.CommonConstants;
import com.pxb7.mall.workorder.infra.constant.RMQConstant;
import com.pxb7.mall.workorder.infra.util.RedissonUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.annotation.RocketMQMessageListener;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 消费预警计划消息
 * <AUTHOR>
 */
@Slf4j
@Service
@RocketMQMessageListener(
        consumerGroup = RMQConstant.WORK_ORDER_SERVICE_GROUP,
        topic = RMQConstant.REMIND_EVENT_TOPIC,
        tag = RMQConstant.REMIND_MESSAGE_TAG)
public class RemindPlanMessageConsumer implements RocketMQListenerExt<RemindPlanMessage> {

    @Resource
    private RemindPlanRecordAppService planRecordAppService;

    @Autowired
    private TransactionHelper transactionHelper;

    @Override
    public ConsumeResult consume(MessageView messageView, RemindPlanMessage remindPlanMessage) {
        log.info("监听到预警计划消息: messageId = {},message = {}", messageView.getMessageId(), JSON.toJSONString(remindPlanMessage));
        //增加限流，限制消费速度，tps最大为50
        RedissonUtils.rateLimit(CommonConstants.REMIND_CONSUME_RATE_LIMITER_KEY, 50);

        try {
            PlanExecuteStatusEnum executeStatus = planRecordAppService.handleRemindMessage(remindPlanMessage);
            if (PlanExecuteStatusEnum.PAUSE.getCode().equals(executeStatus.getCode())) {
                return ConsumeResult.SUCCESS;
            }
            transactionHelper.execute(null, e -> {
                //更新预警执行计划状态
                planRecordAppService.updatePlanRecordStatus(remindPlanMessage, executeStatus);
                //更新预警即将超时状态
                planRecordAppService.updateWillTimeOutStatus(remindPlanMessage);
                return Boolean.TRUE;
            }, (e, flag) -> Assert.isTrue(flag,
                    BizErrorCodeEnum.REMIND_PLAN_RECORD_STATUS_UPDATE_ERROR.getErrCode(),
                    BizErrorCodeEnum.REMIND_PLAN_RECORD_STATUS_UPDATE_ERROR.getErrDesc()));
            return ConsumeResult.SUCCESS;
        } catch (Exception e) {
            log.error("error in consumed from remind_event_topic: message = " + JSON.toJSONString(remindPlanMessage), e);
            return ConsumeResult.FAILURE;
        }
    }

}

