package com.pxb7.mall.workorder.infra.util;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.JsonToken;
import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.module.SimpleModule;

import java.io.IOException;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;

public final class ObjectMapperUtil {

    // 私有静态变量持有唯一实例
    private static final ObjectMapper instance;

    static {
        instance = new ObjectMapper();
        // 收到未知属性时不报异常
        instance.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        // 反序列化时，属性不存在的兼容处理
        instance.getDeserializationConfig().withoutFeatures(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);

        SimpleModule simpleModule =
            new SimpleModule().addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer())

                // .addSerializer(Long.class, ToStringSerializer.instance) // 对于long类型转为String
                // .addSerializer(Long.TYPE, ToStringSerializer.instance) // 对于long类型转为String
                .addSerializer(LocalDateTime.class, new LocalDateTimeSerializer());

        instance.registerModule(simpleModule);
    }

    /**
     * @param bodyString
     * @param clazz
     * @param <T>
     * @return
     * @throws JsonProcessingException
     */
    public static <T> T fromJson(String bodyString, Class<T> clazz) throws JsonProcessingException {
        return instance.readValue(bodyString, clazz);
    }

    public static JsonNode readTree(String msg) throws JsonProcessingException {
        return instance.readTree(msg);
    }

    /**
     * 序列化
     */
    private static class LocalDateTimeSerializer extends JsonSerializer<LocalDateTime> {
        @Override
        public void serialize(LocalDateTime value, JsonGenerator gen, SerializerProvider serializers)
            throws IOException {
            if (value != null) {
                gen.writeString(LocalDateTimeUtil.format(value, DatePattern.NORM_DATETIME_FORMATTER));
            }
        }
    }

    /**
     * 反序列化
     */
    private static class LocalDateTimeDeserializer extends JsonDeserializer<LocalDateTime> {
        @Override
        public LocalDateTime deserialize(JsonParser p, DeserializationContext deserializationContext)
            throws IOException {

            JsonToken token = p.getCurrentToken();
            if (token == JsonToken.VALUE_STRING) {
                // 解析 "yyyy-MM-dd HH:mm:ss" 格式的字符串
                return LocalDateTimeUtil.parse(p.getValueAsString(), DatePattern.NORM_DATETIME_FORMATTER);
            } else if (token == JsonToken.VALUE_NUMBER_INT || token == JsonToken.VALUE_NUMBER_FLOAT) {
                // 解析毫秒级时间戳
                return LocalDateTime.ofInstant(Instant.ofEpochMilli(p.getLongValue()), ZoneId.systemDefault());
            }
            throw new UnsupportedOperationException("不能反序列：" + token);
        }
    }

}
