package com.pxb7.mall.workorder.client.enums;

import lombok.Getter;
import lombok.ToString;

/**
 * 执行状态
 * <AUTHOR>
 */
@Getter
@ToString
public enum PlanExecuteStatusEnum {

    /**
     * 执行状态，0:待执行，1:执行中，2:已执行，3:执行失败，4:已过期，5:已失效，6:防打扰失效
     */
    WILL_EXECUTE(0, "待执行"),
    IN_PROCESS(1, "执行中"),
    DONE(2, "已执行"),
    FAILED(3, "执行失败"),
    EXPIRED(4, "已过期"),
    INVALID(5, "已失效"),
    INVALID_NOT_DISTURB(6, "防打扰失效"),
    PAUSE(7, "暂停")
    ;

    private final Integer code;
    private final String desc;

    PlanExecuteStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}
