package com.pxb7.mall.workorder.domain.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pxb7.mall.workorder.domain.mapping.RemindPlanRetryTaskDomainMapping;
import com.pxb7.mall.workorder.domain.model.RemindPlanRetryTaskReqBO;
import com.pxb7.mall.workorder.domain.model.RemindPlanRetryTaskRespBO;
import com.pxb7.mall.workorder.infra.model.RemindPlanRetryTaskReqPO;
import com.pxb7.mall.workorder.infra.repository.db.RemindPlanRetryTaskRepository;
import com.pxb7.mall.workorder.infra.repository.db.entity.RemindPlanRetryTask;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * 生成提醒计划异常重试表domain服务
 *
 * <AUTHOR>
 * @since 2025-05-06 20:54:42
 */
@Service
public class RemindPlanRetryTaskDomainService {

    @Resource
    private RemindPlanRetryTaskRepository remindPlanRetryTaskRepository;

    public boolean insert(RemindPlanRetryTaskReqBO.AddBO param) {
        RemindPlanRetryTaskReqPO.AddPO addPO = RemindPlanRetryTaskDomainMapping.INSTANCE.remindPlanRetryTaskBO2AddPO(param);
        return remindPlanRetryTaskRepository.insert(addPO);
    }

    public boolean batchUpdate(List<RemindPlanRetryTaskReqBO.UpdateBO> param) {
        List<RemindPlanRetryTaskReqPO.UpdatePO> updatePO = RemindPlanRetryTaskDomainMapping.INSTANCE.remindPlanRetryTaskBO2UpdatePOList(param);
        return remindPlanRetryTaskRepository.batchUpdate(updatePO);
    }

    public Page<RemindPlanRetryTaskRespBO.DetailBO> page(RemindPlanRetryTaskReqBO.PageBO param) {
        RemindPlanRetryTaskReqPO.PagePO pagePO = RemindPlanRetryTaskDomainMapping.INSTANCE.remindPlanRetryTaskBO2PagePO(param);
        Page<RemindPlanRetryTask> page = remindPlanRetryTaskRepository.page(pagePO);
        return RemindPlanRetryTaskDomainMapping.INSTANCE.remindPlanRetryTaskPO2PageBO(page);
    }

}

