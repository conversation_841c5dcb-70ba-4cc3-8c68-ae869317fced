package com.pxb7.mall.workorder.infra.repository.db;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pxb7.mall.workorder.infra.model.RemindSubPlanReqPO;
import com.pxb7.mall.workorder.infra.repository.db.entity.RemindSubPlan;

/**
 * 提醒服务预警子计划(RemindSubPlan)表服务接口
 *
 * <AUTHOR>
 * @since 2025-03-24 21:09:56
 */
public interface RemindSubPlanRepository extends IService<RemindSubPlan> {

    boolean insert(RemindSubPlanReqPO.AddPO param);

    boolean update(RemindSubPlanReqPO.UpdatePO param);

    boolean deleteById(RemindSubPlanReqPO.DelPO param);

    RemindSubPlan findById(Long id);

    List<RemindSubPlan> list(RemindSubPlanReqPO.SearchPO param);

    Page<RemindSubPlan> page(RemindSubPlanReqPO.PagePO param);

    boolean saveBatch(List<RemindSubPlanReqPO.AddPO> params);
}

