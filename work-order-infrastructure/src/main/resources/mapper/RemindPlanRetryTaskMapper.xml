<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pxb7.mall.workorder.infra.repository.db.mapper.RemindPlanRetryTaskMapper">
    <resultMap id="BaseResultMap" type="com.pxb7.mall.workorder.infra.repository.db.entity.RemindPlanRetryTask">
        <result column="id" jdbcType="BIGINT" property="id"/>
        <result column="biz_id" jdbcType="VARCHAR" property="bizId"/>
        <result column="service_type" jdbcType="INTEGER" property="serviceType"/>
        <result column="record_info" jdbcType="VARCHAR" property="recordInfo"/>
        <result column="retry_times" jdbcType="INTEGER" property="retryTimes"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into remind_plan_retry_task(biz_id,service_type,record_info,retry_times,status,create_time,update_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.bizId},#{entity.serviceType},#{entity.recordInfo},#{entity.retryTimes},#{entity.status},#{entity.createTime},#{entity.updateTime})
        </foreach>
    </insert>
</mapper>

