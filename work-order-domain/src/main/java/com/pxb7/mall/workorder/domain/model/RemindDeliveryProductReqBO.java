package com.pxb7.mall.workorder.domain.model;

import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;


/**
 * 账号交付预警记录(RemindDeliveryProduct)实体类
 *
 * <AUTHOR>
 * @since 2025-03-27 20:07:52
 */
public class RemindDeliveryProductReqBO {

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class AddBO {

        /**
         * 预警id
         */
        private String remindId;

        /**
         * 商品id
         */
        private String productId;
        /**
         * 商品编码
         */
        private String productCode;
        /**
         * 游戏id
         */
        private String gameId;
        /**
         * 群组id
         */
        private String groupId;
        /**
         * 订单id
         */
        private String orderId;
        /**
         * 子订单id
         */
        private String orderItemId;
        /**
         * 完结状态,1:未完结,2:已完结,3:终止
         */
        private Integer completeStatus;
        /**
         * 交付状态
         */
        private Integer deliveryStatus;
        /**
         * 超时状态，0: 无 1:即将超时 2:已超时
         */
        private Integer timeOutStatus;
        /**
         * 交付客服
         */
        private String deliveryCustomerCare;
        /**
         * 预期完结时间
         */
        private LocalDateTime expectCompleteTime;
        /**
         * im客服端倒计时开始时间
         */
        private LocalDateTime imCountDownTime;
        /**
         * 预警计划游戏配置id,remind_plan_game_config
         */
        private Long gameConfigId;
        /**
         * 预警计划id,remind_plan
         */
        private Long remindPlanId;
        /**
         * 预警子计划id,remind_sub_plan
         */
        private Long remindSubPlanId;
        /**
         * 创建人
         */
        private String createUserId;
        /**
         * 更新人
         */
        private String updateUserId;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class UpdateBO {


        private Long id;


        private String remindId;


        private String productId;


        private String productCode;


        private String gameId;


        private String groupId;


        private String orderId;


        private String orderItemId;

        /**
         * 完结状态,1:未完结,2:已完结,3:终止
         */
        private Integer completeStatus;

        /**
         * 交付状态
         */
        private Integer deliveryStatus;

        /**
         * 超时状态，0: 无 1:即将超时 2:已超时
         */
        private Integer timeOutStatus;


        private String deliveryCustomerCare;


        private LocalDateTime expectCompleteTime;


        private LocalDateTime imCountDownTime;

        private LocalDateTime payTime;

        private LocalDateTime completeTime;

        private LocalDateTime pauseTime;

        private Long gameConfigId;


        private Long remindPlanId;


        private Long remindSubPlanId;


        private String createUserId;


        private String updateUserId;


    }


    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class DelBO {
        private Long id;
    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class SearchBO {

        private String remindId;


        private String productId;


        private String productCode;


        private String gameId;


        private String groupId;


        private String orderId;


        private String orderItemId;


        private Integer completeStatus;


        private Integer timeOutStatus;


        private String deliveryCustomerCare;


        private LocalDateTime expectCompleteTime;


        private LocalDateTime imCountDownTime;


        private Long gameConfigId;


        private Long remindPlanId;


        private Long remindSubPlanId;


        private String createUserId;


        private String updateUserId;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class PageBO {

        private Long id;

        private String remindId;


        private String productId;


        private String productCode;


        private String gameId;


        private String groupId;


        private String orderId;


        private String orderItemId;


        private Integer completeStatus;


        private Integer timeOutStatus;


        private String deliveryCustomerCare;


        private LocalDateTime expectCompleteTime;


        private LocalDateTime imCountDownTime;


        private Long gameConfigId;


        private Long remindPlanId;


        private Long remindSubPlanId;


        private String createUserId;


        private String updateUserId;


        /**
         * 页码，从1开始
         */
        private long pageIndex;
        /**
         * 每页数量
         */
        private long pageSize;

    }

}

