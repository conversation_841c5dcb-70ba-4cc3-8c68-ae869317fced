package com.pxb7.mall.workorder.domain.model;

import java.util.Date;
import java.time.*;
import java.math.BigDecimal;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.ToString;


/**
 * 客诉工单预警记录(RemindComplaint)实体类
 *
 * <AUTHOR>
 * @since 2025-04-24 23:32:52
 */
public class RemindComplaintReqBO {

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class AddBO {


        private String remindId;


        private String workOrderId;


        private String workOrderTitle;


        private String complaintContent;


        private String groupId;


        private Integer completeStatus;


        private Integer timeOutStatus;


        private String handleUserId;


        private LocalDateTime expectCompleteTime;

        /**
         * im客服端倒计时开始时间
         */
        private LocalDateTime imCountDownTime;

        private LocalDateTime completeTime;


        private Long gameConfigId;


        private Long remindPlanId;


        private Long remindSubPlanId;


        private String createUserId;


        private String updateUserId;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class UpdateBO {


        private Long id;


        private String remindId;


        private String workOrderId;


        private String workOrderTitle;


        private String complaintContent;


        private String groupId;


        private Integer completeStatus;


        private Integer timeOutStatus;


        private String handleUserId;


        private LocalDateTime expectCompleteTime;


        private LocalDateTime completeTime;


        private Long gameConfigId;


        private Long remindPlanId;


        private Long remindSubPlanId;


        private String createUserId;


        private String updateUserId;


    }


    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class DelBO {
        private Long id;
    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class SearchBO {

        private String remindId;


        private String workOrderId;


        private String workOrderTitle;


        private String complaintContent;


        private String groupId;


        private Integer completeStatus;


        private Integer timeOutStatus;


        private String handleUserId;


        private LocalDateTime expectCompleteTime;


        private LocalDateTime completeTime;


        private Long gameConfigId;


        private Long remindPlanId;


        private Long remindSubPlanId;


        private String createUserId;


        private String updateUserId;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class PageBO {

        private Long id;

        private String remindId;


        private String workOrderId;


        private String workOrderTitle;


        private String complaintContent;


        private String groupId;


        private Integer completeStatus;


        private Integer timeOutStatus;


        private String handleUserId;


        private LocalDateTime expectCompleteTime;


        private LocalDateTime completeTime;


        private Long gameConfigId;


        private Long remindPlanId;


        private Long remindSubPlanId;


        private String createUserId;


        private String updateUserId;


        /**
         * 页码，从1开始
         */
        private long pageIndex;
        /**
         * 每页数量
         */
        private long pageSize;

    }

}

