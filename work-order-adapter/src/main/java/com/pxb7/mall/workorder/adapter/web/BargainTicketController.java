package com.pxb7.mall.workorder.adapter.web;

import cn.dev33.satoken.annotation.SaIgnore;
import com.alibaba.cola.dto.SingleResponse;
import com.pxb7.mall.response.PxPageResponse;
import com.pxb7.mall.workorder.app.model.BargainTicketPageDetailDTO;
import com.pxb7.mall.workorder.app.model.BargainTicketReqDTO;
import com.pxb7.mall.workorder.app.model.BargainTicketRespDTO;
import com.pxb7.mall.workorder.app.service.BargainTicketAppService;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


/**
 * 还价工单
 *
 * <AUTHOR>
 * @version : BargainTicketController.java, v 0.1 2025年04月16日 14:25 yang.xuexi Exp $
 */
@RestController
@RequestMapping("/web/bargain-ticket/")
public class BargainTicketController {


    @Resource
    private BargainTicketAppService bargainTicketAppService;

    /**
     * 分页查询还价工单列表信息
     *
     * @param pageDTO 分页查询参数
     * @return 分页星系
     */
    @PostMapping("page")
    public PxPageResponse<BargainTicketPageDetailDTO> page(@RequestBody @Validated BargainTicketReqDTO.PageDTO pageDTO) {
        return bargainTicketAppService.page(pageDTO);
    }


    /**
     * 工单详情接口
     *
     * @param receiveId 工单id
     * @return 返回工单详情买家信息和买家近七日工单
     */
    @GetMapping("detail")
    public SingleResponse<BargainTicketRespDTO> detail(@RequestParam(value = "receiveId") String receiveId) {
        return SingleResponse.of(bargainTicketAppService.detail(receiveId));
    }


    @SaIgnore
    @GetMapping("/inner/detail")
    public SingleResponse<BargainTicketRespDTO> innerDetail() {
        return SingleResponse.of(bargainTicketAppService.detail("172649168379986"));
    }




}
