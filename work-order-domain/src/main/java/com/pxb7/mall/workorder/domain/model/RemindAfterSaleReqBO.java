package com.pxb7.mall.workorder.domain.model;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;


/**
 * 售后工单预警记录(RemindAfterSale)实体类
 *
 * <AUTHOR>
 * @since 2025-04-24 23:32:52
 */
public class RemindAfterSaleReqBO {

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class AddBO {


        private String remindId;


        private String workOrderId;

        /**
         * 工单编号
         */
        private String workOrderNo;

        private String productId;


        private String productCode;


        private String gameId;


        private String groupId;


        private String orderId;


        private String orderItemId;


        private Integer completeStatus;


        private Integer timeOutStatus;


        private String retrieveUserId;


        private String disputeUserId;

        /**
         * 工单类型  1:找回 2:纠纷
         */
        private Integer workOrderType;

        private LocalDateTime expectCompleteTime;

        /**
         * im客服端倒计时开始时间
         */
        private LocalDateTime imCountDownTime;


        private LocalDateTime completeTime;


        private Long gameConfigId;


        private Long remindPlanId;


        private Long remindSubPlanId;


        private String createUserId;


        private String updateUserId;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class UpdateBO {


        private Long id;


        private String remindId;


        private String workOrderId;


        private String productId;


        private String productCode;


        private String gameId;


        private String groupId;


        private String orderId;


        private String orderItemId;


        private Integer completeStatus;


        private Integer timeOutStatus;


        private String retrieveUserId;


        private String disputeUserId;


        private LocalDateTime expectCompleteTime;


        private LocalDateTime completeTime;


        private Long gameConfigId;


        private Long remindPlanId;


        private Long remindSubPlanId;


        private String createUserId;


        private String updateUserId;


    }


    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class DelBO {
        private Long id;
    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class SearchBO {

        private String remindId;


        private String workOrderId;


        private String productId;


        private String productCode;


        private String gameId;


        private String groupId;


        private String orderId;


        private String orderItemId;


        private Integer completeStatus;


        private Integer timeOutStatus;


        private String retrieveUserId;


        private String disputeUserId;


        private LocalDateTime expectCompleteTime;


        private LocalDateTime completeTime;


        private Long gameConfigId;


        private Long remindPlanId;


        private Long remindSubPlanId;


        private String createUserId;


        private String updateUserId;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class PageBO {

        private Long id;

        private String remindId;


        private String workOrderId;


        private String productId;


        private String productCode;


        private String gameId;


        private String groupId;


        private String orderId;


        private String orderItemId;


        private Integer completeStatus;


        private Integer timeOutStatus;


        private String retrieveUserId;


        private String disputeUserId;


        private LocalDateTime expectCompleteTime;


        private LocalDateTime completeTime;


        private Long gameConfigId;


        private Long remindPlanId;


        private Long remindSubPlanId;


        private String createUserId;


        private String updateUserId;


        private Integer workOrderType;


        /**
         * 页码，从1开始
         */
        private long pageIndex;
        /**
         * 每页数量
         */
        private long pageSize;

    }

}

