package com.pxb7.mall.workorder.domain.service;


import com.alibaba.cola.exception.Assert;
import com.alibaba.excel.util.BooleanUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pxb7.mall.trade.ass.client.dto.response.afc.ComplaintWORespDTO;
import com.pxb7.mall.workorder.client.enums.*;
import com.pxb7.mall.workorder.client.message.RemindPlanMessage;
import com.pxb7.mall.workorder.domain.mapping.RemindComplaintDomainMapping;
import com.pxb7.mall.workorder.domain.model.*;
import com.pxb7.mall.workorder.domain.remind.task.RemindMessageTask;
import com.pxb7.mall.workorder.infra.aop.ClusterRedisLock;
import com.pxb7.mall.workorder.infra.constant.CommonConstants;
import com.pxb7.mall.workorder.infra.constant.RedisKeyConstants;
import com.pxb7.mall.workorder.infra.model.ComplaintStatisticDataPO;
import com.pxb7.mall.workorder.infra.model.ComplaintStatisticSearchPO;
import com.pxb7.mall.workorder.infra.model.RemindComplaintReqPO;
import com.pxb7.mall.workorder.infra.repository.db.RemindComplaintRepository;
import com.pxb7.mall.workorder.infra.repository.db.entity.RemindComplaint;
import com.pxb7.mall.workorder.infra.repository.db.mapper.AdsPxb7ComplaintForewarningMapper;
import com.pxb7.mall.workorder.infra.repository.es.entity.RemindComplaintDoc;
import com.pxb7.mall.workorder.infra.repository.es.mapper.RemindComplaintDocEsRepository;
import com.pxb7.mall.workorder.infra.repository.es.mapper.RemindComplaintDocRepository;
import com.pxb7.mall.workorder.infra.repository.gateway.dubbo.ass.AfcWorkOrderGateway;
import com.pxb7.mall.workorder.infra.util.IdGenUtil;
import com.pxb7.mall.workorder.infra.util.RedissonUtils;
import com.pxb7.mall.workorder.infra.util.TimeUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchPage;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;


/**
 * 客诉工单预警记录domain服务
 *
 * <AUTHOR>
 * @since 2025-04-24 23:32:52
 */
@Slf4j
@Service
public class RemindComplaintDomainService extends BaseRemindDomainService {

    @Resource
    private RemindComplaintRepository remindComplaintRepository;

    @Resource
    private RemindMessageTask remindMessageTask;
    @Resource
    private RemindComplaintDocEsRepository remindComplaintDocEsRepository;
    @Resource
    private RemindComplaintDocRepository remindComplaintDocRepository;
    @Resource
    private AdsPxb7ComplaintForewarningMapper adsPxb7ComplaintForewarningMapper;
    @Resource
    private AfcWorkOrderGateway afcWorkOrderGateway;

    @Override
    public BizTypeEnum getBizType() {
        return BizTypeEnum.COMPLAINT;
    }

    public boolean updateByWorkOrderId(RemindComplaintReqBO.UpdateBO param) {
        RemindComplaintReqPO.UpdatePO updatePO = RemindComplaintDomainMapping.INSTANCE.remindComplaintBO2UpdatePO(param);
        return remindComplaintRepository.updateByWorkOrderId(updatePO);
    }

    /**
     * 生成客诉工单预警记录
     * @param complaintWORespDTO
     * @param remindPlanGameConfig
     * @return
     */
    public RemindComplaintReqBO.AddBO generateRemindComplaint(
            ComplaintWORespDTO complaintWORespDTO, RemindPlanGameConfigBO remindPlanGameConfig) {
        //查询是否已存在
        RemindComplaintRespBO.DetailBO detailBO = findByWorkOrderId(complaintWORespDTO.getWorkOrderId(), null);
        if (Objects.nonNull(detailBO)) {
            return null;
        }
        //预期完结时间
        TimeConfigBO expectCompleteTimeConfig = remindPlanGameConfig.getExpectCompleteTimeConfig();
        LocalDateTime expectCompleteTime = TimeUtils.addSpecificTime(
                expectCompleteTimeConfig.getHours(), expectCompleteTimeConfig.getMinutes(), 0);
        //im客服端倒计时开始时间
        TimeConfigBO imCountDownTimeConfig = remindPlanGameConfig.getImCountDownTimeConfig();
        LocalDateTime imCountDownTime = Objects.nonNull(imCountDownTimeConfig) ?
                TimeUtils.subtractSpecificTime(expectCompleteTime, imCountDownTimeConfig.getHours(), imCountDownTimeConfig.getMinutes(), 0)
                : null;
        //插入客诉工单预警记录
        RemindComplaintReqBO.AddBO addBO = new RemindComplaintReqBO.AddBO()
                .setRemindId("CP" + IdGenUtil.getShardingColumnId(complaintWORespDTO.getWorkOrderId()))
                .setWorkOrderId(complaintWORespDTO.getWorkOrderId())
                .setWorkOrderTitle(complaintWORespDTO.getComplaintTitle())
                .setComplaintContent(complaintWORespDTO.getComplaintContent())
                .setGroupId(complaintWORespDTO.getRoomId())
                .setCompleteStatus(CompleteStatusEnum.IN_PROCESS.getCode())
                .setTimeOutStatus(TimeoutStatusEnum.NONE.getCode())
                .setHandleUserId(complaintWORespDTO.getCurrentProcessorId())
                .setExpectCompleteTime(expectCompleteTime)
                .setImCountDownTime(imCountDownTime)
                .setGameConfigId(remindPlanGameConfig.getId())
                .setRemindPlanId(remindPlanGameConfig.getRemindPlanId())
                .setRemindSubPlanId(remindPlanGameConfig.getRemindSubPlanId())
                .setCreateUserId(CommonConstants.SYS_DEFAULT_USER)
                .setUpdateUserId(CommonConstants.SYS_DEFAULT_USER);

        RemindComplaintReqPO.AddPO addPO = RemindComplaintDomainMapping.INSTANCE.remindComplaintBO2AddPO(addBO);
        boolean result = remindComplaintRepository.insert(addPO);
        Assert.isTrue(result,
                BizErrorCodeEnum.REMIND_RECORD_SAVE_ERROR.getErrCode(), BizErrorCodeEnum.REMIND_RECORD_SAVE_ERROR.getErrDesc());
        return addBO;
    }

    /**
     * 处理提醒消息发送
     * @param remindPlanMessage
     */
    @Override
    public PlanExecuteStatusEnum handleRemindMessage(RemindPlanMessage remindPlanMessage) {
        //根据workOrderId查询商品工单预警记录
        RemindComplaintRespBO.DetailBO remindWorkOrder =
                findByWorkOrderId(remindPlanMessage.getBizId(), remindPlanMessage.getRemindId());
        if (Objects.isNull(remindWorkOrder)) {
            log.error("根据workOrderId查询客诉工单预警记录为空，workOrderId:{}", remindPlanMessage.getBizId());
            return PlanExecuteStatusEnum.FAILED;
        }
        //完结状态,1:未完结,2:已完结,3:终止
        Integer completeStatus = remindWorkOrder.getCompleteStatus();
        if (!Objects.equals(CompleteStatusEnum.IN_PROCESS.getCode(), completeStatus)) {
            return PlanExecuteStatusEnum.EXPIRED;
        }
        //查询客诉工单最新的信息
        ComplaintWORespDTO complaintWORespDTO = afcWorkOrderGateway.getComplaintWorkOrderInfo(remindPlanMessage.getBizId());
        if (Objects.isNull(complaintWORespDTO)) {
            log.error("Failed to query dispute work order info. workOrderId:{}", remindPlanMessage.getBizId());
            return PlanExecuteStatusEnum.FAILED;
        }
        //验证工单是否有效
        Integer isValid = complaintWORespDTO.getIsValid();
        if (!Objects.equals(isValid, 1)) {
            return PlanExecuteStatusEnum.INVALID;
        }
        //转接了当前处理人
        String newHandleUserId = complaintWORespDTO.getCurrentProcessorId();
        if (!Objects.equals(newHandleUserId, remindWorkOrder.getHandleUserId())) {
            changeHandleUser(remindWorkOrder, newHandleUserId);
            remindWorkOrder.setHandleUserId(newHandleUserId);
        }

        //处理提醒消息发送
        RemindComplaintBO remindRecord = new RemindComplaintBO();
        BeanUtils.copyProperties(remindWorkOrder, remindRecord);
        Boolean result = remindMessageTask.handleRemindMessage(remindPlanMessage, remindRecord);
        return Objects.equals(result, Boolean.TRUE)
                ? PlanExecuteStatusEnum.DONE : PlanExecuteStatusEnum.FAILED;
    }

    /**
     * 更改当前处理人
     * @param remindWorkOrder
     * @param newHandleUserId
     */
    private void changeHandleUser(RemindComplaintRespBO.DetailBO remindWorkOrder, String newHandleUserId) {
        //更新当前处理人
        RemindComplaintReqBO.UpdateBO updateBO = new RemindComplaintReqBO.UpdateBO();
        updateBO.setWorkOrderId(remindWorkOrder.getWorkOrderId());
        updateBO.setRemindId(remindWorkOrder.getRemindId());
        updateBO.setHandleUserId(newHandleUserId);
        updateByWorkOrderId(updateBO);
        //更改群组超时状态
        changeGroupTimeout(remindWorkOrder, newHandleUserId);
    }

    /**
     * 更改群组超时状态
     * @param remindWorkOrder
     * @param newHandleUserId
     */
    private void changeGroupTimeout(RemindComplaintRespBO.DetailBO remindWorkOrder, String newHandleUserId) {
        if (StringUtils.isBlank(remindWorkOrder.getGroupId())
                || StringUtils.isBlank(newHandleUserId)) {
            return;
        }
        //删除旧的群组超时状态
        String groupTimoutKey = String.format(RedisKeyConstants.TIMEOUT_GROUP_KEY,
                remindWorkOrder.getGroupId(), remindWorkOrder.getHandleUserId());
        Integer timeOutStatus = RedissonUtils.getCacheObject(groupTimoutKey);
        if (Objects.isNull(timeOutStatus)) {
            return;
        }
        RedissonUtils.deleteObject(groupTimoutKey);

        //设置新的群组超时状态
        String newGroupTimoutKey = String.format(RedisKeyConstants.TIMEOUT_GROUP_KEY,
                remindWorkOrder.getGroupId(), newHandleUserId);
        RedissonUtils.setObjectIfAbsent(newGroupTimoutKey, timeOutStatus, Duration.ofDays(180L));
    }

    /**
     * 根据workOrderId查询客诉工单预警记录
     * @param workOrderId
     * @param remindId
     * @return
     */
    public RemindComplaintRespBO.DetailBO findByWorkOrderId(String workOrderId, String remindId) {
        RemindComplaint entity = remindComplaintRepository.findByWorkOrderId(workOrderId, remindId);
        return RemindComplaintDomainMapping.INSTANCE.remindComplaintPO2DetailBO(entity);
    }

    /**
     * 更新客诉预警记录超时状态
     * @param remindPlanMessage
     */
    @Override
    public void updateWillTimeOutStatus(RemindPlanMessage remindPlanMessage) {
        RemindComplaintRespBO.DetailBO remindComplaint =
                findByWorkOrderId(remindPlanMessage.getBizId(), remindPlanMessage.getRemindId());
        if (!Objects.equals(CompleteStatusEnum.IN_PROCESS.getCode(), remindComplaint.getCompleteStatus())) {
            return;
        }
        //更新账号交付预警记录状态
        RemindComplaintReqBO.UpdateBO updateBO = new RemindComplaintReqBO.UpdateBO();
        updateBO.setWorkOrderId(remindPlanMessage.getBizId());
        updateBO.setRemindId(remindPlanMessage.getRemindId());
        updateBO.setTimeOutStatus(TimeoutStatusEnum.WILL_TIMEOUT.getCode());
        this.updateByWorkOrderId(updateBO);

        //通知客服端，把超时房间移到即将超时列表
        moveGroupToTimeoutList(
                Collections.singletonList(remindComplaint), TimeoutStatusEnum.WILL_TIMEOUT);
    }

    /**
     * 执行客诉工单超时状态更新任务
     */
    @Override
    public void executeUpdateTimeoutStatusTask(String databaseName) {
        log.info("执行客诉工单超时状态更新任务，任务开始，databaseName:{}", databaseName);
        //提前时间
        LocalDateTime nowDateTime = LocalDateTime.now();

        //分页查询符合条件的提醒计划记录
        //游标id
        long minId = 0;
        long pageSize = CommonConstants.QUERY_PAGE_SIZE;
        while (true) {
            RemindComplaintReqBO.PageBO param = new RemindComplaintReqBO.PageBO();
            param.setId(minId);
            param.setCompleteStatus(CompleteStatusEnum.IN_PROCESS.getCode());
            param.setTimeOutStatus(TimeoutStatusEnum.TIMEOUT.getCode());
            param.setExpectCompleteTime(nowDateTime);
            param.setPageIndex(1);
            param.setPageSize(pageSize);

            Page<RemindComplaintRespBO.DetailBO> page = pageDb(param);
            List<RemindComplaintRespBO.DetailBO> pageRecords = page.getRecords();
            if (CollectionUtils.isEmpty(pageRecords)) {
                break;
            }
            minId = pageRecords.get(pageRecords.size() - 1).getId();
            //更新状态为过期
            List<Long> ids = pageRecords.stream().map(RemindComplaintRespBO.DetailBO::getId).toList();
            batchUpdateTimeoutStatusByIds(
                    ids, TimeoutStatusEnum.TIMEOUT);

            //通知客服端，把超时房间移到超时tab
            moveGroupToTimeoutList(
                    pageRecords, TimeoutStatusEnum.TIMEOUT);

            log.info("执行客诉工单超时状态更新任务，databaseName:{}，page:{}，page size:{}", databaseName, page.getCurrent(), page.getSize());
        }
        log.info("执行客诉工单超时状态更新任务，任务结束，databaseName:{}", databaseName);
    }


    /**
     * 分页查询
     * @param param
     * @return
     */
    private Page<RemindComplaintRespBO.DetailBO> pageDb(RemindComplaintReqBO.PageBO param) {
        RemindComplaintReqPO.PagePO pagePO =
                RemindComplaintDomainMapping.INSTANCE.remindComplaintBO2PagePO(param);
        Page<RemindComplaint> page = remindComplaintRepository.page(pagePO);
        return RemindComplaintDomainMapping.INSTANCE.remindComplaintPO2PageBO(page);
    }

    /**
     * 批量更新账号交付超时状态
     * @param ids
     * @param status
     * @return
     */
    private Boolean batchUpdateTimeoutStatusByIds(List<Long> ids, TimeoutStatusEnum status) {
        if (CollectionUtils.isEmpty(ids)) {
            return Boolean.FALSE;
        }
        return remindComplaintRepository.batchUpdateTimeoutStatusByIds(ids, status.getCode());
    }

    /**
     * 通知客服端，把超时房间移到超时tab
     * @param remindComplaints
     * @param status
     */
    private void moveGroupToTimeoutList(
            List<RemindComplaintRespBO.DetailBO> remindComplaints, TimeoutStatusEnum status) {
        List<ImGroupCmdMsgBO> imGroupCmdMsgList = new ArrayList<>();
        for (RemindComplaintRespBO.DetailBO remindComplaint : remindComplaints) {
            ImGroupCmdMsgBO imGroupCmdMsgBO = new ImGroupCmdMsgBO()
                    .setGroupId(remindComplaint.getGroupId())
                    .setTargetUserId(remindComplaint.getHandleUserId())
                    .setStatus(status);
            imGroupCmdMsgList.add(imGroupCmdMsgBO);
        }
        moveGroupToTimeoutList(imGroupCmdMsgList);
    }


    /**
     * 通过房间查询客诉预警记录
     * @param groupId
     * @return
     */
    @Override
    public RemindComplaintBO searchByGroupId(String groupId, String customerCareId) {
        RemindComplaintDoc complaintDoc = remindComplaintDocEsRepository.searchByGroupId(groupId, customerCareId);
        return RemindComplaintDomainMapping.INSTANCE.remindComplaintDoc2BO(complaintDoc);
    }

    public Page<RemindComplaintBO> pageEs(RemindComplaintReqBO.PageBO param) {
        RemindComplaintReqPO.PagePO pagePO = RemindComplaintDomainMapping.INSTANCE.remindComplaintBO2PagePO(param);
        SearchPage<RemindComplaintDoc> searchHits = remindComplaintDocEsRepository.pageQueryComplaint(pagePO);
        if (searchHits == null) {
            return new Page<>(param.getPageIndex(), param.getPageSize());
        }
        if (CollectionUtils.isEmpty(searchHits.getContent())) {
            return new Page<>(searchHits.getNumber(), searchHits.getSize(), searchHits.getTotalElements());
        }
        List<SearchHit<RemindComplaintDoc>> content = searchHits.getContent();
        List<RemindComplaintDoc> list = content.stream().map(SearchHit::getContent).toList();
        List<RemindComplaintBO> remindComplaintBOS = RemindComplaintDomainMapping.INSTANCE.remindComplaintDoc2ListBO(list);
        Page<RemindComplaintBO> remindComplaintBOPage = new Page<>(searchHits.getNumber(), searchHits.getSize(), searchHits.getTotalElements());
        remindComplaintBOPage.setRecords(remindComplaintBOS);
        return remindComplaintBOPage;
    }

    public void deleteRemindComplaintEs(List<String> remindIds) {
        if (CollectionUtils.isEmpty(remindIds)) {
            return;
        }
        remindComplaintDocRepository.deleteAllById(remindIds);
    }

    @ClusterRedisLock(prefix = "complaint_data_sync", value = "#remindId")
    public void syncRemindComplaintToEs(String remindId) {
        RemindComplaint remindComplaint = remindComplaintRepository.getByRemindId(remindId);
        if (Objects.isNull(remindComplaint)
                || BooleanUtils.isTrue(remindComplaint.getDeleted())) {
            remindComplaintDocRepository.deleteById(remindId);
        } else {
            RemindComplaintDoc remindComplaintDoc = RemindComplaintDomainMapping.INSTANCE.remindComplaintPO2Doc(remindComplaint);
            if (Objects.isNull(remindComplaintDoc)) {
                return;
            }
            // es中补全 客诉等级 和 投诉渠道
            ComplaintWORespDTO complaintWORespDTO = afcWorkOrderGateway.getComplaintWorkOrderInfo(remindComplaint.getWorkOrderId());
            if (!Objects.isNull(complaintWORespDTO)) {
                remindComplaintDoc.setComplaintLevel(complaintWORespDTO.getComplaintLevel());
                remindComplaintDoc.setChannel(complaintWORespDTO.getComplaintChannel());
            }
            remindComplaintDocRepository.save(remindComplaintDoc);
        }
    }

    /**
     * 客诉工单统计
     * 
     * @param searchBO 参数
     * @return List<ComplaintStatisticDataBO>
     */
    public List<ComplaintStatisticDataBO> getStatisticList(ComplaintStatisticDataSearchBO searchBO) {
        ComplaintStatisticSearchPO param =
            RemindComplaintDomainMapping.INSTANCE.remindAfterSaleStatisticBO2SearchPO(searchBO);
        List<ComplaintStatisticDataPO> complaintStatisticDataPOS =
            adsPxb7ComplaintForewarningMapper.countBySearchParam(param);
        return RemindComplaintDomainMapping.INSTANCE.complaintStatisticDataPO2BO(complaintStatisticDataPOS);
    }

    public LocalDateTime getLastDataInsertTime() {
        return adsPxb7ComplaintForewarningMapper.getLastDataInsertTime();
    }
}

