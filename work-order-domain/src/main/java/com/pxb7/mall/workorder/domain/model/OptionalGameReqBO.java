package com.pxb7.mall.workorder.domain.model;

import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.List;

@Getter
@Setter
@Accessors(chain = true)
@ToString
public class OptionalGameReqBO {

    /**
     * 预警计划Id-新增时不传/编辑必传
     */
    private Long remindPlanId;

    /**
     * 服务类型: 1：账号交付 2:商品工单
     */
    @NotNull
    private Integer serviceType;

    /**
     * 工单类型: 1：找回 2:纠纷
     */
    @NotNull
    private Integer workOrderType;

    /**
     * 业务类型，1:代售，2:中介，服务类型为账号交付有值
     */
    private List<Integer> businessTypes;


    /**
     *  工单状态：1:待接单，2:已接单，3:待跟进，服务类型为商品工单有值
     */
    private List<Integer> workOrderStatuses;

    /**
     * 模糊查询-游戏名称
     */
    private String gameName;

    /**
     * 订单来源(会员类型)：1 散户工单 2 号商工单 3 3A工单，服务类型为商品工单有值
     */
    private List<Integer> memberships;

    /**
     * 订单来源(会员类型)：1 散户工单 2 号商工单 3 3A工单，服务类型为商品工单有值
     */
    private Integer membership;

}
