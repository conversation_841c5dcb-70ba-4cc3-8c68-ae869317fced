package com.pxb7.mall.workorder.domain.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Sets;
import com.pxb7.mall.user.dto.response.user.UserShortInfoDTO;
import com.pxb7.mall.workorder.client.enums.BargainTicketReadFlagEnum;
import com.pxb7.mall.workorder.client.enums.BargainTicketTradeStatusFlagEnum;
import com.pxb7.mall.workorder.domain.mapping.BargainTicketDomainMapping;
import com.pxb7.mall.workorder.domain.model.BargainTicketBO;
import com.pxb7.mall.workorder.domain.model.BargainTicketCacheBO;
import com.pxb7.mall.workorder.domain.model.BargainTicketReqBO;
import com.pxb7.mall.workorder.infra.model.BargainTicketReqPO;
import com.pxb7.mall.workorder.infra.repository.db.BargainAcceptanceCustomerRepository;
import com.pxb7.mall.workorder.infra.repository.db.entity.BargainAcceptanceCustomer;
import com.pxb7.mall.workorder.infra.repository.es.entity.BargainTicketDoc;
import com.pxb7.mall.workorder.infra.repository.es.mapper.BargainTicketDocEsRepository;
import com.pxb7.mall.workorder.infra.repository.gateway.dubbo.order.OrderItemGateway;
import com.pxb7.mall.workorder.infra.repository.gateway.dubbo.user.UserGateway;
import com.pxb7.mall.workorder.infra.util.RedissonUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version : BargainTicketDomainService.java, v 0.1 2025年04月16日 17:14 yang.xuexi Exp $
 */
@Service
@Slf4j
public class BargainTicketDomainService {


    @Resource
    private BargainTicketDocEsRepository bargainTicketDocEsRepository;

    private static final String BUYER_TICKER_KEY = "bargainTicket:user:{}:{}";

    @Resource
    protected UserGateway userGateway;

    @Resource
    protected OrderItemGateway orderItemGateway;

    @Resource
    protected BargainAcceptanceCustomerRepository bargainAcceptanceCustomerRepository;


    public String getCacheReceived(String userId) {
        String key = StrUtil.format(BUYER_TICKER_KEY, userId, LocalDateTime.now().format(DateTimeFormatter.BASIC_ISO_DATE));
        String cacheObject = RedissonUtils.getCacheObject(key);
        if (StrUtil.isNotBlank(cacheObject)) {
            return cacheObject;
        }

        BargainAcceptanceCustomer byBuyerId = bargainAcceptanceCustomerRepository.getByBuyerId(userId);
        if (Objects.isNull(byBuyerId)) {
            return null;
        }

        RedissonUtils.setCacheObject(key, byBuyerId.getReceiveId(), Duration.ofDays(NumberUtils.INTEGER_ONE));
        return byBuyerId.getReceiveId();

    }


    public void addUserCache(String userId, String receiveId) {
        String key = StrUtil.format(BUYER_TICKER_KEY, userId, LocalDateTime.now().format(DateTimeFormatter.BASIC_ISO_DATE));
        //缓存一天
        RedissonUtils.setCacheObject(key, receiveId, Duration.ofDays(NumberUtils.INTEGER_ONE));
    }

    /**
     * 还价工单查询
     *
     * @param param
     * @return
     */
    public Page<BargainTicketBO> pageEs(BargainTicketReqBO.PageBO param) {
        BargainTicketReqPO.PagePO po = BargainTicketDomainMapping.INSTANCE.convertBo2Po(param);
        Page<BargainTicketDoc> page = bargainTicketDocEsRepository.page(po);
        if (Objects.isNull(page)) {
            return new Page<>(param.getPageIndex(), param.getPageSize());
        }

        List<BargainTicketDoc> list = page.getRecords();
        Page<BargainTicketBO> remindWorkOrderBOPage = new Page<>(page.getPages(), page.getSize(), page.getTotal());
        remindWorkOrderBOPage.setRecords(BargainTicketDomainMapping.INSTANCE.convertDoc2BoList(list));
        return remindWorkOrderBOPage;
    }


    public BargainTicketBO findByReceiveId(String receiveId) {
        BargainTicketDoc doc = bargainTicketDocEsRepository.findByReceiveId(receiveId);
        if (Objects.isNull(doc)) {
            return null;
        }
        return BargainTicketDomainMapping.INSTANCE.convertDoc2Bo(doc);
    }


    public Optional<UserShortInfoDTO> getUserInfo(String userId) {
        if (StrUtil.isBlank(userId)) {
            return Optional.empty();
        }
        List<UserShortInfoDTO> userShortInfoDTOS = userGateway.queryUserInfoList(Sets.newHashSet(userId));
        if (CollUtil.isNotEmpty(userShortInfoDTOS)) {
            return Optional.ofNullable(userShortInfoDTOS.get(0));
        }
        return Optional.empty();
    }


    public boolean checkUserIfDealToday(String userId) {
        if (StrUtil.isBlank(userId)) {
            return false;
        }
        Map<String, Boolean> stringBooleanMap = orderItemGateway.batchQueryUserDealByToday(Sets.newHashSet(userId));
        if (MapUtil.isEmpty(stringBooleanMap)) {
            return false;
        }
        return stringBooleanMap.getOrDefault(userId, false);
    }
}
