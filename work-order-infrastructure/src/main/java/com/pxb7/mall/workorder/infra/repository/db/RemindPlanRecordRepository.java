package com.pxb7.mall.workorder.infra.repository.db;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.pxb7.mall.workorder.infra.model.RemindPlanRecordReqPO;
import com.pxb7.mall.workorder.infra.repository.db.entity.RemindPlanRecord;

import java.util.List;

/**
 * 预警执行计划记录(RemindPlanRecord)表服务接口
 *
 * <AUTHOR>
 * @since 2025-04-12 14:13:00
 */
public interface RemindPlanRecordRepository extends IService<RemindPlanRecord> {

    boolean insertBatch(List<RemindPlanRecordReqPO.AddPO> param);

    boolean updateStatusByBizIdAndType(RemindPlanRecordReqPO.UpdatePO param);

    Page<RemindPlanRecord> page(RemindPlanRecordReqPO.PagePO param);

    boolean batchUpdateStatusByIds(List<Long> planRecordIds, Integer status);

    boolean batchUpdateStatusByRemindIds(List<String> remindIds, Integer status, Long addSeconds);

    boolean deleteByBizId(String bizId);
}

