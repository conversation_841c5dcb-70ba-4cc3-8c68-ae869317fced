package com.pxb7.mall.workorder.app.mapping;

import com.pxb7.mall.workorder.app.model.TimeoutInfoRespDTO;
import com.pxb7.mall.workorder.domain.model.TimeoutInfoRespBO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2025/4/16 19:39
 */

@Mapper
public interface TimeoutInfoDomainMapping {

    TimeoutInfoDomainMapping INSTANCE = Mappers.getMapper(TimeoutInfoDomainMapping.class);


    TimeoutInfoRespDTO timeoutInfoBO2DTO(TimeoutInfoRespBO source);


}
