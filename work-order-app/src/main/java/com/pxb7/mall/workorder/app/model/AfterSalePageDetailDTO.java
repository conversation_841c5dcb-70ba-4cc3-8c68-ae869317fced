package com.pxb7.mall.workorder.app.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Getter
@Setter
@Accessors(chain = true)
@ToString
public class AfterSalePageDetailDTO {


    /**
     * 预警Id
     */
    private String remindId;

    /**
     * 预警子计划ID
     */
    private Long remindSubPlanId;


    /**
     * 游戏Id
     */
    private String gameId;

    /**
     * 游戏名称
     */
    private String gameName;

    /**
     * 商品编码
     */
    private String productCode;

    /**
     * 子订单id
     */
    private String orderItemId;

    /**
     * 工单Id
     */
    private String workOrderId;

    /**
     * 工单编号
     */
    private String workOrderNo;

    /**
     * 完结状态,1:未完结,2:已完结,3:交付终止
     */
    private Integer completeStatus;

    /**
     * 找回处理人Id
     */
    private String retrieveUserId;

    /**
     * 找回处理人
     */
    private String retrieveUserName;

    /**
     * 纠纷处理人Id
     */
    private String disputeUserId;

    /**
     * 纠纷处理人
     */
    private String disputeUserName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 预期完结时间：通过当前时间减去expectCompleteTime可以算出
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expectCompleteTime;

    /**
     *  完结时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime completeTime;

    /**
     * 工单持续时间提示："xx天xx小时xx分"
     */
    private String durationTip;

    /**
     * 倒计时提示："xx天xx小时xx分钟后超时" OR "已超时xx天xx小时xx分钟"
     */
    private String countDownTip;


}
