package com.pxb7.mall.workorder.app.util;

import com.alibaba.fastjson.JSON;
import com.pxb7.mall.auth.dto.AdminUserDTO;
import com.pxb7.mall.workorder.infra.util.IdGenUtil;
import com.pxb7.mall.workorder.app.mapping.RemindMethodConfigDomainMapping;
import com.pxb7.mall.workorder.app.mapping.RemindPlanRuleAppMapping;
import com.pxb7.mall.workorder.app.mapping.TimeConfigDomainMapping;
import com.pxb7.mall.workorder.app.model.PlanRuleDTO;
import com.pxb7.mall.workorder.app.model.TimeConfigDTO;
import com.pxb7.mall.workorder.domain.model.RemindPlanBO;
import com.pxb7.mall.workorder.domain.model.RemindPlanRuleBO;
import com.pxb7.mall.workorder.domain.model.RemindPlanRuleRefreshBO;
import com.pxb7.mall.workorder.domain.model.TimeConfigBO;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
public class RemindPlanRuleBuildUtil {

    public static RemindPlanRuleRefreshBO buildRemindPlanRuleRefreshBO( List<PlanRuleDTO> planRuleDTOS,
        RemindPlanBO remindPlanBO, AdminUserDTO adminUser) {

        List<RemindPlanRuleBO> remindPlanRules = Optional.of(remindPlanBO).map(RemindPlanBO::getRemindPlanRules).orElse(new ArrayList<>());
        List<Long> existRuelIds = remindPlanRules.stream().map(RemindPlanRuleBO::getId).toList();
        List<Long> inputRuleIds = planRuleDTOS.stream().map(PlanRuleDTO::getId).filter(Objects::nonNull).distinct().toList();

        List<PlanRuleDTO> needAddPlanRuleDTOS =
            planRuleDTOS.stream().filter(a -> !existRuelIds.contains(a.getId())).toList();

        List<RemindPlanRuleBO> needAddRemindPlanRuleBOS = new ArrayList<>();
        for (PlanRuleDTO planRule : needAddPlanRuleDTOS) {
            RemindPlanRuleBO remindPlanRuleBO = RemindPlanRuleAppMapping.INSTANCE.remindPlanRuleDTO2BO(planRule);
            remindPlanRuleBO.setRemindPlanId(remindPlanBO.getId());
            remindPlanRuleBO.setId(IdGenUtil.getId());
            remindPlanRuleBO.setCreateUserId(adminUser.getUserId());
            remindPlanRuleBO.setUpdateUserId(adminUser.getUserId());
            needAddRemindPlanRuleBOS.add(remindPlanRuleBO);
        }



        List<RemindPlanRuleBO> needDeletePlanRuleBOS = remindPlanRules.stream().filter(a -> !inputRuleIds.contains(a.getId()))
            .map(a->a.setUpdateUserId(adminUser.getUserId()))
            .collect(Collectors.toList());

        Map<Long, PlanRuleDTO> inputPlanRuleMap = planRuleDTOS.stream().filter(a->a.getId()!=null)
            .collect(Collectors.toMap(PlanRuleDTO::getId, Function.identity(), (l, r) -> l));


        List<RemindPlanRuleBO> waitUpdatePlanRuleBOS =
            remindPlanRules.stream().filter(a -> inputRuleIds.contains(a.getId())).toList();
        List<RemindPlanRuleBO> needUpdatePlanRuleBOS = new ArrayList<>();
        for (RemindPlanRuleBO needUpdatePlanRuleBO : waitUpdatePlanRuleBOS) {
            PlanRuleDTO planRuleDTO = inputPlanRuleMap.get(needUpdatePlanRuleBO.getId());
            if (null==planRuleDTO){
                continue;
            }
            if (!hasChanged(needUpdatePlanRuleBO, planRuleDTO)) {
                log.info("remindPlanRule no change:{}", needUpdatePlanRuleBO.getId());
                continue;
            }

            needUpdatePlanRuleBO.setNodeNumber(planRuleDTO.getNodeNumber());
            needUpdatePlanRuleBO.setRemindTimeConfig(TimeConfigDomainMapping.INSTANCE.timeConfigDTO2BO(planRuleDTO.getRemindTimeConfig()));
            needUpdatePlanRuleBO.setRemindMethodConfig(RemindMethodConfigDomainMapping.INSTANCE.remindMethodConfigDTO2ListBO(planRuleDTO.getRemindMethodConfig()));
            needUpdatePlanRuleBO.setUpdateUserId(adminUser.getUserId());
            needUpdatePlanRuleBOS.add(needUpdatePlanRuleBO);
        }

        return new RemindPlanRuleRefreshBO().setNeedAddPlanRules(needAddRemindPlanRuleBOS).setNeedDeletePlanRules(needDeletePlanRuleBOS)
            .setNeedUpdatePlanRules(needUpdatePlanRuleBOS);
    }

    private static boolean hasTimeConfigChanged(TimeConfigBO original, TimeConfigDTO modified) {
        if (original == null && modified == null) {
            return false;
        }
        if (original == null || modified == null) {
            return true;
        }

        return !Objects.equals(original.getHours(), modified.getHours()) ||! Objects.equals(original.getMinutes(),
            modified.getMinutes());
    }

    public static boolean hasChanged(RemindPlanRuleBO original, PlanRuleDTO modified) {
        if (original == null && modified == null) {
            return false;
        }
        if (original == null || modified == null) {
            return true;
        }
        return !Objects.equals(original.getId(), modified.getId()) ||
            !Objects.equals(original.getNodeNumber(), modified.getNodeNumber()) ||
            hasTimeConfigChanged(original.getRemindTimeConfig(), modified.getRemindTimeConfig()) ||
            !Objects.equals(JSON.toJSONString(original.getRemindMethodConfig()), JSON.toJSONString(modified.getRemindMethodConfig())) ;
    }
}
