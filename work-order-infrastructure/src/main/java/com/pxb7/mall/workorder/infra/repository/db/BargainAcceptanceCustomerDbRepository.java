package com.pxb7.mall.workorder.infra.repository.db;


import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.base.Preconditions;
import com.pxb7.mall.workorder.infra.repository.db.entity.BargainAcceptanceCustomer;
import com.pxb7.mall.workorder.infra.repository.db.entity.RemindDeliveryProduct;
import com.pxb7.mall.workorder.infra.repository.db.mapper.BargainAcceptanceCustomerMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * 客服接单信息表(BargainAcceptanceCustomer)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-04-18 17:22:02
 */
@Slf4j
@Repository
public class BargainAcceptanceCustomerDbRepository extends ServiceImpl<BargainAcceptanceCustomerMapper, BargainAcceptanceCustomer> implements BargainAcceptanceCustomerRepository {
    @Override
    public BargainAcceptanceCustomer getByReceiveId(String receiveId) {
        Preconditions.checkArgument(StrUtil.isNotBlank(receiveId), "工单id不能为空");
        return this.lambdaQuery().eq(BargainAcceptanceCustomer::getReceiveId, receiveId).one();
    }

    @Override
    public BargainAcceptanceCustomer getByBuyerId(String buyerId) {
        if (StrUtil.isBlank(buyerId)) {
            return null;
        }
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime startOfDay = now.with(LocalTime.MIN);
        LocalDateTime endOfDay = now.with(LocalTime.MAX);
        LambdaQueryWrapper<BargainAcceptanceCustomer> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BargainAcceptanceCustomer::getBuyerUserId, buyerId);
        queryWrapper.between(BargainAcceptanceCustomer::getCreateTime, startOfDay, endOfDay);
        return this.getOne(queryWrapper);
    }
}
