package com.pxb7.mall.workorder.infra.constant;

/**
 * mq 常量
 *
 * <AUTHOR>
 */
public class RMQConstant {

    public static final String ALL_TAG = "*";


    /**
     * 订单状态变更topic
     */
    public static final String ORDER_STATUS_CHANGE_TOPIC = "order_status_change_topic";
    /**
     * 订单状态变更group
     */
    public static final String ORDER_STATUS_CHANGE_GROUP = "order_status_work_order_group";



    /**
     * 交付状态变更topic
     */
    public static final String DELIVER_STATUS_CHANGE_TOPIC = "delivery_status_change";
    /**
     * 交付状态变更group
     */
    public static final String DELIVER_STATUS_CHANGE_GROUP = "deliver_status_work_order_group";


    /**
     * 群组更换交付客服
     */
    public static final String UPDATE_ORDER_BIND_DELIVERY_CUSTOMER_CARE_TOPIC = "update_order_bind_delivery_customer_care_topic";
    /**
     * 群组更换交付客服group
     */
    public static final String UPDATE_ORDER_BIND_DELIVERY_CUSTOMER_CARE_GROUP = "update_order_bind_delivery_customer_care_work_order_group";

    /**
     * 商品工单状态变更topic
     */
    public static final String PRODUCT_EVENT_TOPIC = "product_event_topic";
    /**
     * 商品工单状态变更tag
     */
    public static final String PRODUCT_WORK_ORDER_TAG = "publish||workOrder";
    /**
     * 商品工单状态变更group
     */
    public static final String PRODUCT_EVENT_GROUP = "product_event_work_order_group";



    /**
     * 预警服务topic
     */
    public static final String REMIND_EVENT_TOPIC = "remind_event_topic";
    /**
     * 预警执行计划
     */
    public static final String REMIND_MESSAGE_TAG = "remind_message_tag";
    /**
     * 预警服务group
     */
    public static final String WORK_ORDER_SERVICE_GROUP = "work_order_service_group";



    /**
     * AFC 工单状态变更topic
     */
    public static final String AFC_WORK_ORDER_TOPIC = "T_afc_work_order_status_change";
    /**
     * 找回 纠纷 客诉
     */
    public static final String AFC_WORK_ORDER_TAG = "retrieve||dispute||complaint";
    /**
     * AFC 工单状态变更group
     */
    public static final String AFC_WORK_ORDER_GROUP = "afc_work_order_group";


    /**
     * 还价客服接单信息变更
     */
    public static final String BARGAIN_ACCEPTANCE_CUSTOMER_CHANGE = "promotion_acceptance_customer_change";


    /**
     * 还价服务分组
     */
    public static final String BARGAIN_TICKET_GROUP = "promotion-bargain_ticket_group";


    /**
     * 还价工单交易状态变更
     */
    public static final String BARGAIN_TICKET_ORDER_GROUP = "promotion-bargain_ticket_order_group";


    /**
     * 还价工单详情已读topic
     */
    public static final String ACCEPTANCE_CUSTOMER_READ_STATUS_CHANGE = "promotion-acceptance_read_status_change";

    /**
     * 订单绑定交付房间变更topic
     */
    public static final String ORDER_CENTER_TOPIC = "order_center_topic";
    /**
     * 订单绑定交付房间变更tag
     */
    public static final String ORDER_DELIVERY_ROOM_CHANGE_TAG = "order_delivery_room_change_tag";
    /**
     * 订单绑定交付房间变更group
     */
    public static final String ORDER_DELIVERY_ROOM_CHANGE_GROUP = "order_delivery_room_work_order_group";


}
