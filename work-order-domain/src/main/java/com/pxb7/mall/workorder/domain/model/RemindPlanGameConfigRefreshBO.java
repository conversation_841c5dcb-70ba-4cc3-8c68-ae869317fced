package com.pxb7.mall.workorder.domain.model;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.List;


@Getter
@Setter
@Accessors(chain = true)
@ToString
public class RemindPlanGameConfigRefreshBO {

    private List<RemindPlanGameConfigBO> needAddPlanGameConfigs;

    private List<RemindPlanGameConfigBO> needUpdatePlanGameConfigs;

    private List<RemindPlanGameConfigBO> needDeletePlanGameConfigs;
}
