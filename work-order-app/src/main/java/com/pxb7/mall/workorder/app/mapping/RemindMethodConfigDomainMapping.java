package com.pxb7.mall.workorder.app.mapping;

import com.pxb7.mall.workorder.app.model.RemindMethodConfigDTO;
import com.pxb7.mall.workorder.domain.model.RemindMethodConfigBO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface RemindMethodConfigDomainMapping {

    RemindMethodConfigDomainMapping INSTANCE = Mappers.getMapper(RemindMethodConfigDomainMapping.class);


    RemindMethodConfigBO remindMethodConfigDTO2BO(RemindMethodConfigDTO source);


    List<RemindMethodConfigBO> remindMethodConfigDTO2ListBO(List<RemindMethodConfigDTO> source);

}
