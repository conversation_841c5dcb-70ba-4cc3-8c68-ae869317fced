package com.pxb7.mall.workorder.app.mapping;

import com.pxb7.mall.workorder.app.model.RemindPlanRecordReqDTO;
import com.pxb7.mall.workorder.app.model.RemindPlanRecordRespDTO;
import com.pxb7.mall.workorder.infra.repository.db.entity.RemindPlanRecord;
import com.pxb7.mall.workorder.domain.model.RemindPlanRecordReqBO;
import com.pxb7.mall.workorder.domain.model.RemindPlanRecordRespBO;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;


@Mapper
public interface RemindPlanRecordAppMapping {

    RemindPlanRecordAppMapping INSTANCE = Mappers.getMapper(RemindPlanRecordAppMapping.class);


    RemindPlanRecordReqBO.AddBO remindPlanRecordDTO2AddBO(RemindPlanRecordReqDTO.AddDTO source);

    RemindPlanRecordReqBO.UpdateBO remindPlanRecordDTO2UpdateBO(RemindPlanRecordReqDTO.UpdateDTO source);

    RemindPlanRecordReqBO.DelBO remindPlanRecordDTO2DelBO(RemindPlanRecordReqDTO.DelDTO source);

    RemindPlanRecordReqBO.SearchBO remindPlanRecordDTO2SearchBO(RemindPlanRecordReqDTO.SearchDTO source);

    RemindPlanRecordReqBO.PageBO remindPlanRecordDTO2PageBO(RemindPlanRecordReqDTO.PageDTO source);

    RemindPlanRecordRespDTO.DetailDTO remindPlanRecordBO2DetailDTO(RemindPlanRecordRespBO.DetailBO source);

    List<RemindPlanRecordRespDTO.DetailDTO> remindPlanRecordBO2ListDTO(List<RemindPlanRecordRespBO.DetailBO> source);

    Page<RemindPlanRecordRespDTO.DetailDTO> remindPlanRecordBO2PageDTO(Page<RemindPlanRecordRespBO.DetailBO> source);

}


