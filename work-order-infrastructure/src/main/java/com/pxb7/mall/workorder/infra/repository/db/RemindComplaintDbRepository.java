package com.pxb7.mall.workorder.infra.repository.db;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pxb7.mall.components.env.profile.PxProfileHelper;
import com.pxb7.mall.workorder.infra.mapping.ComplaintMapping;
import com.pxb7.mall.workorder.infra.model.RemindComplaintReqPO;
import com.pxb7.mall.workorder.infra.repository.db.entity.RemindComplaint;
import com.pxb7.mall.workorder.infra.repository.db.mapper.RemindComplaintMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Objects;

/**
 * 客诉工单预警记录(RemindComplaint)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-04-24 23:31:56
 */
@Slf4j
@Repository
public class RemindComplaintDbRepository extends ServiceImpl<RemindComplaintMapper, RemindComplaint> implements RemindComplaintRepository {

    @Override
    public boolean insert(RemindComplaintReqPO.AddPO param) {
        RemindComplaint entity = ComplaintMapping.INSTANCE.addDto2PO(param);
        return this.save(entity);
    }

    @Override
    public boolean updateByWorkOrderId(RemindComplaintReqPO.UpdatePO param) {
        LambdaUpdateWrapper<RemindComplaint> updateWrapper = new LambdaUpdateWrapper<>();
        //where
        updateWrapper.eq(RemindComplaint::getWorkOrderId, param.getWorkOrderId());
        if (StringUtils.isNotBlank(param.getRemindId())) {
            updateWrapper.eq(RemindComplaint::getRemindId, param.getRemindId());
        }
        //set
        if (Objects.nonNull(param.getCompleteStatus())) {
            updateWrapper.set(RemindComplaint::getCompleteStatus, param.getCompleteStatus());
        }
        if (Objects.nonNull(param.getTimeOutStatus())) {
            updateWrapper.set(RemindComplaint::getTimeOutStatus, param.getTimeOutStatus());
        }
        if (Objects.nonNull(param.getCompleteTime())) {
            updateWrapper.set(RemindComplaint::getCompleteTime, param.getCompleteTime());
        }
        if (Objects.nonNull(param.getHandleUserId())) {
            updateWrapper.set(RemindComplaint::getHandleUserId, param.getHandleUserId());
        }
        return this.update(updateWrapper);
    }

    @Override
    public RemindComplaint findByWorkOrderId(String workOrderId, String remindId) {
        LambdaQueryWrapper<RemindComplaint> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RemindComplaint::getWorkOrderId, workOrderId);
        if (StringUtils.isNotBlank(remindId)) {
            queryWrapper.eq(RemindComplaint::getRemindId, remindId);
        }
        return this.getOne(queryWrapper);
    }

    @Override
    public Page<RemindComplaint> page(RemindComplaintReqPO.PagePO param) {
        LambdaQueryWrapper<RemindComplaint> queryWrapper = new LambdaQueryWrapper<>();
        // 返回分页查询结果
        queryWrapper.eq(RemindComplaint::getEnvProfile, PxProfileHelper.getProfile().getProfileValue());
        queryWrapper.gt(RemindComplaint::getId, param.getId());
        queryWrapper.lt(RemindComplaint::getExpectCompleteTime, param.getExpectCompleteTime());
        queryWrapper.eq(RemindComplaint::getCompleteStatus, param.getCompleteStatus());
        queryWrapper.ne(RemindComplaint::getTimeOutStatus, param.getTimeOutStatus());
        queryWrapper.orderByAsc(RemindComplaint::getId);
        return this.page(new Page<>(param.getPageIndex(), param.getPageSize()), queryWrapper);
    }

    /**
     * 批量更新状态
     * @param ids
     * @param status
     * @return
     */
    @Override
    public boolean batchUpdateTimeoutStatusByIds(List<Long> ids, Integer status) {
        return this.getBaseMapper().batchUpdateTimeoutStatusByIds(ids, status) > 0;
    }

    @Override
    public RemindComplaint getByRemindId(String remindId) {
        if (Objects.isNull(remindId)) {
            return null;
        }
        return this.lambdaQuery().eq(RemindComplaint::getRemindId, remindId).one();
    }

}
