package com.pxb7.mall.workorder.domain.mapping;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pxb7.mall.workorder.domain.model.*;
import com.pxb7.mall.workorder.infra.model.RemindPlanRuleReqPO;
import com.pxb7.mall.workorder.infra.repository.db.entity.RemindPlanRule;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(
    imports = {com.alibaba.fastjson2.JSON.class, TimeConfigBO.class,
        RemindMethodConfigBO.class})
public interface RemindPlanRuleDomainMapping {

    RemindPlanRuleDomainMapping INSTANCE = Mappers.getMapper(RemindPlanRuleDomainMapping.class);

    @Mapping(target = "remindTimeConfig", expression = "java(JSON.toJSONString(source.getRemindTimeConfig()))")
    @Mapping(target = "remindMethodConfig", expression = "java(JSON.toJSONString(source.getRemindMethodConfig()))")
    RemindPlanRuleReqPO.AddPO remindPlanRuleBO2AddPO(RemindPlanRuleBO source);

    @Mapping(target = "remindTimeConfig", expression = "java(JSON.toJSONString(source.getRemindTimeConfig()))")
    @Mapping(target = "remindMethodConfig", expression = "java(JSON.toJSONString(source.getRemindMethodConfig()))")
    RemindPlanRuleReqPO.UpdatePO remindPlanRuleBO2UpdatePO(RemindPlanRuleBO source);

    RemindPlanRuleReqPO.DelPO remindPlanRuleBO2DelPO(RemindPlanRuleReqBO.DelBO source);

    RemindPlanRuleReqPO.SearchPO remindPlanRuleBO2SearchPO(RemindPlanRuleReqBO.SearchBO source);

    RemindPlanRuleReqPO.PagePO remindPlanRuleBO2PagePO(RemindPlanRuleReqBO.PageBO source);

    RemindPlanRuleRespBO.DetailBO remindPlanRulePO2DetailBO(RemindPlanRule source);

    Page<RemindPlanRuleRespBO.DetailBO> remindPlanRulePO2PageBO(Page<RemindPlanRule> source);

    @Mapping(target = "remindTimeConfig", expression = "java(JSON.parseObject(source.getRemindTimeConfig(), TimeConfigBO.class))")
    @Mapping(target = "remindMethodConfig", expression = "java(JSON.parseArray(source.getRemindMethodConfig(), RemindMethodConfigBO.class))")
    RemindPlanRuleBO remindPlanRulePO2BO(RemindPlanRule source);

    @Mapping(target = "remindTimeConfig", expression = "java(JSON.parseObject(source.getRemindTimeConfig(), TimeConfigBO.class))")
    @Mapping(target = "remindMethodConfig", expression = "java(JSON.parseObject(source.getRemindMethodConfig(), RemindMethodConfigBO.class))")
    List<RemindPlanRuleBO> remindPlanRulePO2ListBO(List<RemindPlanRule> source);
}


