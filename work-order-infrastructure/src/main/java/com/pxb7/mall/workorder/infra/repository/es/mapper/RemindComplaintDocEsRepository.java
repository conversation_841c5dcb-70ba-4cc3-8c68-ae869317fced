package com.pxb7.mall.workorder.infra.repository.es.mapper;

import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.Query;
import co.elastic.clients.elasticsearch._types.query_dsl.QueryBuilders;
import co.elastic.clients.elasticsearch._types.query_dsl.TermQuery;
import com.pxb7.mall.workorder.client.enums.CompleteStatusEnum;
import com.pxb7.mall.workorder.client.enums.TimeoutStatusEnum;
import com.pxb7.mall.workorder.infra.model.RemindComplaintReqPO;
import com.pxb7.mall.workorder.infra.repository.es.entity.RemindComplaintDoc;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.elasticsearch.client.elc.ElasticsearchTemplate;
import org.springframework.data.elasticsearch.client.elc.NativeQuery;
import org.springframework.data.elasticsearch.client.elc.NativeQueryBuilder;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHitSupport;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.SearchPage;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Slf4j
@Repository
public class RemindComplaintDocEsRepository {

    @Resource
    private ElasticsearchTemplate elasticsearchTemplate;



    public SearchPage<RemindComplaintDoc> pageQueryComplaint(RemindComplaintReqPO.PagePO pagePO) {

        // 构造查询条件
        List<Query> mustQueryList = new ArrayList<>();

        if (StringUtils.isNotBlank(pagePO.getWorkOrderId())) {
            TermQuery workOrderIdQuery = QueryBuilders.term().field("work_order_id")
                .value(pagePO.getWorkOrderId()).build();
            mustQueryList.add(workOrderIdQuery._toQuery());
        }
        // 构造排序条件
        Sort sort = Sort.by(Sort.Order.desc("create_time"));

        // 构建最终查询结构
        var nativeQuery = NativeQuery.builder().withQuery(QueryBuilders.bool().must(mustQueryList).build()._toQuery())
            .withPageable(PageRequest.of((int)pagePO.getPageIndex() - 1, (int)pagePO.getPageSize()))
            .withSort(sort).withTrackTotalHits(true).build();

        log.info("pageQueryComplaint DSL语句：{}", nativeQuery.getQuery().toString());

        SearchHits<RemindComplaintDoc> searchHits = elasticsearchTemplate.search(nativeQuery, RemindComplaintDoc.class);
        return SearchHitSupport.searchPageFor(searchHits, nativeQuery.getPageable());
    }

    /**
     * 查询超时工单数量
     * @param timoutStatus
     * @return
     */
    public Long countWorkOrder(TimeoutStatusEnum timoutStatus) {
        NativeQueryBuilder nativeQueryBuilder = new NativeQueryBuilder();
        BoolQuery.Builder boolFilter = QueryBuilders.bool();
        boolFilter.filter(QueryBuilders.term(a -> a.field("complete_status")
                .value(CompleteStatusEnum.IN_PROCESS.getCode())));
        boolFilter.filter(QueryBuilders.term(a -> a.field("time_out_status")
                .value(timoutStatus.getCode())));

        nativeQueryBuilder.withQuery(boolFilter.build()._toQuery());

        SearchHits<RemindComplaintDoc> searchHits = elasticsearchTemplate.search(nativeQueryBuilder.build(), RemindComplaintDoc.class);
        return searchHits.getTotalHits();
    }

    /**
     * 通过房间查询客诉工单预警记录
     * @param groupId
     * @param customerCareId
     * @return
     */
    public RemindComplaintDoc searchByGroupId(String groupId, String customerCareId) {
        NativeQueryBuilder nativeQueryBuilder = new NativeQueryBuilder();
        BoolQuery.Builder boolFilter = QueryBuilders.bool();
        boolFilter.filter(QueryBuilders.term(a -> a.field("group_id").value(groupId)));
        boolFilter.filter(QueryBuilders.term(a -> a.field("complete_status")
                .value(CompleteStatusEnum.IN_PROCESS.getCode())));
        if (StringUtils.isNotBlank(customerCareId)) {
            boolFilter.filter(QueryBuilders.term(a -> a.field("handle_user_id").value(customerCareId)));
        }
        nativeQueryBuilder.withQuery(boolFilter.build()._toQuery());

        SearchHit<RemindComplaintDoc> searchHit = elasticsearchTemplate.searchOne(
                nativeQueryBuilder.build(), RemindComplaintDoc.class);
        return Objects.isNull(searchHit) ? null : searchHit.getContent();
    }

}
