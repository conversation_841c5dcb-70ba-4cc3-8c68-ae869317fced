package com.pxb7.mall.workorder.warmup;

import com.pxb7.mall.components.warmup.PxWarmUpPostProcess;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.logging.HttpLoggingInterceptor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.io.IOException;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2025/09/10 17:57
 **/
@Slf4j
@Component
public class HttpWarmUp  implements PxWarmUpPostProcess {

    @Value("${app.warmup.base-url:http://n1-im-windows.pxb7.com}")
    private String baseUrl;

    @Value("${app.warmup.timeout:3}")
    private int timeoutSeconds;

    private OkHttpClient httpClient;
    @PostConstruct
    public void init() {
        // 创建日志拦截器
        HttpLoggingInterceptor loggingInterceptor = new HttpLoggingInterceptor(log::info);
        loggingInterceptor.setLevel(HttpLoggingInterceptor.Level.BASIC);

        // 构建OkHttpClient
        this.httpClient = new OkHttpClient.Builder().connectTimeout(timeoutSeconds, TimeUnit.SECONDS)
            .readTimeout(timeoutSeconds, TimeUnit.SECONDS)
            .writeTimeout(timeoutSeconds, TimeUnit.SECONDS)
            .addInterceptor(loggingInterceptor)
            .retryOnConnectionFailure(true)
            .build();

        log.info("HttpWarmUp initialized with baseUrl: {}, timeout: {}s", baseUrl, timeoutSeconds);
    }

    @PreDestroy
    public void destroy() {
        if (httpClient != null) {
            httpClient.dispatcher().executorService().shutdown();
            httpClient.connectionPool().evictAll();
            log.info("HttpWarmUp resources cleaned up");
        }
    }
    private void callInnerApi() throws IOException {
        String url = baseUrl + "/api/work-order/web/bargain-ticket/inner/detail";

        Request request = new Request.Builder().url(url)
            .get()
            .addHeader("Content-Type", "application/json")
            .addHeader("User-Agent", "ass-WarmUp/1.0")
            .build();

        try (Response response = httpClient.newCall(request).execute()) {
            String responseBody = response.body() != null ? response.body().string() : "";

            if (response.isSuccessful()) {
                log.info("Successfully called AfcQuestionConf User API, response length: {}", responseBody.length());
            } else {
                log.warn("AfcQuestionConf API call failed with status: {}, body: {}", response.code(), responseBody);
                throw new IOException("API call failed with status: " + response.code());
            }
        }
    }

    @Override
    public void warmUpProcess() {
        try {
            // 调用内部接口
            this.callInnerApi();
        } catch (Exception e) {
            log.error("HttpWarmUp process failed", e);
        }
    }

    @Override
    public int loopExecNum() {
        return 30;
    }

    @Override
    public boolean loopExecute() {
        return true;
    }
}
