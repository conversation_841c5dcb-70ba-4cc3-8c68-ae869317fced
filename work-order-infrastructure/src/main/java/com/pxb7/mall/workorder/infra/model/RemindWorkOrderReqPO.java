package com.pxb7.mall.workorder.infra.model;

import java.util.Date;
import java.time.*;
import java.math.BigDecimal;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.ToString;


/**
 * 商品工单预警记录(RemindWorkOrder)实体类
 *
 * <AUTHOR>
 * @since 2025-04-07 11:58:57
 */
public class RemindWorkOrderReqPO {

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class AddPO {


        private String remindId;


        private String workOrderId;


        private Integer workOrderStatus;


        private String productId;


        private String productCode;


        private String gameId;


        private Integer completeStatus;


        private Integer timeOutStatus;


        private String artDesignerId;


        private String followerId;


        private String auditUserId;


        private LocalDateTime followedUpInflowTime;


        private LocalDateTime expectCompleteTime;


        private LocalDateTime completeTime;


        private Long gameConfigId;


        private Long remindPlanId;


        private Long remindSubPlanId;


        private String createUserId;


        private String updateUserId;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class UpdatePO {


        private Long id;


        private String remindId;


        private String workOrderId;


        private Integer workOrderStatus;


        private String productId;


        private String gameId;


        private Integer completeStatus;


        private Integer timeOutStatus;


        private String artDesignerId;


        private String followerId;


        private String auditUserId;


        private LocalDateTime expectCompleteTime;


        private LocalDateTime completeTime;


        private Long gameConfigId;


        private Long remindPlanId;


        private Long remindSubPlanId;


        private String createUserId;


        private String updateUserId;


    }


    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class DelPO {
        private Long id;
    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class SearchPO {

        private String remindId;


        private String workOrderId;


        private Integer workOrderStatus;


        private String productId;


        private Integer completeStatus;


        private Integer timeOutStatus;


        private String artDesignerId;


        private String followerId;


        private String auditUserId;


        private LocalDateTime expectCompleteTime;


        private Long gameConfigId;


        private Long remindPlanId;


        private Long remindSubPlanId;


        private String createUserId;


        private String updateUserId;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class PagePO {

        private Long id;

        private String workOrderId;

        private Integer completeStatus;

        private Integer timeOutStatus;

        private LocalDateTime expectCompleteTime;

        /**
         * 页码，从1开始
         */
        private long pageIndex;
        /**
         * 每页数量
         */
        private long pageSize;

    }

}

