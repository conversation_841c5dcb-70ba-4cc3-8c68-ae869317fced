package com.pxb7.mall.workorder.app.model;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;


/**
 * 预警计划游戏配置(RemindPlanGameConfig)实体类
 *
 * <AUTHOR>
 * @since 2025-03-24 21:12:18
 */
public class RemindPlanGameConfigRespDTO {


    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class DetailDTO {
        private Long id;
        private String gameId;
        private String gameName;
        private Integer maker;
        private Integer channel;
        private String expectCompleteTimeConfig;
        private String imCountDownTimeConfig;
        private Long remindPlanId;
        private Long remindSubPlanId;
    }
}

