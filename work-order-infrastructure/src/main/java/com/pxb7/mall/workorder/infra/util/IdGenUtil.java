package com.pxb7.mall.workorder.infra.util;

import com.alibaba.cola.exception.BizException;
import com.pxb7.mall.components.idgen.IdGen;
import com.pxb7.mall.trade.order.client.dto.ErrorCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

@Slf4j
public class IdGenUtil {

    private final static String CURRENT_SYSTEM_NO = "2";

    private final static Integer SPILT_NUM = 4;

    private IdGenUtil() {
    }

    private static IdGen idGen = SpringUtils.getBean(IdGen.class);
    public static Long getId() {
        if (Objects.isNull(idGen)) {
            throw new BizException(ErrorCode.SYSTEM_ERROR.getErrCode(), "IdGen is null");
        }
        return idGen.nextId();
    }

    /**
     * 传递 带有买家id后四位的参数, 可以是买家用户id, 也可以订单id
     *
     * @param shardingId
     * @return
     */
    public static String getShardingColumnId(String shardingId) {
        if (StringUtils.isBlank(shardingId)) {
            log.error("生成sharding key失败，shardingId：{}", shardingId);
            throw new BizException(ErrorCode.SHARDING_KEY_ERROR.getErrCode(), ErrorCode.SHARDING_KEY_ERROR.getErrDesc());
        }

        if (shardingId.length() < 4) {
            shardingId = "0000".substring(shardingId.length()) + shardingId;
        }

        return String.valueOf(getId())
                .concat(CURRENT_SYSTEM_NO)
                .concat(shardingId.substring(shardingId.length() - SPILT_NUM));
    }

}
