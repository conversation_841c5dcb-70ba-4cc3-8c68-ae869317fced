package com.pxb7.mall.workorder.infra.config;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.pxb7.mall.components.env.profile.PxProfileHelper;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class MybatisPlusConfig {
    //定义一个mybatisPlus的拦截器 再 add一个分页拦截器
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        //1.初始化核心插件
        MybatisPlusInterceptor mybatisPlusInterceptor = new MybatisPlusInterceptor();
        //2.添加分页插件
        mybatisPlusInterceptor.addInnerInterceptor(new PaginationInnerInterceptor());
        return mybatisPlusInterceptor;
    }

    /**
     * 自动填充处理器
     */
    @Bean
    public MetaObjectHandlerImp metaObjectHandlerImp() {
        return new MetaObjectHandlerImp();
    }

    static class MetaObjectHandlerImp implements MetaObjectHandler {

        @Override
        public void insertFill(MetaObject metaObject) {
            if (metaObject.hasSetter("envProfile")) {
                final int profileValue = PxProfileHelper.getProfile().getProfileValue();
                this.strictInsertFill(metaObject, "envProfile", Integer.class, profileValue);
            }

        }

        @Override
        public void updateFill(MetaObject metaObject) {

        }
    }

}