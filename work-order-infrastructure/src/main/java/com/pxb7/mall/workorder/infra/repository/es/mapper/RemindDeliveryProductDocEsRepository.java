package com.pxb7.mall.workorder.infra.repository.es.mapper;

import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.Query;
import co.elastic.clients.elasticsearch._types.query_dsl.QueryBuilders;
import co.elastic.clients.elasticsearch._types.query_dsl.TermQuery;
import com.pxb7.mall.workorder.infra.model.RemindDeliveryProductReqPO;
import com.pxb7.mall.workorder.infra.repository.es.entity.RemindDeliveryProductDoc;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.elasticsearch.client.elc.ElasticsearchTemplate;
import org.springframework.data.elasticsearch.client.elc.NativeQuery;
import org.springframework.data.elasticsearch.client.elc.NativeQueryBuilder;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHitSupport;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.SearchPage;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Slf4j
@Repository
public class RemindDeliveryProductDocEsRepository  {

    @Resource
    private ElasticsearchTemplate elasticsearchTemplate;


    public SearchPage<RemindDeliveryProductDoc> pageQueryDeliveryProduct(RemindDeliveryProductReqPO.PagePO deliveryProductPagePO) {

        // 构造查询条件
        List<Query> mustQueryList = new ArrayList<>();

        if (StringUtils.isNotBlank(deliveryProductPagePO.getProductId())) {
            TermQuery workOrderIdQuery = QueryBuilders.term().field("product_id")
                .value(deliveryProductPagePO.getProductId()).build();
            mustQueryList.add(workOrderIdQuery._toQuery());
        }
        if (StringUtils.isNotBlank(deliveryProductPagePO.getProductCode())) {
            TermQuery workOrderIdQuery = QueryBuilders.term().field("product_code")
                .value(deliveryProductPagePO.getProductCode()).build();
            mustQueryList.add(workOrderIdQuery._toQuery());
        }

        if (StringUtils.isNotBlank(deliveryProductPagePO.getOrderId())) {
            TermQuery workOrderIdQuery = QueryBuilders.term().field("order_id")
                .value(deliveryProductPagePO.getOrderId()).build();
            mustQueryList.add(workOrderIdQuery._toQuery());
        }

        if (StringUtils.isNotBlank(deliveryProductPagePO.getOrderItemId())) {
            TermQuery workOrderIdQuery = QueryBuilders.term().field("order_item_id")
                .value(deliveryProductPagePO.getOrderItemId()).build();
            mustQueryList.add(workOrderIdQuery._toQuery());
        }

        if (StringUtils.isNotBlank(deliveryProductPagePO.getGroupId())) {
            TermQuery workOrderIdQuery = QueryBuilders.term().field("group_id")
                .value(deliveryProductPagePO.getGroupId()).build();
            mustQueryList.add(workOrderIdQuery._toQuery());
        }

        if (StringUtils.isNotBlank(deliveryProductPagePO.getDeliveryCustomerCare())) {
            TermQuery workOrderIdQuery = QueryBuilders.term().field("delivery_customer_care")
                .value(deliveryProductPagePO.getDeliveryCustomerCare()).build();
            mustQueryList.add(workOrderIdQuery._toQuery());
        }
        // 构造排序条件
        Sort sort = Sort.by(Sort.Order.desc("create_time"));
        // 构建最终查询结构
        var nativeQuery = NativeQuery.builder().withQuery(QueryBuilders.bool().must(mustQueryList).build()._toQuery())
            .withPageable(PageRequest.of((int)deliveryProductPagePO.getPageIndex() - 1, (int)deliveryProductPagePO.getPageSize()))
            .withSort(sort).withTrackTotalHits(true).build();

        log.info("pageQueryDeliveryProduct DSL语句：{}", nativeQuery.getQuery().toString());

        SearchHits<RemindDeliveryProductDoc> searchHits = elasticsearchTemplate.search(nativeQuery, RemindDeliveryProductDoc.class);
        return SearchHitSupport.searchPageFor(searchHits, nativeQuery.getPageable());

    }

    /**
     * 通过房间查询账号交付预警记录
     * @param groupId
     * @param customerCareId
     * @return
     */
    public RemindDeliveryProductDoc searchByGroupId(String groupId, String customerCareId) {
        NativeQueryBuilder nativeQueryBuilder = new NativeQueryBuilder();
        BoolQuery.Builder boolFilter = QueryBuilders.bool();
        boolFilter.filter(QueryBuilders.term(a -> a.field("group_id").value(groupId)));
        if (StringUtils.isNotBlank(customerCareId)) {
            boolFilter.filter(QueryBuilders.term(a -> a.field("delivery_customer_care").value(customerCareId)));
        }
        nativeQueryBuilder.withQuery(boolFilter.build()._toQuery());

        SearchHit<RemindDeliveryProductDoc> searchHit = elasticsearchTemplate.searchOne(
                nativeQueryBuilder.build(), RemindDeliveryProductDoc.class);
        return Objects.isNull(searchHit) ? null : searchHit.getContent();
    }

}
