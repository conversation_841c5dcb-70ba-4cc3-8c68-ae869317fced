package com.pxb7.mall.workorder.app.model;

import java.time.*;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Min;
import lombok.ToString;


/**
 * 客诉工单预警记录(RemindComplaint)实体类
 *
 * <AUTHOR>
 * @since 2025-04-24 23:33:20
 */
public class RemindComplaintReqDTO {

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class AddDTO {


        @NotBlank(message = "remindId不能为空")
        private String remindId;


        @NotBlank(message = "workOrderId不能为空")
        private String workOrderId;


        @NotBlank(message = "workOrderTitle不能为空")
        private String workOrderTitle;


        @NotBlank(message = "complaintContent不能为空")
        private String complaintContent;


        @NotBlank(message = "groupId不能为空")
        private String groupId;


        @NotNull(message = "completeStatus不能为空")
        private Integer completeStatus;


        @NotNull(message = "timeOutStatus不能为空")
        private Integer timeOutStatus;


        @NotBlank(message = "handleUserId不能为空")
        private String handleUserId;


        @NotNull(message = "expectCompleteTime不能为空")
        private LocalDateTime expectCompleteTime;


        @NotNull(message = "completeTime不能为空")
        private LocalDateTime completeTime;


        @NotNull(message = "gameConfigId不能为空")
        private Long gameConfigId;


        @NotNull(message = "remindPlanId不能为空")
        private Long remindPlanId;


        @NotNull(message = "remindSubPlanId不能为空")
        private Long remindSubPlanId;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class UpdateDTO {


        @NotNull(message = "id不能为空")
        private Long id;


        @NotBlank(message = "remindId不能为空")
        private String remindId;


        @NotBlank(message = "workOrderId不能为空")
        private String workOrderId;


        @NotBlank(message = "workOrderTitle不能为空")
        private String workOrderTitle;


        @NotBlank(message = "complaintContent不能为空")
        private String complaintContent;


        @NotBlank(message = "groupId不能为空")
        private String groupId;


        @NotNull(message = "completeStatus不能为空")
        private Integer completeStatus;


        @NotNull(message = "timeOutStatus不能为空")
        private Integer timeOutStatus;


        @NotBlank(message = "handleUserId不能为空")
        private String handleUserId;


        @NotNull(message = "expectCompleteTime不能为空")
        private LocalDateTime expectCompleteTime;


        @NotNull(message = "completeTime不能为空")
        private LocalDateTime completeTime;


        @NotNull(message = "gameConfigId不能为空")
        private Long gameConfigId;


        @NotNull(message = "remindPlanId不能为空")
        private Long remindPlanId;


        @NotNull(message = "remindSubPlanId不能为空")
        private Long remindSubPlanId;


    }


    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class DelDTO {
        @NotNull(message = "id不能为空")
        private Long id;
    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class SearchDTO {

        @NotBlank(message = "remindId不能为空")
        private String remindId;


        @NotBlank(message = "workOrderId不能为空")
        private String workOrderId;


        @NotBlank(message = "workOrderTitle不能为空")
        private String workOrderTitle;


        @NotBlank(message = "complaintContent不能为空")
        private String complaintContent;


        @NotBlank(message = "groupId不能为空")
        private String groupId;


        @NotNull(message = "completeStatus不能为空")
        private Integer completeStatus;


        @NotNull(message = "timeOutStatus不能为空")
        private Integer timeOutStatus;


        @NotBlank(message = "handleUserId不能为空")
        private String handleUserId;


        @NotNull(message = "expectCompleteTime不能为空")
        private LocalDateTime expectCompleteTime;


        @NotNull(message = "completeTime不能为空")
        private LocalDateTime completeTime;


        @NotNull(message = "gameConfigId不能为空")
        private Long gameConfigId;


        @NotNull(message = "remindPlanId不能为空")
        private Long remindPlanId;


        @NotNull(message = "remindSubPlanId不能为空")
        private Long remindSubPlanId;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class PageDTO {

        private String workOrderId;

        /**
         * 页码，从1开始
         */
        @NotNull(message = "pageIndex不能为空")
        @Min(value = 1, message = "pageIndex不能小于1")
        private Integer pageIndex;
        /**
         * 每页数量
         */
        @NotNull(message = "pageSize不能为空")
        private Integer pageSize;

    }

}

