package com.pxb7.mall.workorder.domain.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;


/**
 * 预警执行计划
 *
 * <AUTHOR>
 * @since 2025-04-22 14:13:43
 */

@Data
@Accessors(chain = true)
public class RemindPlanRecordBO {

    /**
     * 预警记录id前缀
     */
    private String recordIdPrefix;

    /**
     * 业务类型
     */
    private Integer bizType;

    /**
     * 业务id
     */
    private String bizId;

    /**
     * 预警id
     */
    private String remindId;

    /**
     * 预警计划id
     */
    private Long remindPlanId;

    /**
     * 预警子计划id
     */
    private Long remindSubPlanId;

    /**
     * 预期完结时间
     */
    private LocalDateTime expectCompleteTime;

}

