package com.pxb7.mall.workorder.app.util;

import com.alibaba.cola.exception.Assert;
import com.pxb7.mall.auth.dto.AdminUserDTO;
import com.pxb7.mall.workorder.app.model.AssPlanGameConfigDTO;
import com.pxb7.mall.workorder.app.model.AssRemindPlanReqDTO;
import com.pxb7.mall.workorder.app.model.ComplaintPlanGameConfigDTO;
import com.pxb7.mall.workorder.app.model.ComplaintRemindPlanReqDTO;
import com.pxb7.mall.workorder.infra.enums.RemindPlanOnShelfTypeEnum;
import com.pxb7.mall.workorder.infra.enums.RemindPlanServiceTypeEnum;
import com.pxb7.mall.workorder.infra.enums.RemindPlanWorkOrderStatusEnum;
import com.pxb7.mall.workorder.infra.util.IdGenUtil;
import com.pxb7.mall.workorder.app.mapping.RemindPlanAppMapping;
import com.pxb7.mall.workorder.app.mapping.RemindPlanGameConfigAppMapping;
import com.pxb7.mall.workorder.app.mapping.RemindPlanRuleAppMapping;
import com.pxb7.mall.workorder.app.model.PlanGameConfigDTO;
import com.pxb7.mall.workorder.app.model.PlanRuleDTO;
import com.pxb7.mall.workorder.app.model.RemindPlanReqDTO;
import com.pxb7.mall.workorder.domain.model.RemindPlanBO;
import com.pxb7.mall.workorder.domain.model.RemindPlanGameConfigBO;
import com.pxb7.mall.workorder.domain.model.RemindPlanRuleBO;
import com.pxb7.mall.workorder.domain.model.RemindSubPlanBO;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.pxb7.mall.workorder.client.enums.BizErrorCodeEnum.PARAM_ERROR;

public class RemindPlanBuildUtil {

    public static RemindPlanBO buildFromAddDTO(RemindPlanReqDTO.AddDTO param, AdminUserDTO adminUser){

        RemindPlanServiceTypeEnum serviceTypeEnum = RemindPlanServiceTypeEnum.getEnum(param.getServiceType());
        Assert.isTrue(serviceTypeEnum != null, PARAM_ERROR.getErrCode(), "业务类型暂不支持");
        List<Integer> workOrderStatuses = param.getWorkOrderStatuses();

        RemindPlanBO remindPlanBO = RemindPlanAppMapping.INSTANCE.remindPlanAddDTO2BO(param);
        remindPlanBO.setId(IdGenUtil.getId());
        remindPlanBO.setCreateUserId(adminUser.getUserId());
        remindPlanBO.setUpdateUserId(adminUser.getUserId());
        if (RemindPlanServiceTypeEnum.DELIVERY_PRODUCT.equals(serviceTypeEnum)){
            // todo 循环相关的参数要判空
            for (Integer businessType : param.getBusinessTypes()) {
                RemindSubPlanBO remindSubPlanBO = new RemindSubPlanBO();
                remindSubPlanBO.setRemindPlanId(remindPlanBO.getId());
                remindSubPlanBO.setId(IdGenUtil.getId());
                remindSubPlanBO.setBusinessType(businessType);
                remindSubPlanBO.setCreateUserId(adminUser.getUserId());
                remindSubPlanBO.setUpdateUserId(adminUser.getUserId());
                remindPlanBO.putRemindSubPlan(remindSubPlanBO);
            }

        }else if (RemindPlanServiceTypeEnum.WORK_ORDER.equals(serviceTypeEnum)){
            List<Integer> memberships = param.getMemberships();
            if (workOrderStatuses.contains(RemindPlanWorkOrderStatusEnum.WAIT_ACCEPT.getValue())){
                Assert.isTrue(workOrderStatuses.size() == 1, PARAM_ERROR.getErrCode(), "工单状态:待接单不支持和其他转态一起处理");
            }
            for (Integer membership : memberships){
                for (Integer workOrderStatus : workOrderStatuses) {
                    for (Integer shelfType : RemindPlanOnShelfTypeEnum.getAllTypes()) {
                        RemindSubPlanBO remindSubPlanBO = new RemindSubPlanBO();
                        remindSubPlanBO.setRemindPlanId(remindPlanBO.getId());
                        remindSubPlanBO.setId(IdGenUtil.getId());
                        remindSubPlanBO.setWorkOrderStatus(workOrderStatus);
                        remindSubPlanBO.setOnShelfType(shelfType);
                        remindSubPlanBO.setMembership(membership);
                        remindSubPlanBO.setCreateUserId(adminUser.getUserId());
                        remindSubPlanBO.setUpdateUserId(adminUser.getUserId());
                        remindPlanBO.putRemindSubPlan(remindSubPlanBO);
                    }
                }
            }

        }


        boolean specailWorkOrderStatus = RemindPlanServiceTypeEnum.WORK_ORDER.equals(serviceTypeEnum);
        Function<PlanGameConfigDTO, String> groupFun = a -> String.valueOf(a.getGameId());
        if (specailWorkOrderStatus) {
            groupFun = a -> a.getGameId() + "_" + a.getOnShelfType();
        }
        List<PlanGameConfigDTO> planGameConfigs = param.getPlanGameConfigs();
        Map<String, PlanGameConfigDTO>
            plagGameConfigDTOMap = planGameConfigs.stream().collect(Collectors.toMap(groupFun, Function.identity(), (l, r) -> l));

        for (RemindSubPlanBO remindSubPlan : remindPlanBO.getRemindSubPlans()) {
            Integer onShelfType = remindSubPlan.getOnShelfType();
            if (specailWorkOrderStatus){
                Assert.notNull(onShelfType, PARAM_ERROR.getErrCode(), "上架方式不能为空");
                for (PlanGameConfigDTO planGameConfigDTO : plagGameConfigDTOMap.values()) {
                    Assert.notNull(planGameConfigDTO.getOnShelfType(), PARAM_ERROR.getErrCode(), "上架方式不能为空!");
                }
            }
            List<RemindPlanGameConfigBO> remindPlanGameConfigs =new ArrayList<>();
            for (PlanGameConfigDTO planGameConfigDTO : plagGameConfigDTOMap.values()) {
                if (specailWorkOrderStatus && !onShelfType.equals(planGameConfigDTO.getOnShelfType())) {
                    continue;
                }
                RemindPlanGameConfigBO remindPlanGameConfigBO =
                    RemindPlanGameConfigAppMapping.INSTANCE.remindPlanGameConfigDTO2BO(planGameConfigDTO);
                remindPlanGameConfigBO.setId(IdGenUtil.getId());
                remindPlanGameConfigBO.setRemindPlanId(remindSubPlan.getRemindPlanId());
                remindPlanGameConfigBO.setRemindSubPlanId(remindSubPlan.getId());
                remindPlanGameConfigBO.setCreateUserId(adminUser.getUserId());
                remindPlanGameConfigBO.setUpdateUserId(adminUser.getUserId());
                remindPlanGameConfigs.add(remindPlanGameConfigBO);
            }
            remindSubPlan.setRemindPlanGameConfigs(remindPlanGameConfigs);
        }
        remindPlanBO.setRemindPlanRules(buildPlanRules(param.getPlanRules(), adminUser, remindPlanBO.getId()));

        return remindPlanBO;
    }

    // 组装售后预警设置数据
    public static RemindPlanBO buildAssFromAddDTO(AssRemindPlanReqDTO.AddDTO param, AdminUserDTO adminUser){

        RemindPlanServiceTypeEnum serviceTypeEnum = RemindPlanServiceTypeEnum.getEnum(param.getServiceType());
        Assert.isTrue(serviceTypeEnum != null, PARAM_ERROR.getErrCode(), "业务类型暂不支持");

        RemindPlanBO remindPlanBO = RemindPlanAppMapping.INSTANCE.remindPlanAddDTO2BO(param);
        remindPlanBO.setId(IdGenUtil.getId());
        remindPlanBO.setServiceType(param.getServiceType());
        remindPlanBO.setCreateUserId(adminUser.getUserId());
        remindPlanBO.setUpdateUserId(adminUser.getUserId());

        if (RemindPlanServiceTypeEnum.ASS_ORDER.equals(serviceTypeEnum)){
            RemindSubPlanBO remindSubPlanBO = new RemindSubPlanBO();
            remindSubPlanBO.setRemindPlanId(remindPlanBO.getId());
            remindSubPlanBO.setId(IdGenUtil.getId());
            remindSubPlanBO.setMembership(param.getMembership());
            remindSubPlanBO.setWorkOrderType(param.getWorkOrderType());
            remindSubPlanBO.setCreateUserId(adminUser.getUserId());
            remindSubPlanBO.setUpdateUserId(adminUser.getUserId());
            remindPlanBO.putRemindSubPlan(remindSubPlanBO);

        }

        List<AssPlanGameConfigDTO> planGameConfigs = param.getPlanGameConfigs();

        for (RemindSubPlanBO remindSubPlan : remindPlanBO.getRemindSubPlans()) {
            List<RemindPlanGameConfigBO> remindPlanGameConfigs =new ArrayList<>();
            for (AssPlanGameConfigDTO AssPlanGameConfigDTO : planGameConfigs) {

                RemindPlanGameConfigBO remindPlanGameConfigBO =
                    RemindPlanGameConfigAppMapping.INSTANCE.remindAssPlanGameConfigDTO2BO(AssPlanGameConfigDTO);
                remindPlanGameConfigBO.setId(IdGenUtil.getId());
                remindPlanGameConfigBO.setRemindPlanId(remindSubPlan.getRemindPlanId());
                remindPlanGameConfigBO.setRemindSubPlanId(remindSubPlan.getId());
                remindPlanGameConfigBO.setCreateUserId(adminUser.getUserId());
                remindPlanGameConfigBO.setUpdateUserId(adminUser.getUserId());
                remindPlanGameConfigs.add(remindPlanGameConfigBO);
            }
            remindSubPlan.setRemindPlanGameConfigs(remindPlanGameConfigs);
        }
        remindPlanBO.setRemindPlanRules(buildPlanRules(param.getPlanRules(), adminUser, remindPlanBO.getId()));

        return remindPlanBO;
    }

    // 组装售后预警设置数据
    public static RemindPlanBO buildComplaintFromAddDTO(ComplaintRemindPlanReqDTO.AddDTO param, AdminUserDTO adminUser){

        RemindPlanServiceTypeEnum serviceTypeEnum = RemindPlanServiceTypeEnum.getEnum(param.getServiceType());
        Assert.isTrue(serviceTypeEnum != null, PARAM_ERROR.getErrCode(), "业务类型暂不支持");

        RemindPlanBO remindPlanBO = RemindPlanAppMapping.INSTANCE.remindPlanAddDTO2BO(param);
        remindPlanBO.setId(IdGenUtil.getId());
        remindPlanBO.setServiceType(param.getServiceType());
        remindPlanBO.setCreateUserId(adminUser.getUserId());
        remindPlanBO.setUpdateUserId(adminUser.getUserId());

        if (RemindPlanServiceTypeEnum.COMPLAIN_ORDER.equals(serviceTypeEnum)){
            RemindSubPlanBO remindSubPlanBO = new RemindSubPlanBO();
            remindSubPlanBO.setRemindPlanId(remindPlanBO.getId());
            remindSubPlanBO.setId(IdGenUtil.getId());
            remindSubPlanBO.setComplaintLevel(param.getComplaintLevel());
            remindSubPlanBO.setCreateUserId(adminUser.getUserId());
            remindSubPlanBO.setUpdateUserId(adminUser.getUserId());
            remindPlanBO.putRemindSubPlan(remindSubPlanBO);

        }

        List<ComplaintPlanGameConfigDTO> planGameConfigs = param.getPlanGameConfigs();

        for (RemindSubPlanBO remindSubPlan : remindPlanBO.getRemindSubPlans()) {
            List<RemindPlanGameConfigBO> remindPlanGameConfigs =new ArrayList<>();
            for (ComplaintPlanGameConfigDTO complaintPlanGameConfigDTO : planGameConfigs) {

                RemindPlanGameConfigBO remindPlanGameConfigBO =
                    RemindPlanGameConfigAppMapping.INSTANCE.remindAssPlanGameConfigDTO2BO(complaintPlanGameConfigDTO);
                remindPlanGameConfigBO.setId(IdGenUtil.getId());
                remindPlanGameConfigBO.setGameId("");
                remindPlanGameConfigBO.setGameName("");
                remindPlanGameConfigBO.setRemindPlanId(remindSubPlan.getRemindPlanId());
                remindPlanGameConfigBO.setRemindSubPlanId(remindSubPlan.getId());
                remindPlanGameConfigBO.setCreateUserId(adminUser.getUserId());
                remindPlanGameConfigBO.setUpdateUserId(adminUser.getUserId());
                remindPlanGameConfigs.add(remindPlanGameConfigBO);
            }
            remindSubPlan.setRemindPlanGameConfigs(remindPlanGameConfigs);
        }
        remindPlanBO.setRemindPlanRules(buildPlanRules(param.getPlanRules(), adminUser, remindPlanBO.getId()));

        return remindPlanBO;
    }

    private static List<RemindPlanRuleBO> buildPlanRules(List<PlanRuleDTO> planRules, AdminUserDTO adminUser, Long remindPlanId) {
        List<RemindPlanRuleBO> remindPlanRuleBOs = new ArrayList<>();
        for (PlanRuleDTO remindPlanRuleDTO : planRules) {
            RemindPlanRuleBO remindPlanRuleBO =
                RemindPlanRuleAppMapping.INSTANCE.remindPlanRuleDTO2BO(remindPlanRuleDTO);
            remindPlanRuleBO.setRemindPlanId(remindPlanId);
            remindPlanRuleBO.setId(IdGenUtil.getId());
            remindPlanRuleBO.setCreateUserId(adminUser.getUserId());
            remindPlanRuleBO.setUpdateUserId(adminUser.getUserId());
            remindPlanRuleBOs.add(remindPlanRuleBO);
        }
        return remindPlanRuleBOs;
    }
}
