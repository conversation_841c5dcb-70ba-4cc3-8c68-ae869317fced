package com.pxb7.mall.workorder.infra.repository.db;


import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import cn.hutool.core.collection.CollectionUtil;
import com.pxb7.mall.workorder.infra.model.RemindPlanGameConfigReqPO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.pxb7.mall.workorder.infra.repository.db.mapper.RemindPlanGameConfigMapper;
import com.pxb7.mall.workorder.infra.repository.db.entity.RemindPlanGameConfig;

/**
 * 预警计划游戏配置(RemindPlanGameConfig)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-24 21:09:56
 */
@Slf4j
@Repository
public class RemindPlanGameConfigDbRepository extends ServiceImpl<RemindPlanGameConfigMapper, RemindPlanGameConfig> implements RemindPlanGameConfigRepository {

    @Override
    public boolean insert(RemindPlanGameConfigReqPO.AddPO param) {
        RemindPlanGameConfig entity = new RemindPlanGameConfig();
        entity.setGameId(param.getGameId());
        entity.setGameName(param.getGameName());
        entity.setMaker(param.getMaker());
        entity.setChannel(param.getChannel());
        entity.setExpectCompleteTimeConfig(param.getExpectCompleteTimeConfig());
        entity.setImCountDownTimeConfig(param.getImCountDownTimeConfig());
        entity.setRemindPlanId(param.getRemindPlanId());
        entity.setRemindSubPlanId(param.getRemindSubPlanId());
        entity.setCreateUserId(param.getCreateUserId());
        entity.setUpdateUserId(param.getUpdateUserId());
        return this.save(entity);
    }


    @Override
    public boolean saveBatch(List<RemindPlanGameConfigReqPO.AddPO> params) {
        List<RemindPlanGameConfig> entities = new ArrayList<>();
        for (RemindPlanGameConfigReqPO.AddPO param : params) {
            RemindPlanGameConfig entity = new RemindPlanGameConfig();
            //entity.setId(param.getId());
            entity.setGameId(param.getGameId());
            entity.setGameName(param.getGameName());
            entity.setMaker(param.getMaker());
            entity.setChannel(param.getChannel());
            entity.setExpectCompleteTimeConfig(param.getExpectCompleteTimeConfig());
            entity.setImCountDownTimeConfig(param.getImCountDownTimeConfig());
            entity.setRemindPlanId(param.getRemindPlanId());
            entity.setRemindSubPlanId(param.getRemindSubPlanId());
            entity.setCreateUserId(param.getCreateUserId());
            entity.setUpdateUserId(param.getUpdateUserId());
            entities.add(entity);
        }
        return this.saveBatch(entities);
    }

    @Override
    public boolean update(RemindPlanGameConfigReqPO.UpdatePO param) {
        LambdaUpdateWrapper<RemindPlanGameConfig> updateWrapper = new LambdaUpdateWrapper<>();
        //where
        updateWrapper.eq(RemindPlanGameConfig::getId, param.getId());
        //set
        if (StringUtils.isNotBlank(param.getGameId())) {
            updateWrapper.set(RemindPlanGameConfig::getGameId, param.getGameId());
        }
        if (StringUtils.isNotBlank(param.getGameName())) {
            updateWrapper.set(RemindPlanGameConfig::getGameName, param.getGameName());
        }
        if (Objects.nonNull(param.getMaker())) {
            updateWrapper.set(RemindPlanGameConfig::getMaker, param.getMaker());
        }
        if (Objects.nonNull(param.getChannel())) {
            updateWrapper.set(RemindPlanGameConfig::getChannel, param.getChannel());
        }
        if (StringUtils.isNotBlank(param.getExpectCompleteTimeConfig())) {
            updateWrapper.set(RemindPlanGameConfig::getExpectCompleteTimeConfig, param.getExpectCompleteTimeConfig());
        }
        if (StringUtils.isNotBlank(param.getImCountDownTimeConfig())) {
            updateWrapper.set(RemindPlanGameConfig::getImCountDownTimeConfig, param.getImCountDownTimeConfig());
        }
        if (Objects.nonNull(param.getRemindPlanId())) {
            updateWrapper.set(RemindPlanGameConfig::getRemindPlanId, param.getRemindPlanId());
        }
        if (Objects.nonNull(param.getRemindSubPlanId())) {
            updateWrapper.set(RemindPlanGameConfig::getRemindSubPlanId, param.getRemindSubPlanId());
        }
        if (StringUtils.isNotBlank(param.getCreateUserId())) {
            updateWrapper.set(RemindPlanGameConfig::getCreateUserId, param.getCreateUserId());
        }
        if (StringUtils.isNotBlank(param.getUpdateUserId())) {
            updateWrapper.set(RemindPlanGameConfig::getUpdateUserId, param.getUpdateUserId());
        }
        return this.update(updateWrapper);
    }

    @Override
    public boolean deleteById(RemindPlanGameConfigReqPO.DelPO param) {
        return this.removeById(param.getId());
    }


    @Override
    public RemindPlanGameConfig findById(Long id) {
        return this.getById(id);
    }

    @Override
    public List<RemindPlanGameConfig> list(RemindPlanGameConfigReqPO.SearchPO param) {
        LambdaQueryWrapper<RemindPlanGameConfig> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(param.getGameId())) {
            queryWrapper.eq(RemindPlanGameConfig::getGameId, param.getGameId());
        }
        if (StringUtils.isNotBlank(param.getGameName())) {
            queryWrapper.eq(RemindPlanGameConfig::getGameName, param.getGameName());
        }
        if (Objects.nonNull(param.getMaker())) {
            queryWrapper.eq(RemindPlanGameConfig::getMaker, param.getMaker());
        }
        if (Objects.nonNull(param.getChannel())) {
            queryWrapper.eq(RemindPlanGameConfig::getChannel, param.getChannel());
        }
        if (StringUtils.isNotBlank(param.getExpectCompleteTimeConfig())) {
            queryWrapper.eq(RemindPlanGameConfig::getExpectCompleteTimeConfig, param.getExpectCompleteTimeConfig());
        }
        if (StringUtils.isNotBlank(param.getImCountDownTimeConfig())) {
            queryWrapper.eq(RemindPlanGameConfig::getImCountDownTimeConfig, param.getImCountDownTimeConfig());
        }
        if (Objects.nonNull(param.getRemindPlanId())) {
            queryWrapper.eq(RemindPlanGameConfig::getRemindPlanId, param.getRemindPlanId());
        }
        if (CollectionUtil.isNotEmpty(param.getRemindPlanIds())){
            queryWrapper.in(RemindPlanGameConfig::getRemindPlanId, param.getRemindPlanIds());
        }
        if (Objects.nonNull(param.getRemindSubPlanId())) {
            queryWrapper.eq(RemindPlanGameConfig::getRemindSubPlanId, param.getRemindSubPlanId());
        }
        if (CollectionUtil.isNotEmpty(param.getRemindSubPlanIds())){
            queryWrapper.in(RemindPlanGameConfig::getRemindSubPlanId, param.getRemindSubPlanIds());
        }

        if (StringUtils.isNotBlank(param.getCreateUserId())) {
            queryWrapper.eq(RemindPlanGameConfig::getCreateUserId, param.getCreateUserId());
        }
        if (StringUtils.isNotBlank(param.getUpdateUserId())) {
            queryWrapper.eq(RemindPlanGameConfig::getUpdateUserId, param.getUpdateUserId());
        }
        return this.list(queryWrapper);
    }

    @Override
    public Page<RemindPlanGameConfig> page(RemindPlanGameConfigReqPO.PagePO param) {
        LambdaQueryWrapper<RemindPlanGameConfig> queryWrapper = new LambdaQueryWrapper<>();
        // 返回分页查询结果
        if (StringUtils.isNotBlank(param.getGameId())) {
            queryWrapper.eq(RemindPlanGameConfig::getGameId, param.getGameId());
        }
        if (StringUtils.isNotBlank(param.getGameName())) {
            queryWrapper.eq(RemindPlanGameConfig::getGameName, param.getGameName());
        }
        if (Objects.nonNull(param.getMaker())) {
            queryWrapper.eq(RemindPlanGameConfig::getMaker, param.getMaker());
        }
        if (Objects.nonNull(param.getChannel())) {
            queryWrapper.eq(RemindPlanGameConfig::getChannel, param.getChannel());
        }
        if (StringUtils.isNotBlank(param.getExpectCompleteTimeConfig())) {
            queryWrapper.eq(RemindPlanGameConfig::getExpectCompleteTimeConfig, param.getExpectCompleteTimeConfig());
        }
        if (StringUtils.isNotBlank(param.getImCountDownTimeConfig())) {
            queryWrapper.eq(RemindPlanGameConfig::getImCountDownTimeConfig, param.getImCountDownTimeConfig());
        }
        if (Objects.nonNull(param.getRemindPlanId())) {
            queryWrapper.eq(RemindPlanGameConfig::getRemindPlanId, param.getRemindPlanId());
        }
        if (Objects.nonNull(param.getRemindSubPlanId())) {
            queryWrapper.eq(RemindPlanGameConfig::getRemindSubPlanId, param.getRemindSubPlanId());
        }
        if (StringUtils.isNotBlank(param.getCreateUserId())) {
            queryWrapper.eq(RemindPlanGameConfig::getCreateUserId, param.getCreateUserId());
        }
        if (StringUtils.isNotBlank(param.getUpdateUserId())) {
            queryWrapper.eq(RemindPlanGameConfig::getUpdateUserId, param.getUpdateUserId());
        }
        return this.page(new Page<>(param.getPageIndex(), param.getPageSize()), queryWrapper);
    }

    /**
     * 根据gameId和businessType查询
     * @param gameId
     * @param businessType
     * @return
     */
    @Override
    public RemindPlanGameConfig getRemindPlanGameConfig(String gameId, Integer businessType) {
        return this.getBaseMapper().selectByGameIdAndBusinessType(gameId, businessType);
    }

    /**
     * 根据gameId和工单信息查询预警计划游戏配置
     * @param gameId
     * @param workOrderStatus
     * @param onShelfType
     * @param membership
     * @return
     */
    @Override
    public RemindPlanGameConfig getRemindPlanGameConfig(String gameId, Integer workOrderStatus,
                                                        Integer onShelfType, Integer membership) {
        return this.getBaseMapper().selectByGameIdAndWorkOrderStatus(gameId, workOrderStatus, onShelfType, membership);
    }

    /**
     * 根据gameId和工单类型查询预警计划游戏配置
     * @param gameId
     * @param workOrderType
     * @param membership
     * @return
     */
    @Override
    public RemindPlanGameConfig getAssRemindPlanGameConfig(String gameId, Integer workOrderType, Integer membership) {
        return this.getBaseMapper().selectByGameIdAndWorkOrderType(gameId, workOrderType, membership);
    }

    /**
     * 根据complaintLevel和complaintChannel查询预警计划游戏配置
     * @param complaintLevel
     * @param complaintChannel
     * @return
     */
    @Override
    public RemindPlanGameConfig getComplaintRemindPlanGameConfig(Integer complaintLevel, Integer complaintChannel) {
        return this.getBaseMapper().selectByComplaintLevelAndChannel(complaintLevel, complaintChannel);
    }

}
