package com.pxb7.mall.workorder.infra.repository.db.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.Date;
import java.io.Serializable;

/**
 * 商品预警统计(AdsPxb7ProductForewarning)实体类
 *
 * <AUTHOR>
 * @since 2025-04-17 22:45:20
 */
@Data
@Accessors(chain = true)
@TableName(value = "ads_pxb7_product_forewarning")
@ToString
public class AdsPxb7ProductForewarning implements Serializable {
    private static final long serialVersionUID = 662859499888874889L;
    /**
     * 主键
     */
    private Long id;
    /**
     * 已接单已完结数
     */
    private Long acceptedCompletedWorkOrderCnt;
    /**
     * 已接单已失效数
     */
    private Long acceptedTerminatedWorkOrderCnt;
    /**
     * 已接单已超时数
     */
    private Long acceptedTimeoutedWorkOrderCnt;
    /**
     * 已接单即将超时数
     */
    private Long acceptedTimeoutingWorkOrderCnt;
    /**
     * 已接单数
     */
    private Long acceptedWorkOrderCnt;
    /**
     * 接单美工
     */
    private String artDesignerId;
    /**
     * 审核客服
     */
    private String auditUserId;
    /**
     * 创建日期
     */
    private String createDate;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 创建人
     */
    private String createUserId;
    /**
     * 待跟进已完结数
     */
    private Long followedCompletedWorkOrderCnt;
    /**
     * 待跟进已失效数
     */
    private Long followedTerminatedWorkOrderCnt;
    /**
     * 待跟进已超时数
     */
    private Long followedTimeoutedWorkOrderCnt;
    /**
     * 待跟进即将超时数
     */
    private Long followedTimeoutingWorkOrderCnt;
    /**
     * 待跟进数
     */
    private Long followedWorkOrderCnt;
    /**
     * 跟进人
     */
    private String followerId;
    /**
     * 游戏id
     */
    private String gameId;
    /**
     * 是否删除 0:否, 1:是
     */
    private Integer isDeleted;
    /**
     * 上架方式：1:官方截图，2:自主截图
     */
    private Integer onShelfType;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 更新人
     */
    private String updateUserId;
    /**
     * 待接单已完结数
     */
    private Long waitCompletedWorkOrderCnt;
    /**
     * 待接单已超时数
     */
    private Long waitTimeoutedWorkOrderCnt;
    /**
     * 待接单即将超时数
     */
    private Long waitTimeoutingWorkOrderCnt;
    /**
     * 待接单数
     */
    private Long waitWorkOrderCnt;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getAcceptedCompletedWorkOrderCnt() {
        return acceptedCompletedWorkOrderCnt;
    }

    public void setAcceptedCompletedWorkOrderCnt(Long acceptedCompletedWorkOrderCnt) {
        this.acceptedCompletedWorkOrderCnt = acceptedCompletedWorkOrderCnt;
    }

    public Long getAcceptedTerminatedWorkOrderCnt() {
        return acceptedTerminatedWorkOrderCnt;
    }

    public void setAcceptedTerminatedWorkOrderCnt(Long acceptedTerminatedWorkOrderCnt) {
        this.acceptedTerminatedWorkOrderCnt = acceptedTerminatedWorkOrderCnt;
    }

    public Long getAcceptedTimeoutedWorkOrderCnt() {
        return acceptedTimeoutedWorkOrderCnt;
    }

    public void setAcceptedTimeoutedWorkOrderCnt(Long acceptedTimeoutedWorkOrderCnt) {
        this.acceptedTimeoutedWorkOrderCnt = acceptedTimeoutedWorkOrderCnt;
    }

    public Long getAcceptedTimeoutingWorkOrderCnt() {
        return acceptedTimeoutingWorkOrderCnt;
    }

    public void setAcceptedTimeoutingWorkOrderCnt(Long acceptedTimeoutingWorkOrderCnt) {
        this.acceptedTimeoutingWorkOrderCnt = acceptedTimeoutingWorkOrderCnt;
    }

    public Long getAcceptedWorkOrderCnt() {
        return acceptedWorkOrderCnt;
    }

    public void setAcceptedWorkOrderCnt(Long acceptedWorkOrderCnt) {
        this.acceptedWorkOrderCnt = acceptedWorkOrderCnt;
    }

    public String getArtDesignerId() {
        return artDesignerId;
    }

    public void setArtDesignerId(String artDesignerId) {
        this.artDesignerId = artDesignerId;
    }

    public String getAuditUserId() {
        return auditUserId;
    }

    public void setAuditUserId(String auditUserId) {
        this.auditUserId = auditUserId;
    }

    public String getCreateDate() {
        return createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId;
    }

    public Long getFollowedCompletedWorkOrderCnt() {
        return followedCompletedWorkOrderCnt;
    }

    public void setFollowedCompletedWorkOrderCnt(Long followedCompletedWorkOrderCnt) {
        this.followedCompletedWorkOrderCnt = followedCompletedWorkOrderCnt;
    }

    public Long getFollowedTerminatedWorkOrderCnt() {
        return followedTerminatedWorkOrderCnt;
    }

    public void setFollowedTerminatedWorkOrderCnt(Long followedTerminatedWorkOrderCnt) {
        this.followedTerminatedWorkOrderCnt = followedTerminatedWorkOrderCnt;
    }

    public Long getFollowedTimeoutedWorkOrderCnt() {
        return followedTimeoutedWorkOrderCnt;
    }

    public void setFollowedTimeoutedWorkOrderCnt(Long followedTimeoutedWorkOrderCnt) {
        this.followedTimeoutedWorkOrderCnt = followedTimeoutedWorkOrderCnt;
    }

    public Long getFollowedTimeoutingWorkOrderCnt() {
        return followedTimeoutingWorkOrderCnt;
    }

    public void setFollowedTimeoutingWorkOrderCnt(Long followedTimeoutingWorkOrderCnt) {
        this.followedTimeoutingWorkOrderCnt = followedTimeoutingWorkOrderCnt;
    }

    public Long getFollowedWorkOrderCnt() {
        return followedWorkOrderCnt;
    }

    public void setFollowedWorkOrderCnt(Long followedWorkOrderCnt) {
        this.followedWorkOrderCnt = followedWorkOrderCnt;
    }

    public String getFollowerId() {
        return followerId;
    }

    public void setFollowerId(String followerId) {
        this.followerId = followerId;
    }

    public String getGameId() {
        return gameId;
    }

    public void setGameId(String gameId) {
        this.gameId = gameId;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Integer getOnShelfType() {
        return onShelfType;
    }

    public void setOnShelfType(Integer onShelfType) {
        this.onShelfType = onShelfType;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getUpdateUserId() {
        return updateUserId;
    }

    public void setUpdateUserId(String updateUserId) {
        this.updateUserId = updateUserId;
    }

    public Long getWaitCompletedWorkOrderCnt() {
        return waitCompletedWorkOrderCnt;
    }

    public void setWaitCompletedWorkOrderCnt(Long waitCompletedWorkOrderCnt) {
        this.waitCompletedWorkOrderCnt = waitCompletedWorkOrderCnt;
    }

    public Long getWaitTimeoutedWorkOrderCnt() {
        return waitTimeoutedWorkOrderCnt;
    }

    public void setWaitTimeoutedWorkOrderCnt(Long waitTimeoutedWorkOrderCnt) {
        this.waitTimeoutedWorkOrderCnt = waitTimeoutedWorkOrderCnt;
    }

    public Long getWaitTimeoutingWorkOrderCnt() {
        return waitTimeoutingWorkOrderCnt;
    }

    public void setWaitTimeoutingWorkOrderCnt(Long waitTimeoutingWorkOrderCnt) {
        this.waitTimeoutingWorkOrderCnt = waitTimeoutingWorkOrderCnt;
    }

    public Long getWaitWorkOrderCnt() {
        return waitWorkOrderCnt;
    }

    public void setWaitWorkOrderCnt(Long waitWorkOrderCnt) {
        this.waitWorkOrderCnt = waitWorkOrderCnt;
    }

}

