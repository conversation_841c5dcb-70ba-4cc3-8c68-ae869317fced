package com.pxb7.mall.workorder.infra.mapping;

import com.pxb7.mall.workorder.infra.model.RemindPlanRecordReqPO;
import com.pxb7.mall.workorder.infra.repository.db.entity.RemindPlanRecord;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/3 17:34
 */

@Mapper
public interface PlanRecordMapping {

    PlanRecordMapping INSTANCE = Mappers.getMapper(PlanRecordMapping.class);

    List<RemindPlanRecord> addDto2POList(List<RemindPlanRecordReqPO.AddPO> source);

    List<RemindPlanRecord> updateDto2POList(List<RemindPlanRecordReqPO.UpdatePO> source);

}
