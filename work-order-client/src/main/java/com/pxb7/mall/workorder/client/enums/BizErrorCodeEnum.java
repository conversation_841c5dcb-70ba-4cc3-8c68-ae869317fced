package com.pxb7.mall.workorder.client.enums;

import lombok.Getter;

/**
 * 业务错误码枚举
 *
 * <AUTHOR>
 */
@Getter
public enum BizErrorCodeEnum {

    SUCCESS("00000", "成功"), SIGN_ERROR("00001", "认证失败"),
    PARAM_ERROR("00008", "请求参数错误"),
    SYSTEM_ERROR("00009", "系统内部错误"),
    PARAM_STRING_BLANK_ERROR("00010", "参数不能为空!"),
    SYNC_LOCK_FAIL("00015", "请勿频繁操作"),

    // 其他域的错误
    MESSAGE_GET_ERROR("im70900", "接口获取为空"),
    USER_INFO_NOT_FOUND("00017", "获取用户信息失败"),

    // 预警计划相关
    REMIND_PLAN_NOT_FOUND("wo00001", "获取预警计划信息失败"),
    REMIND_RECORD_SAVE_ERROR("wo00002", "插入预警记录失败"),
    REMIND_PLAN_RECORD_SAVE_ERROR("wo00003", "插入预警执行计划记录失败"),

    REMIND_PLAN_RECORD_STATUS_UPDATE_ERROR("wo00004", "更新预警执行计划状态失败"),

    ;
    private final String errCode;
    private final String errDesc;

    BizErrorCodeEnum(String errCode, String errDesc) {
        this.errCode = errCode;
        this.errDesc = errDesc;
    }

}
