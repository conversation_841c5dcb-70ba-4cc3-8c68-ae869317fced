package com.pxb7.mall.workorder.adapter.consumer;

import com.alibaba.fastjson.JSON;
import com.pxb7.mall.components.rocketmq.RocketMQListenerExt;
import com.pxb7.mall.workorder.app.service.RemindAfterSaleAppService;
import com.pxb7.mall.workorder.app.service.RemindComplaintAppService;
import com.pxb7.mall.workorder.domain.message.AssWorkOrderMessage;
import com.pxb7.mall.workorder.infra.constant.RMQConstant;
import com.pxb7.mall.workorder.infra.enums.RemindPlanWorkOrderTypeEnum;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.annotation.RocketMQMessageListener;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 监听售后和投诉工单状态变更消息
 * <AUTHOR>
 */
@Slf4j
@Service
@RocketMQMessageListener(
        consumerGroup = RMQConstant.AFC_WORK_ORDER_GROUP,
        topic = RMQConstant.AFC_WORK_ORDER_TOPIC,
        tag = RMQConstant.AFC_WORK_ORDER_TAG)
public class AssWorkOrderMessageConsumer implements RocketMQListenerExt<AssWorkOrderMessage> {

    @Resource
    private RemindAfterSaleAppService afterSaleAppService;

    @Resource
    private RemindComplaintAppService complaintAppService;

    @Override
    public ConsumeResult consume(MessageView messageView, AssWorkOrderMessage workOrderMessage) {
        log.info("监听到售后工单状态变更消息: messageId = {},message = {}", messageView.getMessageId(), JSON.toJSONString(workOrderMessage));
        String workOrderId = workOrderMessage.getWorkOrderId();
        Integer workOrderType = workOrderMessage.getWorkOrderType();
        try {
            if (StringUtils.isBlank(workOrderId) || Objects.isNull(workOrderType)) {
                return ConsumeResult.SUCCESS;
            }
            if (RemindPlanWorkOrderTypeEnum.RETRIEVE.eq(workOrderType)
                    || RemindPlanWorkOrderTypeEnum.DISPUTE.eq(workOrderType)) {
                afterSaleAppService.dealAfterSaleWorkOrder(workOrderId, workOrderType);
            } else if (RemindPlanWorkOrderTypeEnum.COMPLAINT.eq(workOrderType)) {
                complaintAppService.dealComplaintWorkOrder(workOrderId);
            }
            return ConsumeResult.SUCCESS;
        } catch (Exception e) {
            log.error("error in consumed from T_afc_work_order_status_change: workOrderId = {}", workOrderId, e);
            return ConsumeResult.FAILURE;
        }
    }

}

