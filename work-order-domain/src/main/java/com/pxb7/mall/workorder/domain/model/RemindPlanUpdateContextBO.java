package com.pxb7.mall.workorder.domain.model;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.List;

@Getter
@Setter
@Accessors(chain = true)
@ToString
public class RemindPlanUpdateContextBO {

    private RemindPlanBO originRemindPlan;

    private RemindPlanBO remindPlan;

    private RemindPlanRuleRefreshBO remindPlanRuleRefresh;

    private RemindPlanGameConfigRefreshBO remindPlanGameConfigRefresh;

    private List<RemindPlanOperateRecordBO> operateRecords;
}
