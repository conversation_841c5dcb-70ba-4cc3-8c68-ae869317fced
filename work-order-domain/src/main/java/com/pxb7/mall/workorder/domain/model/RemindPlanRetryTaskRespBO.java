package com.pxb7.mall.workorder.domain.model;

import java.util.Date;
import java.time.*;
import java.math.BigDecimal;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.ToString;


/**
 * 生成提醒计划异常重试表(RemindPlanRetryTask)实体类
 *
 * <AUTHOR>
 * @since 2025-05-06 20:54:42
 */
public class RemindPlanRetryTaskRespBO {


    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class DetailBO {
        private Long id;
        private String bizId;
        private Integer serviceType;
        private String recordInfo;
        private Integer retryTimes;
        private Integer status;
        private LocalDateTime createTime;
        private LocalDateTime updateTime;
    }
}

