package com.pxb7.mall.workorder.domain.mapping;

import com.pxb7.mall.workorder.domain.model.RemindPlanRetryTaskReqBO;
import com.pxb7.mall.workorder.domain.model.RemindPlanRetryTaskRespBO;
import com.pxb7.mall.workorder.infra.model.RemindPlanRetryTaskReqPO;
import com.pxb7.mall.workorder.infra.repository.db.entity.RemindPlanRetryTask;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;


@Mapper
public interface RemindPlanRetryTaskDomainMapping {

    RemindPlanRetryTaskDomainMapping INSTANCE = Mappers.getMapper(RemindPlanRetryTaskDomainMapping.class);


    RemindPlanRetryTaskReqPO.AddPO remindPlanRetryTaskBO2AddPO(RemindPlanRetryTaskReqBO.AddBO source);

    List<RemindPlanRetryTaskReqPO.UpdatePO> remindPlanRetryTaskBO2UpdatePOList(List<RemindPlanRetryTaskReqBO.UpdateBO> source);

    RemindPlanRetryTaskReqPO.PagePO remindPlanRetryTaskBO2PagePO(RemindPlanRetryTaskReqBO.PageBO source);

    Page<RemindPlanRetryTaskRespBO.DetailBO> remindPlanRetryTaskPO2PageBO(Page<RemindPlanRetryTask> source);

}


