package com.pxb7.mall.workorder.infra.repository.es.entity;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;
import lombok.experimental.FieldNameConstants;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 还价工单索引
 *
 * <AUTHOR>
 * @version : BargainTicketDoc.java, v 0.1 2025年04月16日 16:52 yang.xuexi Exp $
 */
@Getter
@Setter
@Accessors(chain = true)
@ToString
@FieldNameConstants
@Document(indexName = "bargain_ticket")
public class BargainTicketDoc {
    /**
     * 工单id
     */
    @Id
    @Field(value = "receive_id", type = FieldType.Keyword)
    private String receiveId;

    /**
     * 买家id
     */
    @Field(value = "buyer_id", type = FieldType.Keyword)
    private String buyerId;

    /**
     * 买家手机号
     */
    @Field(value = "buyer_phone", type = FieldType.Keyword)
    private String buyerPhone;
    /**
     * 接单客服id
     */
    @Field(value = "customer_id", type = FieldType.Keyword)
    private String customerId;

    /**
     * 接单客服名称
     */
    @Field(value = "customer_name", type = FieldType.Text)
    private String customerName;

    /**
     * 还价数量
     */
    @Field(value = "bargain_num", type = FieldType.Integer)
    private Integer bargainNum;

    /**
     * 10:未成交 20:有成交
     */
    @Field(value = "deal_flag", type = FieldType.Integer)
    private Integer dealFlag;

    /**
     * 还价游戏列表信息
     */
    @Field(value = "game_info_list", type = FieldType.Nested)
    private List<GameInfo> gameInfoList;

    /**
     * 最低号价
     */
    @Field(value = "number_price_min", type = FieldType.Long)
    private Long numberPriceMin;

    /**
     * 最高号价
     */
    @Field(value = "number_price_max", type = FieldType.Long)
    private Long numberPriceMax;

    /**
     * 议价比例开始价（单位：分）
     */
    @Field(value = "bargaining_ratio_start", type = FieldType.Double)
    private Double bargainingRatioStart;

    /**
     * 议价比例结束价（单位：分）
     */
    @Field(value = "bargaining_ratio_end", type = FieldType.Double)
    private Double bargainingRatioEnd;

    /**
     * 工单状态( 0：不可接单 1：待接单 2：已接单 3：已完结')
     */
    @Field(value = "receive_status", type = FieldType.Integer)
    private Integer receiveStatus;

    /**
     * 创建时间
     */
    @Field(value = "create_time", type = FieldType.Date, format = DateFormat.date_hour_minute_second_fraction)
    private LocalDateTime createTime;

    /**
     * 创建时间
     */
    @Field(value = "update_time", type = FieldType.Date, format = DateFormat.date_hour_minute_second_fraction)
    private LocalDateTime updateTime;

    /**
     * 接单时间(yyyy-MM-dd)
     */
    @Field(value = "receive_time", type = FieldType.Date, format = DateFormat.date_hour_minute_second_fraction)
    private LocalDateTime receiveTime;

    /**
     * 接单日期
     */
    @Field(value = "receiveDate" ,type = FieldType.Keyword)
    private String receiveDate;

    /**
     * 10:未读 20:已读
     */
    @Field(value = "read_flag", type = FieldType.Integer)
    private Integer readFlag;


    /**
     * 还价信息列表
     */
    @Field(value = "bargain_info_list", type = FieldType.Nested)
    private List<BargainInfo> bargainInfoList;

    /**
     * 商品信息列表
     */
    @Field(value = "product_info_list", type = FieldType.Nested)
    private List<ProductInfo> productInfoList;


    /**
     * 游戏列表信息
     */
    @Setter
    @Getter
    @FieldNameConstants
    @Accessors(chain = true)
    public static class GameInfo {
        @Field(type = FieldType.Keyword, value = "game_id")
        private String gameId;
        @Field(type = FieldType.Text, value = "game_name")
        private String gameName;
    }

    @Setter
    @Getter
    @FieldNameConstants
    @Accessors(chain = true)
    public static class BargainInfo{
        /**
         * 还价单id
         */
        @Field(type = FieldType.Keyword, value = "bargain_id")
        private String bargainId;
        /**
         * 还价单状态
         */
        @Field(type = FieldType.Keyword, value = "bargain_status")
        private Integer bargainStatus;

        /**
         * 最终议价金额(分)(买家最新出价金额)
         */
        @Field(type = FieldType.Keyword, value = "bargain_price")
        private Long bargainPrice;

        /**
         * 比例
         */
        @Field(type = FieldType.Double, value = "bargain_ratio")
        private Double bargainRatio;
    }
    @Setter
    @Getter
    @FieldNameConstants
    @Accessors(chain = true)
    public static class ProductInfo{
        /**
         * 商品唯一编号
         */
        @Field(type = FieldType.Keyword, value = "product_unique_no")
        private String productUniqueNo;
        /**
         * 商品名称
         */
        @Field(type = FieldType.Text, value = "product_name")
        private String productName;

        /**
         * 商品价格
         */
        @Field(value = "number_price", type = FieldType.Long)
        private Long numberPrice;

    }
}
