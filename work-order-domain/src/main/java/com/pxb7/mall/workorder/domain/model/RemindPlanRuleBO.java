package com.pxb7.mall.workorder.domain.model;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.List;

@Getter
@Setter
@Accessors(chain = true)
@ToString
public class RemindPlanRuleBO {


    private Long id;

    private Long remindPlanId;
    /**
     * 节点序列：
     * 11:完结时间之前第1次 12:完结时间之前第2次 13:完结时间之前第3次
     * 21:完结时间之后第1次 22:完结时间之后第2次 23:完结时间之后第3次
     */
    private Integer nodeNumber;

    /**
     * 提醒时间点,{"hours":"10","minutes":"30"}
     */
    private TimeConfigBO remindTimeConfig;

    /**
     * 提醒方式配置
     */
    private List<RemindMethodConfigBO> remindMethodConfig;

    private String createUserId;


    private String updateUserId;
}
