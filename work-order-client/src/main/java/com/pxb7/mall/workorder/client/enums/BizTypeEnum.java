package com.pxb7.mall.workorder.client.enums;

import lombok.Getter;
import lombok.ToString;

/**
 * 预警业务类型
 * <AUTHOR>
 */
@Getter
@ToString
public enum BizTypeEnum {

    /**
     * 预警业务类型
     */
    DELIVERY_PRODUCT(1, Constants.DELIVERY_PRODUCT, "账号交付"),
    WORK_ORDER(2, Constants.WORK_ORDER, "商品工单"),
    AFTER_SALE(3, Constants.AFTER_SALE, "售后工单"),
    COMPLAINT(4, Constants.COMPLAINT, "客诉工单");

    private final Integer type;
    private final String code;
    private final String desc;

    BizTypeEnum(Integer type, String code, String desc) {
        this.type = type;
        this.code = code;
        this.desc = desc;
    }

    public static BizTypeEnum getByType(Integer type) {
        for (BizTypeEnum value : BizTypeEnum.values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        return null;
    }

    public static class Constants {
        public static final String DELIVERY_PRODUCT = "delivery_product";
        public static final String WORK_ORDER = "work_order";
        public static final String AFTER_SALE = "after_sale";
        public static final String COMPLAINT = "complaint";

    }

}
