package com.pxb7.mall.workorder.domain.model;

import java.util.Date;
import java.time.*;
import java.math.BigDecimal;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.ToString;


/**
 * 预警计划操作记录表(RemindPlanOperateRecord)实体类
 *
 * <AUTHOR>
 * @since 2025-03-31 20:36:47
 */
public class RemindPlanOperateRecordRespBO {


    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class DetailBO {
        private Long id;
        private String remindPlanId;
        private Integer optType;
        private Integer dataType;
        private String originContent;
        private String newContent;
        private String traceId;
        private String optUserId;
        private LocalDateTime createTime;
        private LocalDateTime updateTime;
        private Boolean deleted;
    }
}

