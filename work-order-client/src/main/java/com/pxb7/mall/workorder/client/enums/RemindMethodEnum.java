package com.pxb7.mall.workorder.client.enums;

import lombok.Getter;
import lombok.ToString;

/**
 * 提醒方式，触达渠道
 * <AUTHOR>
 */
@Getter
@ToString
public enum RemindMethodEnum {

    /**
     * 提醒方式，触达渠道
     */
    FEISHU(Constants.FEISHU, "飞书"),
    IM(Constants.IM, "im渠道"),
    BACKEND_MANAGE(Constants.BACKEND, "管理后台");

    private final String code;
    private final String desc;

    RemindMethodEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static RemindMethodEnum getByCode(String code) {
        for (RemindMethodEnum value : RemindMethodEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static class Constants {
        public static final String FEISHU = "feishu";
        public static final String IM = "im";
        public static final String BACKEND = "backend";

    }

}
