package com.pxb7.mall.workorder.infra.repository.db.entity;

import java.io.Serializable;
import java.util.Date;
import java.time.*;
import java.math.BigDecimal;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import com.baomidou.mybatisplus.annotation.*;

/**
 * 预警执行计划记录(RemindPlanRecord)实体类
 *
 * <AUTHOR>
 * @since 2025-04-12 14:12:59
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "remind_plan_record")
@ToString
public class RemindPlanRecord implements Serializable {
    private static final long serialVersionUID = 362898982464263175L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 预警计划id
     */
    @TableField(value = "plan_record_id")
    private String planRecordId;
    /**
     * 预警业务类型，1:账号交付，2:商品工单
     */
    @TableField(value = "biz_type")
    private Integer bizType;
    /**
     * 预警业务数据id（order_item_id或work_order_id）
     */
    @TableField(value = "biz_id")
    private String bizId;
    /**
     * 预警记录id（remind_delivery_product或remind_work_order）
     */
    @TableField(value = "remind_id")
    private String remindId;
    /**
     * 第几次提醒
     */
    @TableField(value = "which_time")
    private Integer whichTime;
    /**
     * 是否是超时前最后一条预警 0:否, 1:是
     */
    @TableField(value = "is_last_before_timeout")
    private Boolean lastBeforeTimeout;
    /**
     * 执行状态，0:待执行，1:执行中，2:已执行，3:执行失败，4:已过期，5:已失效，6:防打扰失效
     */
    @TableField(value = "status")
    private Integer status;
    /**
     * 提醒时间点
     */
    @TableField(value = "remind_time")
    private LocalDateTime remindTime;
    /**
     * 预警计划规则配置id,remind_plan_rule
     */
    @TableField(value = "plan_rule_id")
    private Long planRuleId;
    /**
     * 预警计划id,remind_plan
     */
    @TableField(value = "remind_plan_id")
    private Long remindPlanId;
    /**
     * 预警子计划id,remind_sub_plan
     */
    @TableField(value = "remind_sub_plan_id")
    private Long remindSubPlanId;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;
    /**
     * 创建人
     */
    @TableField(value = "create_user_id")
    private String createUserId;
    /**
     * 更新人
     */
    @TableField(value = "update_user_id")
    private String updateUserId;
    /**
     * 是否删除 0:否, 1:是
     */
    @TableLogic
    @TableField(value = "is_deleted")
    private Boolean deleted;

    /**
      * 环境变量
      */
     @TableField(value = "env_profile", fill = FieldFill.INSERT)
     private Integer envProfile;
}

