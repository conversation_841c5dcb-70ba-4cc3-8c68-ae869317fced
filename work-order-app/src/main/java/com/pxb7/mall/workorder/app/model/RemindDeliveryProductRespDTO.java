package com.pxb7.mall.workorder.app.model;

import java.util.Date;
import java.time.*;
import java.math.BigDecimal;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.ToString;


/**
 * 账号交付预警记录(RemindDeliveryProduct)实体类
 *
 * <AUTHOR>
 * @since 2025-03-27 20:08:52
 */
public class RemindDeliveryProductRespDTO {


    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class DetailDTO {
        private Long id;
        private String remindId;
        private String productId;
        private String productCode;
        private String gameId;
        private String groupId;
        private String orderId;
        private String orderItemId;
        private Integer completeStatus;
        private Integer timeOutStatus;
        private String deliveryCustomerCare;
        private LocalDateTime expectCompleteTime;
        private LocalDateTime imCountDownTime;
        private Long gameConfigId;
        private Long remindPlanId;
        private Long remindSubPlanId;
    }
}

