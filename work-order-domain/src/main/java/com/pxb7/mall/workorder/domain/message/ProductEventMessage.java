package com.pxb7.mall.workorder.domain.message;

import com.pxb7.mall.product.client.enums.OptLogTypeEnum;
import com.pxb7.mall.product.client.enums.OptTypeEnum;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ProductEventMessage implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 业务主键
     */
    private String logId;
    /**
     * 商品id
     */
    private String productId;
    /**
     * 工单id
     */
    private String workOrderId;
    private String  sellerCommentId;
    /**
     * 游戏id
     */
    private String gameId;
    /**
     * 游戏名称
     */
    private String gameName;
    /**
     * 商品修改前价格
     */
    private Long beforePrice;
    /**
     * 商品修改后价格
     */
    private Long afterPrice;
    /**
     * 操作类型
     * @see OptTypeEnum
     */
    private Integer optType;
    /**
     * 操作人
     */
    private String optUser;
    /**
     * 操作时间
     */
    private LocalDateTime optTime;
    /**
     * 商品事件
     */
    private String productEventTag;

    /**
     * 截图任务id
     */
    private String taskId;

    /**
     * 接收到的参数
     */
    private String params;

    /**
     * @see OptLogTypeEnum
     */
    private Integer optLogType;
    /**
     * @see com.pxb7.mall.product.client.enums.OptLogMsgTagEnum
     */
    private String optLogMsgTag;

    /**
     * @see com.pxb7.mall.product.client.enums.OptLogSourceEnum
     * 可能有多个
     */
    private List<Integer> optLogSource;


}
