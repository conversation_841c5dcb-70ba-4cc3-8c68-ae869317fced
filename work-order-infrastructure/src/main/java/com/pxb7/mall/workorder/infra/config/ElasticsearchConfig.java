package com.pxb7.mall.workorder.infra.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.elasticsearch.config.AbstractElasticsearchConfiguration;
import org.springframework.data.elasticsearch.core.ElasticsearchOperations;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.convert.ElasticsearchConverter;
import org.springframework.data.elasticsearch.core.convert.MappingElasticsearchConverter;
import org.springframework.data.elasticsearch.core.mapping.SimpleElasticsearchMappingContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;

/**
 * Elasticsearch配置类
 * 用于处理Elasticsearch连接和映射配置
 * 
 * <AUTHOR>
 */
@Slf4j
@Configuration
public class ElasticsearchConfig extends AbstractElasticsearchConfiguration {

    @Override
    public org.elasticsearch.client.RestHighLevelClient elasticsearchClient() {
        // 这里可以自定义客户端配置
        return super.elasticsearchClient();
    }

    /**
     * 自定义Elasticsearch转换器，处理字段映射
     */
    @Bean
    @Primary
    public ElasticsearchConverter elasticsearchConverter() {
        MappingElasticsearchConverter converter = new MappingElasticsearchConverter(
            new SimpleElasticsearchMappingContext()
        );
        
        // 设置类型映射策略
        converter.setConversions(elasticsearchCustomConversions());
        
        return converter;
    }

    /**
     * 自定义操作模板，增加错误处理
     */
    @Bean
    @Primary
    public ElasticsearchOperations elasticsearchOperations() {
        ElasticsearchRestTemplate template = new ElasticsearchRestTemplate(
            elasticsearchClient(), 
            elasticsearchConverter()
        );
        
        // 可以在这里添加自定义的错误处理逻辑
        return template;
    }
}
