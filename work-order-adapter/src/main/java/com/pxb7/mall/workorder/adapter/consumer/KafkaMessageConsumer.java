package com.pxb7.mall.workorder.adapter.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.pxb7.mall.workorder.app.service.DbRecordSyncAppService;
import com.pxb7.mall.workorder.client.dto.DbRecordSyncDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;


@Slf4j
@Component
public class KafkaMessageConsumer {

    public static final String CREATE = "INSERT";
    public static final String UPDATE = "UPDATE";
    public static final String DELETE = "DELETE";
    public static final String INIT = "INIT";


    @Resource
    private DbRecordSyncAppService dbRecordSyncAppService;


    public String[] getTopics() {
        return new String[] {"work-order-sync"};
    }

    public String getGroupId() {
        return "work_order_dts_consumer_group";
    }



    @KafkaListener(topics = "#{__listener.getTopics()}", groupId = "#{__listener.getGroupId()}", containerFactory = "batchKafkaListenerContainerFactory", concurrency = "8")
    public void batchListener(List<String> messages) throws IOException {
        for (String message : messages) {
            DbRecordSyncDTO dbRecordSyncDTO = extractDbRecordSyncDTO(message, "remind_id");
            if (null == dbRecordSyncDTO) {
                continue;
            }
            dbRecordSyncAppService.syncDbRecord(dbRecordSyncDTO);
        }
    }

    private DbRecordSyncDTO extractDbRecordSyncDTO(String message,String keyField) {
        try {
            // 解析 JSON 消息
            JSONObject jsonObject = JSON.parseObject(message);
            if (jsonObject == null ||StringUtils.isBlank(keyField)) {
                return null;
            }

            String database = jsonObject.getString("database");
            String table = jsonObject.getString("table");
            String operateType = jsonObject.getString("type");
            log.info(String.format("db:%s.%s", database, table));

            List<String> operateKeys = Lists.newArrayList();
            JSONArray dataList = jsonObject.getJSONArray("data");
            for (int j = 0; j < dataList.size(); j++) {
                JSONObject data = dataList.getJSONObject(j);
                if (!data.containsKey(keyField)) {
                    log.warn("");
                    continue;
                }
                String remindId = data.getString(keyField);
                if (StringUtils.isBlank(remindId)){
                    continue;
                }
                operateKeys.add(remindId);
            }
            if (CollectionUtils.isEmpty(operateKeys)) {
                return null;
            }

            DbRecordSyncDTO dbRecordSyncDTO = new DbRecordSyncDTO();
            dbRecordSyncDTO.setTableName(table);

            dbRecordSyncDTO.setOperateKeys(operateKeys);
            dbRecordSyncDTO.setSendTime(LocalDateTime.now());
            dbRecordSyncDTO.setOperateType(operateType);
            return dbRecordSyncDTO;

        } catch (Exception e) {
            log.error("extractDbRecordSyncDTO error:", e);
            return null;
        }
    }
}
