package com.pxb7.mall.workorder.infra.mapping;

import com.pxb7.mall.workorder.infra.model.RemindComplaintReqPO;
import com.pxb7.mall.workorder.infra.repository.db.entity.RemindComplaint;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2025/4/3 17:34
 */

@Mapper
public interface ComplaintMapping {

    ComplaintMapping INSTANCE = Mappers.getMapper(ComplaintMapping.class);

    RemindComplaint addDto2PO(RemindComplaintReqPO.AddPO source);

}
