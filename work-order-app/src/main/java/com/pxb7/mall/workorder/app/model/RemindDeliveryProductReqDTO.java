package com.pxb7.mall.workorder.app.model;

import java.util.Date;
import java.time.*;
import java.math.BigDecimal;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Min;
import lombok.ToString;


/**
 * 账号交付预警记录(RemindDeliveryProduct)实体类
 *
 * <AUTHOR>
 * @since 2025-03-27 20:08:50
 */
public class RemindDeliveryProductReqDTO {

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class AddDTO {


        @NotBlank(message = "remindId不能为空")
        private String remindId;


        @NotBlank(message = "productId不能为空")
        private String productId;


        @NotBlank(message = "productCode不能为空")
        private String productCode;


        @NotBlank(message = "gameId不能为空")
        private String gameId;


        @NotBlank(message = "groupId不能为空")
        private String groupId;


        @NotBlank(message = "orderId不能为空")
        private String orderId;


        @NotBlank(message = "orderItemId不能为空")
        private String orderItemId;


        @NotNull(message = "completeStatus不能为空")
        private Integer completeStatus;


        @NotNull(message = "timeOutStatus不能为空")
        private Integer timeOutStatus;


        @NotBlank(message = "deliveryCustomerCare不能为空")
        private String deliveryCustomerCare;


        @NotNull(message = "expectCompleteTime不能为空")
        private LocalDateTime expectCompleteTime;


        @NotNull(message = "imCountDownTime不能为空")
        private LocalDateTime imCountDownTime;


        @NotNull(message = "gameConfigId不能为空")
        private Long gameConfigId;


        @NotNull(message = "remindPlanId不能为空")
        private Long remindPlanId;


        @NotNull(message = "remindSubPlanId不能为空")
        private Long remindSubPlanId;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class UpdateDTO {


        @NotNull(message = "id不能为空")
        private Long id;


        @NotBlank(message = "remindId不能为空")
        private String remindId;


        @NotBlank(message = "productId不能为空")
        private String productId;


        @NotBlank(message = "productCode不能为空")
        private String productCode;


        @NotBlank(message = "gameId不能为空")
        private String gameId;


        @NotBlank(message = "groupId不能为空")
        private String groupId;


        @NotBlank(message = "orderId不能为空")
        private String orderId;


        @NotBlank(message = "orderItemId不能为空")
        private String orderItemId;


        @NotNull(message = "completeStatus不能为空")
        private Integer completeStatus;


        @NotNull(message = "timeOutStatus不能为空")
        private Integer timeOutStatus;


        @NotBlank(message = "deliveryCustomerCare不能为空")
        private String deliveryCustomerCare;


        @NotNull(message = "expectCompleteTime不能为空")
        private LocalDateTime expectCompleteTime;


        @NotNull(message = "imCountDownTime不能为空")
        private LocalDateTime imCountDownTime;


        @NotNull(message = "gameConfigId不能为空")
        private Long gameConfigId;


        @NotNull(message = "remindPlanId不能为空")
        private Long remindPlanId;


        @NotNull(message = "remindSubPlanId不能为空")
        private Long remindSubPlanId;


    }


    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class DelDTO {
        @NotNull(message = "id不能为空")
        private Long id;
    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class SearchDTO {

        @NotBlank(message = "remindId不能为空")
        private String remindId;


        @NotBlank(message = "productId不能为空")
        private String productId;


        @NotBlank(message = "productCode不能为空")
        private String productCode;


        @NotBlank(message = "gameId不能为空")
        private String gameId;


        @NotBlank(message = "groupId不能为空")
        private String groupId;


        @NotBlank(message = "orderId不能为空")
        private String orderId;


        @NotBlank(message = "orderItemId不能为空")
        private String orderItemId;


        @NotNull(message = "completeStatus不能为空")
        private Integer completeStatus;


        @NotNull(message = "timeOutStatus不能为空")
        private Integer timeOutStatus;


        @NotBlank(message = "deliveryCustomerCare不能为空")
        private String deliveryCustomerCare;


        @NotNull(message = "expectCompleteTime不能为空")
        private LocalDateTime expectCompleteTime;


        @NotNull(message = "imCountDownTime不能为空")
        private LocalDateTime imCountDownTime;


        @NotNull(message = "gameConfigId不能为空")
        private Long gameConfigId;


        @NotNull(message = "remindPlanId不能为空")
        private Long remindPlanId;


        @NotNull(message = "remindSubPlanId不能为空")
        private Long remindSubPlanId;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class PageDTO {


        /**
         * 商品id
         */
        private String productId;

        /**
         * 商品编码
         */
        private String productCode;

        /**
         * 订单号
         */
        private String orderId;

        /**
         * 订单明细号
         */
        private String orderItemId;

        /**
         * 群组ID （房间ID）
         */
        private String groupId;

        /**
         * 页码，从1开始
         */
        @NotNull(message = "页码不能为空")
        @Min(value = 1, message = "页码不能小于1")
        private Integer pageIndex;
        /**
         * 每页数量
         */
        @NotNull(message = "每页数量不能为空")
        @Min(value = 1, message = "每页数量不能小于1")
        private Integer pageSize;

    }

}

