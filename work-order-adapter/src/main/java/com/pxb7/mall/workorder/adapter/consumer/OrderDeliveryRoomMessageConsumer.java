package com.pxb7.mall.workorder.adapter.consumer;

import com.alibaba.fastjson.JSON;
import com.pxb7.mall.components.rocketmq.RocketMQListenerExt;
import com.pxb7.mall.trade.order.client.dto.model.OrderDeliveryRoomChangeMessage;
import com.pxb7.mall.workorder.app.service.RemindDeliveryProductAppService;
import com.pxb7.mall.workorder.infra.constant.RMQConstant;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.annotation.RocketMQMessageListener;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.springframework.stereotype.Service;

/**
 * 监听订单绑定交付房间变更消息(换绑房间场景)
 * <AUTHOR>
 */
@Slf4j
@Service
@RocketMQMessageListener(
        consumerGroup = RMQConstant.ORDER_DELIVERY_ROOM_CHANGE_GROUP,
        topic = RMQConstant.ORDER_CENTER_TOPIC,
        tag = RMQConstant.ORDER_DELIVERY_ROOM_CHANGE_TAG)
public class OrderDeliveryRoomMessageConsumer implements RocketMQListenerExt<OrderDeliveryRoomChangeMessage> {

    @Resource
    private RemindDeliveryProductAppService deliveryProductAppService;

    @Override
    public ConsumeResult consume(MessageView messageView, OrderDeliveryRoomChangeMessage deliveryRoomChangeMessage) {
        log.info("监听到订单绑定交付房间变更消息: messageId = {},message = {}",
                messageView.getMessageId(), JSON.toJSONString(deliveryRoomChangeMessage));
        String orderItemId = deliveryRoomChangeMessage.getOrderItemId();
        try {
            if (StringUtils.isBlank(orderItemId)) {
                return ConsumeResult.SUCCESS;
            }
            //换绑交付房间和交付客服
            deliveryProductAppService.updateGroupAndCustomerCare(orderItemId);
            return ConsumeResult.SUCCESS;
        } catch (Exception e) {
            log.error("error in consumed from order_center_topic: orderItemId = {}", orderItemId, e);
            return ConsumeResult.FAILURE;
        }
    }

}

