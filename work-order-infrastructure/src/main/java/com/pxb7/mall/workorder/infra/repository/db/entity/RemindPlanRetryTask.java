package com.pxb7.mall.workorder.infra.repository.db.entity;

import java.io.Serializable;
import java.util.Date;
import java.time.*;
import java.math.BigDecimal;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import com.baomidou.mybatisplus.annotation.*;

/**
 * 生成提醒计划异常重试表(RemindPlanRetryTask)实体类
 *
 * <AUTHOR>
 * @since 2025-05-06 20:53:52
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "remind_plan_retry_task")
@ToString
public class RemindPlanRetryTask implements Serializable {
    private static final long serialVersionUID = 732666287794562366L;
    /**
     * 主键ID，自增
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 预警业务数据id（order_item_id或work_order_id）
     */
    @TableField(value = "biz_id")
    private String bizId;
    /**
     * 服务类型，1:账号交付，2:商品工单，3:售后工单，4:客诉工单
     */
    @TableField(value = "service_type")
    private Integer serviceType;
    /**
     * 记录消息
     */
    @TableField(value = "record_info")
    private String recordInfo;
    /**
     * 已重试次数
     */
    @TableField(value = "retry_times")
    private Integer retryTimes;
    /**
     * 状态 0:待处理, 1:处理成功，2:处理失败
     */
    @TableField(value = "status")
    private Integer status;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    /**
      * 环境变量
      */
     @TableField(value = "env_profile", fill = FieldFill.INSERT)
     private Integer envProfile;

}

