package com.pxb7.mall.workorder.app.mapping;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.pxb7.mall.user.dto.response.user.UserShortInfoDTO;
import com.pxb7.mall.workorder.app.model.BargainTicketDetailDTO;
import com.pxb7.mall.workorder.app.model.BargainTicketPageDetailDTO;
import com.pxb7.mall.workorder.app.model.BargainTicketReqDTO;
import com.pxb7.mall.workorder.client.enums.BargainTicketReadFlagEnum;
import com.pxb7.mall.workorder.client.enums.BargainTicketTradeStatusFlagEnum;
import com.pxb7.mall.workorder.domain.message.BargainTicketMessage;
import com.pxb7.mall.workorder.domain.model.BargainTicketBO;
import com.pxb7.mall.workorder.domain.model.BargainTicketReqBO;
import com.pxb7.mall.workorder.infra.repository.db.entity.BargainAcceptanceCustomer;
import com.pxb7.mall.workorder.infra.repository.es.entity.BargainTicketDoc;
import org.apache.commons.lang3.math.NumberUtils;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;
import org.springframework.beans.BeanUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


@Mapper
public interface BargainTicketAppMapping {

    BargainTicketAppMapping INSTANCE = Mappers.getMapper(BargainTicketAppMapping.class);

    BargainTicketReqBO.PageBO convert2Bo(BargainTicketReqDTO.PageDTO source);

    List<BargainTicketPageDetailDTO> convertList(List<BargainTicketBO> records);

    BargainTicketDetailDTO convertTicketBo2DetailDto(BargainTicketBO bargainTicketBO);

    List<BargainTicketDetailDTO> convertTicketBo2DetailDtoList(List<BargainTicketBO> records);


    @AfterMapping
    default void afterSetMapping(@MappingTarget BargainTicketDetailDTO ticketDetailDTO, BargainTicketBO bo) {
        if (CollUtil.isNotEmpty(bo.getGameInfoList())) {
            List<String> list = bo.getGameInfoList().stream().map(BargainTicketBO.GameInfo::getGameName).filter(StrUtil::isNotBlank).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(list)) {
                ticketDetailDTO.setGameNameStr(Joiner.on("、").join(list));
            }
        } else {
            ticketDetailDTO.setGameNameStr("");
        }
        if (Objects.nonNull(bo.getReceiveTime()) && bo.getReceiveTime().getYear() < 2000) {
            ticketDetailDTO.setReceiveTime(null);
        }
    }

    default BargainTicketDoc convertEventMessageToInitDoc(BargainTicketMessage message, Optional<UserShortInfoDTO> userInfoOptional, BargainAcceptanceCustomer customer, boolean dealSuccess) {
        BargainTicketDoc doc = new BargainTicketDoc();
        doc.setReceiveId(message.getReceiveId());
        doc.setBuyerId(customer.getBuyerUserId());
        if (Objects.nonNull(customer.getReceiveTime()) && customer.getReceiveTime().getYear() > 2000) {
            doc.setReceiveTime(customer.getReceiveTime());
        }
        doc.setReceiveDate(customer.getReceiveDate());
        //工单状态
        doc.setReceiveStatus(customer.getReceiveStatus());
        if (Objects.nonNull(customer.getReceiveDate())) {
            doc.setReceiveDate(customer.getReceiveDate());
        }

        //还价信息列表
        if (Objects.nonNull(message.getBargainId())) {

            BargainTicketDoc.BargainInfo bargainInfo = new BargainTicketDoc.BargainInfo()
                    .setBargainId(message.getBargainId())
                    .setBargainStatus(message.getRecordStatus());
            if (ObjectUtil.equal(1, message.getRecordStatus()) || ObjectUtil.equal(5, message.getRecordStatus())) {
                bargainInfo.setBargainPrice(message.getBargainPrice())
                        .setBargainRatio(message.getBargainRatio());
                //比例
                doc.setBargainingRatioStart(message.getBargainRatio());
                doc.setBargainingRatioEnd(message.getBargainRatio());
            }
            doc.setBargainInfoList(Lists.newArrayList(bargainInfo));

        }
        doc.setCustomerId(customer.getCustomerId());
        doc.setCustomerName(customer.getCustomerName());
        doc.setCreateTime(customer.getCreateTime());
        doc.setUpdateTime(ObjectUtil.defaultIfNull(message.getBargainTime(), LocalDateTime.now()));
        //游戏信息
        if (StrUtil.isNotBlank(message.getGameId())) {
            BargainTicketDoc.GameInfo gameInfo = new BargainTicketDoc.GameInfo()
                    .setGameId(message.getGameId())
                    .setGameName(message.getGameName());
            if (CollUtil.isNotEmpty(doc.getGameInfoList())) {
                doc.getGameInfoList().add(gameInfo);
                List<BargainTicketDoc.GameInfo> values = doc.getGameInfoList().stream()
                        .collect(Collectors.toMap(BargainTicketDoc.GameInfo::getGameId, Function.identity(), (a, b) -> a))
                        .values().stream().toList();
                doc.setGameInfoList(values);
            } else {
                doc.setGameInfoList(Lists.newArrayList(gameInfo));
            }
        }
        //商品信息
        if (StrUtil.isNotBlank(message.getProductUniqueNo())) {
            if (CollUtil.isEmpty(doc.getProductInfoList())) {
                doc.setProductInfoList(Lists.newArrayList(new BargainTicketDoc.ProductInfo()
                                .setProductName(message.getProductName())
                                .setNumberPrice(message.getPrice())
                                .setProductUniqueNo(message.getProductUniqueNo())
                        )
                );
            } else {
                Map<String, BargainTicketDoc.ProductInfo> productInfoMap = doc.getProductInfoList()
                        .stream()
                        .collect(Collectors.toMap(BargainTicketDoc.ProductInfo::getProductUniqueNo, Function.identity(), (a, b) -> a));
                if (productInfoMap.containsKey(message.getProductUniqueNo())) {
                    BargainTicketDoc.ProductInfo productInfo = productInfoMap.get(message.getProductUniqueNo());
                    productInfo.setProductUniqueNo(message.getProductUniqueNo());
                    productInfo.setNumberPrice(message.getPrice());
                    productInfo.setProductName(message.getProductName());
                } else {
                    doc.getProductInfoList().add(new BargainTicketDoc.ProductInfo()
                            .setProductName(message.getProductName())
                            .setNumberPrice(message.getPrice())
                            .setProductUniqueNo(message.getProductUniqueNo()));
                }
            }


        }
        if (CollUtil.isNotEmpty(doc.getProductInfoList())) {
            doc.getProductInfoList().stream()
                    .filter(productInfo -> Objects.nonNull(productInfo.getNumberPrice()))
                    .max(Comparator.comparing(BargainTicketDoc.ProductInfo::getNumberPrice))
                    .ifPresent(s -> {
                        doc.setNumberPriceMax(s.getNumberPrice());
                    });

            doc.getProductInfoList().stream()
                    .min(Comparator.comparing(BargainTicketDoc.ProductInfo::getNumberPrice))
                    .ifPresent(s -> {
                        doc.setNumberPriceMin(s.getNumberPrice());
                    });
        }

        //已读未读
        doc.setReadFlag(ObjectUtil.defaultIfNull(message.getReadFlag(), BargainTicketReadFlagEnum.UN_READ.getCode()));

        //买家信息
        userInfoOptional.ifPresent(user -> {
            doc.setBuyerPhone(user.getPhone());
        });

        //是否有成交
        if (dealSuccess) {
            doc.setDealFlag(BargainTicketTradeStatusFlagEnum.DEAL.getCode());
        } else {
            doc.setDealFlag(BargainTicketTradeStatusFlagEnum.NONE.getCode());
        }

        //还价数量
        doc.setBargainNum(CollUtil.isEmpty(doc.getBargainInfoList()) ? NumberUtils.INTEGER_ZERO : doc.getBargainInfoList().size());

        return doc;
    }


    default BargainTicketDoc convertEventMessageToUpdateDoc(BargainTicketMessage message, Optional<UserShortInfoDTO> userInfoOptional, BargainAcceptanceCustomer customer, boolean dealSuccess, BargainTicketDoc originTicket) {
        BargainTicketDoc doc = new BargainTicketDoc();
        BeanUtils.copyProperties(originTicket, doc);

        doc.setReceiveId(message.getReceiveId());
        doc.setBuyerId(customer.getBuyerUserId());
        if (Objects.nonNull(customer.getReceiveTime()) && customer.getReceiveTime().getYear() > 2000) {
            doc.setReceiveTime(customer.getReceiveTime());
        }
        doc.setReceiveDate(customer.getReceiveDate());
        //工单状态
        doc.setReceiveStatus(customer.getReceiveStatus());
        if (Objects.nonNull(customer.getReceiveDate())) {
            doc.setReceiveDate(customer.getReceiveDate());
        }

        //还价信息列表
        if (Objects.nonNull(message.getBargainId())) {

            if (CollUtil.isNotEmpty(doc.getBargainInfoList())) {

                Map<String, BargainTicketDoc.BargainInfo> bargainInfoMap = doc.getBargainInfoList()
                        .stream()
                        .collect(Collectors.toMap(BargainTicketDoc.BargainInfo::getBargainId, Function.identity(), (a, b) -> a));
                if (bargainInfoMap.containsKey(message.getBargainId())) {
                    BargainTicketDoc.BargainInfo bargainInfo = bargainInfoMap.get(message.getBargainId());
                    bargainInfo.setBargainStatus(message.getRecordStatus());
                    if (ObjectUtil.equal(1, message.getRecordStatus()) || ObjectUtil.equal(5, message.getRecordStatus())) {
                        bargainInfo.setBargainPrice(message.getBargainPrice())
                                .setBargainRatio(message.getBargainRatio());

                    }

                } else {
                    BargainTicketDoc.BargainInfo info = new BargainTicketDoc.BargainInfo();
                    info.setBargainStatus(message.getRecordStatus())
                            .setBargainId(message.getBargainId());
                    if (ObjectUtil.equal(1, message.getRecordStatus()) || ObjectUtil.equal(5, message.getRecordStatus())) {
                        info.setBargainPrice(message.getBargainPrice())
                                .setBargainRatio(message.getBargainRatio());
                    }
                    doc.getBargainInfoList().add(info);
                }

                if (CollUtil.isNotEmpty(doc.getBargainInfoList())) {
                    doc.getBargainInfoList().stream().map(BargainTicketDoc.BargainInfo::getBargainRatio)
                            .filter(Objects::nonNull).max(Comparator.comparing(Double::doubleValue)).ifPresent(doc::setBargainingRatioEnd);
                    doc.getBargainInfoList().stream().map(BargainTicketDoc.BargainInfo::getBargainRatio)
                            .filter(Objects::nonNull).min(Comparator.comparing(Double::doubleValue)).ifPresent(doc::setBargainingRatioStart);
                }


            } else {

                BargainTicketDoc.BargainInfo bargainInfo = new BargainTicketDoc.BargainInfo()
                        .setBargainId(message.getBargainId())
                        .setBargainStatus(message.getRecordStatus());
                if (ObjectUtil.equal(1, message.getRecordStatus()) || ObjectUtil.equal(5, message.getRecordStatus())) {
                    bargainInfo.setBargainPrice(message.getBargainPrice())
                            .setBargainRatio(message.getBargainRatio());
                    //比例
                    doc.setBargainingRatioStart(message.getBargainRatio());
                    doc.setBargainingRatioEnd(message.getBargainRatio());
                }
                doc.setBargainInfoList(Lists.newArrayList(bargainInfo));
            }

            if (CollUtil.isNotEmpty(doc.getBargainInfoList())) {
                doc.getBargainInfoList()
                        .stream()
                        .filter(b -> Objects.nonNull(b.getBargainRatio()))
                        .max(Comparator.comparing(BargainTicketDoc.BargainInfo::getBargainRatio))
                        .ifPresent(s -> {
                            doc.setBargainingRatioEnd(s.getBargainRatio());
                        });
                doc.getBargainInfoList().stream()
                        .filter(b -> Objects.nonNull(b.getBargainRatio()))
                        .min(Comparator.comparing(BargainTicketDoc.BargainInfo::getBargainRatio))
                        .ifPresent(s -> {
                            doc.setBargainingRatioStart(s.getBargainRatio());
                        });
            }

        }
        doc.setCustomerId(customer.getCustomerId());
        doc.setCustomerName(customer.getCustomerName());
        doc.setCreateTime(customer.getCreateTime());
        if (Objects.nonNull(message.getBargainTime())) {
            doc.setUpdateTime(message.getBargainTime());
        }
        //游戏信息
        if (StrUtil.isNotBlank(message.getGameId())) {
            BargainTicketDoc.GameInfo gameInfo = new BargainTicketDoc.GameInfo()
                    .setGameId(message.getGameId())
                    .setGameName(message.getGameName());
            if (CollUtil.isNotEmpty(doc.getGameInfoList())) {
                doc.getGameInfoList().add(gameInfo);
                List<BargainTicketDoc.GameInfo> values = doc.getGameInfoList().stream()
                        .collect(Collectors.toMap(BargainTicketDoc.GameInfo::getGameId, Function.identity(), (a, b) -> a))
                        .values().stream().toList();
                doc.setGameInfoList(values);
            } else {
                doc.setGameInfoList(Lists.newArrayList(gameInfo));
            }
        }
        //商品信息
        if (StrUtil.isNotBlank(message.getProductUniqueNo())) {
            if (CollUtil.isEmpty(doc.getProductInfoList())) {
                doc.setProductInfoList(Lists.newArrayList(new BargainTicketDoc.ProductInfo()
                                .setProductName(message.getProductName())
                                .setNumberPrice(message.getPrice())
                                .setProductUniqueNo(message.getProductUniqueNo())
                        )
                );
            } else {
                Map<String, BargainTicketDoc.ProductInfo> productInfoMap = doc.getProductInfoList()
                        .stream()
                        .collect(Collectors.toMap(BargainTicketDoc.ProductInfo::getProductUniqueNo, Function.identity(), (a, b) -> a));
                if (productInfoMap.containsKey(message.getProductUniqueNo())) {
                    BargainTicketDoc.ProductInfo productInfo = productInfoMap.get(message.getProductUniqueNo());
                    productInfo.setProductUniqueNo(message.getProductUniqueNo());
                    productInfo.setNumberPrice(message.getPrice());
                    productInfo.setProductName(message.getProductName());
                } else {
                    doc.getProductInfoList().add(new BargainTicketDoc.ProductInfo()
                            .setProductName(message.getProductName())
                            .setNumberPrice(message.getPrice())
                            .setProductUniqueNo(message.getProductUniqueNo()));
                }
            }


        }
        if (CollUtil.isNotEmpty(doc.getProductInfoList())) {
            doc.getProductInfoList().stream()
                    .filter(productInfo -> Objects.nonNull(productInfo.getNumberPrice()))
                    .max(Comparator.comparing(BargainTicketDoc.ProductInfo::getNumberPrice))
                    .ifPresent(s -> {
                        doc.setNumberPriceMax(s.getNumberPrice());
                    });

            doc.getProductInfoList().stream()
                    .filter(productInfo -> Objects.nonNull(productInfo.getNumberPrice()))
                    .min(Comparator.comparing(BargainTicketDoc.ProductInfo::getNumberPrice))
                    .ifPresent(s -> {
                        doc.setNumberPriceMin(s.getNumberPrice());
                    });
        }

        //买家信息
        userInfoOptional.ifPresent(user -> {
            doc.setBuyerPhone(user.getPhone());
        });

        //是否有成交
        if (dealSuccess) {
            doc.setDealFlag(BargainTicketTradeStatusFlagEnum.DEAL.getCode());
        } else {
            doc.setDealFlag(BargainTicketTradeStatusFlagEnum.NONE.getCode());
        }

        //还价数量
        doc.setBargainNum(CollUtil.isEmpty(doc.getBargainInfoList()) ? NumberUtils.INTEGER_ZERO : doc.getBargainInfoList().size());


        return doc;
    }
}


