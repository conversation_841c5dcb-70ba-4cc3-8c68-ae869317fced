package com.pxb7.mall.workorder.app.model;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;


/**
 * 商品工单预警记录(RemindWorkOrder)实体类
 *
 * <AUTHOR>
 * @since 2025-04-07 12:00:04
 */
public class RemindWorkOrderReqDTO {

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class AddDTO {


        @NotBlank(message = "remindId不能为空")
        private String remindId;


        @NotBlank(message = "workOrderId不能为空")
        private String workOrderId;


        @NotNull(message = "workOrderStatus不能为空")
        private Integer workOrderStatus;


        @NotNull(message = "completeStatus不能为空")
        private Integer completeStatus;


        @NotNull(message = "timeOutStatus不能为空")
        private Integer timeOutStatus;


        @NotBlank(message = "artDesignerId不能为空")
        private String artDesignerId;


        @NotBlank(message = "followerId不能为空")
        private String followerId;


        @NotBlank(message = "auditUserId不能为空")
        private String auditUserId;


        @NotNull(message = "expectCompleteTime不能为空")
        private LocalDateTime expectCompleteTime;


        @NotNull(message = "gameConfigId不能为空")
        private Long gameConfigId;


        @NotNull(message = "remindPlanId不能为空")
        private Long remindPlanId;


        @NotNull(message = "remindSubPlanId不能为空")
        private Long remindSubPlanId;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class UpdateDTO {


        @NotNull(message = "id不能为空")
        private Long id;


        @NotBlank(message = "remindId不能为空")
        private String remindId;


        @NotBlank(message = "workOrderId不能为空")
        private String workOrderId;


        @NotNull(message = "workOrderStatus不能为空")
        private Integer workOrderStatus;


        @NotBlank(message = "productId不能为空")
        private String productId;


        @NotNull(message = "completeStatus不能为空")
        private Integer completeStatus;


        @NotNull(message = "timeOutStatus不能为空")
        private Integer timeOutStatus;


        @NotBlank(message = "artDesignerId不能为空")
        private String artDesignerId;


        @NotBlank(message = "followerId不能为空")
        private String followerId;


        @NotBlank(message = "auditUserId不能为空")
        private String auditUserId;


        @NotNull(message = "expectCompleteTime不能为空")
        private LocalDateTime expectCompleteTime;


        @NotNull(message = "gameConfigId不能为空")
        private Long gameConfigId;


        @NotNull(message = "remindPlanId不能为空")
        private Long remindPlanId;


        @NotNull(message = "remindSubPlanId不能为空")
        private Long remindSubPlanId;


    }


    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class DelDTO {
        @NotNull(message = "id不能为空")
        private Long id;
    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class SearchDTO {

        @NotBlank(message = "remindId不能为空")
        private String remindId;


        @NotBlank(message = "workOrderId不能为空")
        private String workOrderId;


        @NotBlank(message = "productId不能为空")
        private String productId;


        @NotNull(message = "workOrderStatus不能为空")
        private Integer workOrderStatus;


        @NotNull(message = "completeStatus不能为空")
        private Integer completeStatus;


        @NotNull(message = "timeOutStatus不能为空")
        private Integer timeOutStatus;


        @NotBlank(message = "artDesignerId不能为空")
        private String artDesignerId;


        @NotBlank(message = "followerId不能为空")
        private String followerId;


        @NotBlank(message = "auditUserId不能为空")
        private String auditUserId;


        @NotNull(message = "expectCompleteTime不能为空")
        private LocalDateTime expectCompleteTime;


        @NotNull(message = "gameConfigId不能为空")
        private Long gameConfigId;


        @NotNull(message = "remindPlanId不能为空")
        private Long remindPlanId;


        @NotNull(message = "remindSubPlanId不能为空")
        private Long remindSubPlanId;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class PageDTO {

        /**
         * 工单Id
         */
        private String workOrderId;

        /**
         * 页码，从1开始
         */
        @NotNull(message = "pageIndex不能为空")
        @Min(value = 1, message = "pageIndex不能小于1")
        private Integer pageIndex;
        /**
         * 每页数量
         */
        @NotNull(message = "pageSize不能为空")
        private Integer pageSize;

    }

}

