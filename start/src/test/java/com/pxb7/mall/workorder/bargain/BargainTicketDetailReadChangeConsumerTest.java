package com.pxb7.mall.workorder.bargain;

import com.pxb7.mall.workorder.adapter.consumer.bargain.BargainTicketDetailReadChangeConsumer;
import com.pxb7.mall.workorder.app.service.BargainTicketAppService;
import com.pxb7.mall.workorder.client.enums.BargainTicketEventEnum;
import com.pxb7.mall.workorder.domain.message.BargainTicketDetailReadChangeMessage;
import com.pxb7.mall.workorder.domain.message.BargainTicketMessage;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.message.MessageId;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.*;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

@SpringBootTest
public class BargainTicketDetailReadChangeConsumerTest {

    @InjectMocks
    private BargainTicketDetailReadChangeConsumer consumer;

    @Mock
    private BargainTicketAppService bargainTicketAppService;

    @Mock
    private MessageView messageView;

    @Mock
    private MessageId messageId;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        when(messageView.getMessageId()).thenReturn(messageId);
    }

    @Test
    void testConsume_ValidMessage_ProcessesSuccessfully() throws Exception {
        // 准备测试数据
        String receiveId = "receive123";
        Integer readFlag = 20; // 已读标志
        BargainTicketDetailReadChangeMessage message = new BargainTicketDetailReadChangeMessage();
        message.setReceiveId(receiveId);
        message.setReadFlag(readFlag);

        when(messageId.toString()).thenReturn("msg123");

        // 调用消费方法
        ConsumeResult result = consumer.consume(messageView, message);

        // 验证调用
        verify(bargainTicketAppService, times(1)).syncBargainTicket(argThat(new ArgumentMatcher<BargainTicketMessage>() {
            @Override
            public boolean matches(BargainTicketMessage argument) {
                return argument.getEventType().equals(BargainTicketEventEnum.BARGAIN_TICKET_READ_STATUS_CHANGE.getCode())
                        && argument.getReceiveId().equals(receiveId)
                        && argument.getReadFlag().equals(readFlag);
            }
        }));

        // 验证结果
        assertEquals(ConsumeResult.SUCCESS, result);
    }

    @Test
    void testConsume_ExceptionThrown_LogsErrorAndReturnsFailure() throws Exception {
        // 模拟抛出异常
        BargainTicketDetailReadChangeMessage message = new BargainTicketDetailReadChangeMessage();
        message.setReceiveId("receive123");
        message.setReadFlag(20);

        doThrow(new RuntimeException("Test exception")).when(bargainTicketAppService).syncBargainTicket(any());

        // 执行消费
        ConsumeResult result = consumer.consume(messageView, message);

        // 验证结果
        assertEquals(ConsumeResult.FAILURE, result);
    }
}
