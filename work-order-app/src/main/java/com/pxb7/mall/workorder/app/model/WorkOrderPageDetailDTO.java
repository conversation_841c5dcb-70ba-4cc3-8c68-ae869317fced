package com.pxb7.mall.workorder.app.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Getter
@Setter
@Accessors(chain = true)
@ToString
public class WorkOrderPageDetailDTO {


    /**
     * 预警Id
     */
    private String remindId;

    /**
     * 预警子计划ID
     */
    private Long remindSubPlanId;


    /**
     * 游戏Id
     */
    private String gameId;

    /**
     * 游戏名称
     */
    private String gameName;

    /**
     * 工单Id
     */
    private String workOrderId;

    /**
     * 工单状态,1:待接单,2:已接单,3:待跟进
     */
    private Integer workOrderStatus;

    /**
     * 完结状态,1:未完结,2:已完结,3:交付终止
     */
    private Integer completeStatus;


    /**
     * 上架方式：1:官方截图，2:自助截图，
     */
    private Integer onShelfType;

    /**
     * 接单美工Id
     */
    private String artDesignerId;

    /**
     * 接单美工
     */
    private String artDesignerName;

    /**
     * 跟进人Id
     */
    private String followerId;



    /**
     * 跟进人
     */
    private String followerName;

    /**
     * 审核人Id
     */
    private String auditUserId;


    /**
     * 审核人
     */
    private String auditUserName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 预期完结时间：通过当前时间减去expectCompleteTime可以算出
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expectCompleteTime;

    /**
     *  完结时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime completeTime;

    /**
     * 工单持续时间提示："xx天xx小时xx分"
     */
    private String durationTip;

    /**
     * 倒计时提示："xx天xx小时xx分钟后超时" OR "已超时xx天xx小时xx分钟"
     */
    private String countDownTip;

    /**
     * 待跟进流入时间
     */
    private LocalDateTime followedUpInflowTime;

}
