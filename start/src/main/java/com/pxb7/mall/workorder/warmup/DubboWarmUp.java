package com.pxb7.mall.workorder.warmup;

import com.pxb7.mall.components.warmup.PxWarmUpPostProcess;
import com.pxb7.mall.im.client.api.ImGroupServiceI;
import com.pxb7.mall.product.client.api.ProductServiceI;
import com.pxb7.mall.trade.order.client.api.OrderInfoDubboServiceI;
import com.pxb7.mall.user.api.UserServiceI;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/09/10 17:56
 **/
@Slf4j
@Component
public class DubboWarmUp implements PxWarmUpPostProcess {
    @DubboReference
    private UserServiceI userServiceI;
    @DubboReference
    private ImGroupServiceI imGroupServiceI;
    @DubboReference
    private ProductServiceI productServiceI;
    @DubboReference
    private OrderInfoDubboServiceI orderInfoDubboServiceI;

    @Override
    public void warmUpProcess() {
        try {
            userServiceI.getUserAccountInfo("5667758");
        } catch (Exception e) {
            log.error("userServiceI.getUserAccountInfo() error", e);
        }

        try {
            imGroupServiceI.queryGroupInfoByOrderId("**********************");
        } catch (Exception e) {
            log.error("imGroupService.queryGroupInfoByOrderId() error", e);
        }
        try {
            productServiceI.findGameIdByProductId("1726441670661660225");
        } catch (Exception e) {
            log.error("productServiceI.findGameIdByProductId() error", e);
        }
        try {
            orderInfoDubboServiceI.getOrderInfo("**********************");
        } catch (Exception e) {
            log.error("orderInfoDubboServiceI.getOrderInfo() error", e);
        }
    }

    @Override
    public int loopExecNum() {
        return 30;
    }

    @Override
    public boolean loopExecute() {
        return true;
    }
}
