package com.pxb7.mall.workorder.infra.repository.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 商品工单预警记录(RemindWorkOrder)实体类
 *
 * <AUTHOR>
 * @since 2025-04-07 11:58:48
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "remind_work_order")
@ToString
public class RemindWorkOrder implements Serializable {
    private static final long serialVersionUID = 941937468285397258L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 预警记录id
     */
    @TableField(value = "remind_id")
    private String remindId;
    /**
     * 工单id
     */
    @TableField(value = "work_order_id")
    private String workOrderId;
    /**
     * 工单状态,1:待接单,2:已接单,3:待跟进
     */
    @TableField(value = "work_order_status")
    private Integer workOrderStatus;
    /**
     * 商品id
     */
    @TableField(value = "product_id")
    private String productId;
    /**
     * 商品编码
     */
    @TableField(value = "product_code")
    private String productCode;
    /**
     * 游戏id
     */
    @TableField(value = "game_id")
    private String gameId;
    /**
     * 完结状态,1:未完结,2:已完结,3:终止
     */
    @TableField(value = "complete_status")
    private Integer completeStatus;
    /**
     * 超时状态，0:无,1:即将超时,2:已超时
     */
    @TableField(value = "time_out_status")
    private Integer timeOutStatus;
    /**
     * 接单美工
     */
    @TableField(value = "art_designer_id")
    private String artDesignerId;
    /**
     * 跟进人
     */
    @TableField(value = "follower_id")
    private String followerId;
    /**
     * 审核客服
     */
    @TableField(value = "audit_user_id")
    private String auditUserId;
    /**
     * 预期完结时间
     */
    @TableField(value = "expect_complete_time")
    private LocalDateTime expectCompleteTime;
    /**
     * 完结时间
     */
    @TableField(value = "complete_time")
    private LocalDateTime completeTime;
    /**
     * 预警计划游戏配置id,remind_plan_game_config
     */
    @TableField(value = "game_config_id")
    private Long gameConfigId;
    /**
     * 预警计划id,remind_plan
     */
    @TableField(value = "remind_plan_id")
    private Long remindPlanId;
    /**
     * 预警子计划id,remind_sub_plan
     */
    @TableField(value = "remind_sub_plan_id")
    private Long remindSubPlanId;
    /**
     * 待跟进流入时间
     */
    @TableField(value = "followed_up_inflow_time")
    private LocalDateTime followedUpInflowTime;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;
    /**
     * 创建人
     */
    @TableField(value = "create_user_id")
    private String createUserId;
    /**
     * 更新人
     */
    @TableField(value = "update_user_id")
    private String updateUserId;
    /**
     * 是否删除 0:否, 1:是
     */
    @TableLogic
    @TableField(value = "is_deleted")
    private Boolean deleted;

    /**
      * 环境变量
      */
     @TableField(value = "env_profile", fill = FieldFill.INSERT)
     private Integer envProfile;

}

