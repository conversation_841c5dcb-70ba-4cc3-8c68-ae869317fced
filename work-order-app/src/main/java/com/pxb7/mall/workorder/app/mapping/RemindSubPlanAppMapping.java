package com.pxb7.mall.workorder.app.mapping;

import com.pxb7.mall.workorder.app.model.RemindSubPlanReqDTO;
import com.pxb7.mall.workorder.app.model.RemindSubPlanRespDTO;
import com.pxb7.mall.workorder.domain.model.RemindSubPlanBO;
import com.pxb7.mall.workorder.domain.model.RemindSubPlanReqBO;
import com.pxb7.mall.workorder.domain.model.RemindSubPlanRespBO;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;


@Mapper
public interface RemindSubPlanAppMapping {

    RemindSubPlanAppMapping INSTANCE = Mappers.getMapper(RemindSubPlanAppMapping.class);


    RemindSubPlanBO remindSubPlanDTO2AddBO(RemindSubPlanReqDTO.AddDTO source);

    RemindSubPlanBO remindSubPlanDTO2UpdateBO(RemindSubPlanReqDTO.UpdateDTO source);

    RemindSubPlanReqBO.DelBO remindSubPlanDTO2DelBO(RemindSubPlanReqDTO.DelDTO source);

    RemindSubPlanReqBO.SearchBO remindSubPlanDTO2SearchBO(RemindSubPlanReqDTO.SearchDTO source);

    RemindSubPlanReqBO.PageBO remindSubPlanDTO2PageBO(RemindSubPlanReqDTO.PageDTO source);

    RemindSubPlanRespDTO.DetailDTO remindSubPlanBO2DetailDTO(RemindSubPlanRespBO.DetailBO source);

    List<RemindSubPlanRespDTO.DetailDTO> remindSubPlanBO2ListDTO(List<RemindSubPlanRespBO.DetailBO> source);

    Page<RemindSubPlanRespDTO.DetailDTO> remindSubPlanBO2PageDTO(Page<RemindSubPlanRespBO.DetailBO> source);

}


