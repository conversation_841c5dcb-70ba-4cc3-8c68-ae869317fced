package com.pxb7.mall.workorder.infra.repository.db;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.pxb7.mall.workorder.infra.model.RemindPlanRetryTaskReqPO;
import com.pxb7.mall.workorder.infra.repository.db.entity.RemindPlanRetryTask;

import java.util.List;

/**
 * 生成提醒计划异常重试表(RemindPlanRetryTask)表服务接口
 *
 * <AUTHOR>
 * @since 2025-05-06 20:53:53
 */
public interface RemindPlanRetryTaskRepository extends IService<RemindPlanRetryTask> {

    boolean insert(RemindPlanRetryTaskReqPO.AddPO param);

    boolean batchUpdate(List<RemindPlanRetryTaskReqPO.UpdatePO> param);

    Page<RemindPlanRetryTask> page(RemindPlanRetryTaskReqPO.PagePO param);

}

