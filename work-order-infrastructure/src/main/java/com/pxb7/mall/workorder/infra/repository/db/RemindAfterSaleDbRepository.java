package com.pxb7.mall.workorder.infra.repository.db;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pxb7.mall.components.env.profile.PxProfileHelper;
import com.pxb7.mall.workorder.infra.mapping.AfterSaleMapping;
import com.pxb7.mall.workorder.infra.model.RemindAfterSaleReqPO;
import com.pxb7.mall.workorder.infra.repository.db.entity.RemindAfterSale;
import com.pxb7.mall.workorder.infra.repository.db.mapper.RemindAfterSaleMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Objects;

/**
 * 售后工单预警记录(RemindAfterSale)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-04-24 23:31:56
 */
@Slf4j
@Repository
public class RemindAfterSaleDbRepository extends ServiceImpl<RemindAfterSaleMapper, RemindAfterSale> implements RemindAfterSaleRepository {

    @Override
    public boolean insert(RemindAfterSaleReqPO.AddPO param) {
        RemindAfterSale entity = AfterSaleMapping.INSTANCE.addDto2PO(param);
        return this.save(entity);
    }

    @Override
    public boolean updateByWorkOrderId(RemindAfterSaleReqPO.UpdatePO param) {
        LambdaUpdateWrapper<RemindAfterSale> updateWrapper = new LambdaUpdateWrapper<>();
        //where
        updateWrapper.eq(RemindAfterSale::getWorkOrderId, param.getWorkOrderId());
        if (StringUtils.isNotBlank(param.getRemindId())) {
            updateWrapper.eq(RemindAfterSale::getRemindId, param.getRemindId());
        }
        //set
        if (Objects.nonNull(param.getCompleteStatus())) {
            updateWrapper.set(RemindAfterSale::getCompleteStatus, param.getCompleteStatus());
        }
        if (Objects.nonNull(param.getTimeOutStatus())) {
            updateWrapper.set(RemindAfterSale::getTimeOutStatus, param.getTimeOutStatus());
        }
        if (Objects.nonNull(param.getCompleteTime())) {
            updateWrapper.set(RemindAfterSale::getCompleteTime, param.getCompleteTime());
        }
        return this.update(updateWrapper);
    }

    @Override
    public RemindAfterSale findByWorkOrderId(String workOrderId, String remindId) {
        LambdaQueryWrapper<RemindAfterSale> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RemindAfterSale::getWorkOrderId, workOrderId);
        if (StringUtils.isNotBlank(remindId)) {
            queryWrapper.eq(RemindAfterSale::getRemindId, remindId);
        }
        return this.getOne(queryWrapper);
    }

    @Override
    public Page<RemindAfterSale> page(RemindAfterSaleReqPO.PagePO param) {
        LambdaQueryWrapper<RemindAfterSale> queryWrapper = new LambdaQueryWrapper<>();
        // 返回分页查询结果
        queryWrapper.eq(RemindAfterSale::getEnvProfile, PxProfileHelper.getProfile().getProfileValue());
        queryWrapper.gt(RemindAfterSale::getId, param.getId());
        queryWrapper.lt(RemindAfterSale::getExpectCompleteTime, param.getExpectCompleteTime());
        queryWrapper.eq(RemindAfterSale::getCompleteStatus, param.getCompleteStatus());
        queryWrapper.ne(RemindAfterSale::getTimeOutStatus, param.getTimeOutStatus());
        queryWrapper.orderByAsc(RemindAfterSale::getId);
        return this.page(new Page<>(param.getPageIndex(), param.getPageSize()), queryWrapper);
    }

    /**
     * 批量更新状态
     * @param ids
     * @param status
     * @return
     */
    @Override
    public boolean batchUpdateTimeoutStatusByIds(List<Long> ids, Integer status) {
        return this.getBaseMapper().batchUpdateTimeoutStatusByIds(ids, status) > 0;
    }

    @Override
    public RemindAfterSale getByRemindId(String remindId) {
        if (StringUtils.isBlank(remindId)) {
            return null;
        }
        return this.lambdaQuery().eq(RemindAfterSale::getRemindId, remindId).one();
    }

}
