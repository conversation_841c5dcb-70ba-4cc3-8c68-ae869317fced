<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pxb7.mall.workorder.infra.repository.db.mapper.AdsPxb7DeliveryForewarningMapper">

    <resultMap id="BaseResultMap" type="com.pxb7.mall.workorder.infra.repository.db.entity.AdsPxb7DeliveryForewarning">
        <!--@Table ads_pxb7_delivery_forewarning-->
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="businessType" column="business_type" jdbcType="INTEGER"/>
        <result property="createDate" column="create_date" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="createUserId" column="create_user_id" jdbcType="VARCHAR"/>
        <result property="deliveryCustomerCare" column="delivery_customer_care" jdbcType="VARCHAR"/>
        <result property="deliveryRemindCnt" column="delivery_remind_cnt" jdbcType="INTEGER"/>
        <result property="deliveryRemindFinishCnt" column="delivery_remind_finish_cnt" jdbcType="INTEGER"/>
        <result property="deliveryRemindTimeoutCnt" column="delivery_remind_timeout_cnt" jdbcType="INTEGER"/>
        <result property="deliveryRemindTimeoutFinishCnt" column="delivery_remind_timeout_finish_cnt" jdbcType="INTEGER"/>
        <result property="deliveryRemindUnfinishedCnt" column="delivery_remind_unfinished_cnt" jdbcType="INTEGER"/>
        <result property="gameId" column="game_id" jdbcType="VARCHAR"/>
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="updateUserId" column="update_user_id" jdbcType="VARCHAR"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="BaseResultMap">
        select
        id, business_type, create_date, create_time, create_user_id, delivery_customer_care, delivery_remind_cnt,
        delivery_remind_finish_cnt, delivery_remind_timeout_cnt, delivery_remind_timeout_finish_cnt,
        delivery_remind_unfinished_cnt, game_id, is_deleted, update_time, update_user_id
        from ads_pxb7_delivery_forewarning
        where id = #{id}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="BaseResultMap">
        select
        id, business_type, create_date, create_time, create_user_id, delivery_customer_care, delivery_remind_cnt,
        delivery_remind_finish_cnt, delivery_remind_timeout_cnt, delivery_remind_timeout_finish_cnt,
        delivery_remind_unfinished_cnt, game_id, is_deleted, update_time, update_user_id
        from ads_pxb7_delivery_forewarning
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="businessType != null">
                and business_type = #{businessType}
            </if>
            <if test="createDate != null and createDate != ''">
                and create_date = #{createDate}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="createUserId != null and createUserId != ''">
                and create_user_id = #{createUserId}
            </if>
            <if test="deliveryCustomerCare != null and deliveryCustomerCare != ''">
                and delivery_customer_care = #{deliveryCustomerCare}
            </if>
            <if test="deliveryRemindCnt != null">
                and delivery_remind_cnt = #{deliveryRemindCnt}
            </if>
            <if test="deliveryRemindFinishCnt != null">
                and delivery_remind_finish_cnt = #{deliveryRemindFinishCnt}
            </if>
            <if test="deliveryRemindTimeoutCnt != null">
                and delivery_remind_timeout_cnt = #{deliveryRemindTimeoutCnt}
            </if>
            <if test="deliveryRemindTimeoutFinishCnt != null">
                and delivery_remind_timeout_finish_cnt = #{deliveryRemindTimeoutFinishCnt}
            </if>
            <if test="deliveryRemindUnfinishedCnt != null">
                and delivery_remind_unfinished_cnt = #{deliveryRemindUnfinishedCnt}
            </if>
            <if test="gameId != null and gameId != ''">
                and game_id = #{gameId}
            </if>
            <if test="isDeleted != null">
                and is_deleted = #{isDeleted}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="updateUserId != null and updateUserId != ''">
                and update_user_id = #{updateUserId}
            </if>
        </where>
        limit #{pageable.offset}, #{pageable.pageSize}
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="countBySearchParam" resultType="com.pxb7.mall.workorder.infra.model.DeliveryStatictisDataPO"
    parameterType="com.pxb7.mall.workorder.infra.model.DeliveryStatictisSearchPO">
        SELECT
        create_date
        <if test="param.gameIds !=null and !param.gameIds.isEmpty()">
            ,game_id
        </if>
        <if test="param.businessType != null and param.businessType != ''">
            ,business_type
        </if>
        <if test="param.deliveryCustomerCareIds !=null and !param.deliveryCustomerCareIds.isEmpty()">
            ,delivery_customer_care
        </if>
        ,sum(delivery_remind_cnt ) AS 'delivery_remind_cnt',
        sum(delivery_remind_finish_cnt) AS 'delivery_remind_finish_cnt',
        sum(delivery_remind_unfinished_cnt) AS 'delivery_remind_unfinished_cnt',
        sum(delivery_remind_timeout_cnt) AS 'delivery_remind_timeout_cnt',
        SUM(delivery_remind_timeout_finish_cnt) AS 'delivery_remind_timeout_finish_cnt'
        FROM
        ads_pxb7_delivery_forewarning
        where
            is_deleted = 0
            and create_date BETWEEN #{param.startDate} AND #{param.endDate}
            <if test="param.gameIds !=null and !param.gameIds.isEmpty()">
                and game_id IN
                <foreach collection="param.gameIds" item="gameId" separator="," open="(" close=")">
                    <!-- 使用 #{gameId} 防止 SQL 注入 -->
                    #{gameId}
                </foreach>
            </if>


            <if test="param.businessType != null">
                and business_type = #{param.businessType}
            </if>
            <if test="param.deliveryCustomerCareIds !=null and !param.deliveryCustomerCareIds.isEmpty()">
                and delivery_customer_care IN
                <foreach collection="param.deliveryCustomerCareIds" item="deliveryCustomerCareId" separator="," open="(" close=")">
                    #{deliveryCustomerCareId}
                </foreach>
            </if>

        group by create_date
        <if test="param.gameIds !=null and !param.gameIds.isEmpty()">
            ,game_id
        </if>
        <if test="param.businessType != null and param.businessType != ''">
            ,business_type
        </if>
        <if test="param.deliveryCustomerCareIds !=null and !param.deliveryCustomerCareIds.isEmpty()">
            ,delivery_customer_care
        </if>
    </select>


    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from ads_pxb7_delivery_forewarning
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="businessType != null">
                and business_type = #{businessType}
            </if>
            <if test="createDate != null and createDate != ''">
                and create_date = #{createDate}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="createUserId != null and createUserId != ''">
                and create_user_id = #{createUserId}
            </if>
            <if test="deliveryCustomerCare != null and deliveryCustomerCare != ''">
                and delivery_customer_care = #{deliveryCustomerCare}
            </if>
            <if test="deliveryRemindCnt != null">
                and delivery_remind_cnt = #{deliveryRemindCnt}
            </if>
            <if test="deliveryRemindFinishCnt != null">
                and delivery_remind_finish_cnt = #{deliveryRemindFinishCnt}
            </if>
            <if test="deliveryRemindTimeoutCnt != null">
                and delivery_remind_timeout_cnt = #{deliveryRemindTimeoutCnt}
            </if>
            <if test="deliveryRemindTimeoutFinishCnt != null">
                and delivery_remind_timeout_finish_cnt = #{deliveryRemindTimeoutFinishCnt}
            </if>
            <if test="deliveryRemindUnfinishedCnt != null">
                and delivery_remind_unfinished_cnt = #{deliveryRemindUnfinishedCnt}
            </if>
            <if test="gameId != null and gameId != ''">
                and game_id = #{gameId}
            </if>
            <if test="isDeleted != null">
                and is_deleted = #{isDeleted}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="updateUserId != null and updateUserId != ''">
                and update_user_id = #{updateUserId}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into ads_pxb7_delivery_forewarning(business_type, create_date, create_time, create_user_id,
        delivery_customer_care, delivery_remind_cnt, delivery_remind_finish_cnt, delivery_remind_timeout_cnt,
        delivery_remind_timeout_finish_cnt, delivery_remind_unfinished_cnt, game_id, is_deleted, update_time,
        update_user_id)
        values (#{businessType}, #{createDate}, #{createTime}, #{createUserId}, #{deliveryCustomerCare},
        #{deliveryRemindCnt}, #{deliveryRemindFinishCnt}, #{deliveryRemindTimeoutCnt},
        #{deliveryRemindTimeoutFinishCnt}, #{deliveryRemindUnfinishedCnt}, #{gameId}, #{isDeleted}, #{updateTime},
        #{updateUserId})
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into ads_pxb7_delivery_forewarning(business_type, create_date, create_time, create_user_id,
        delivery_customer_care, delivery_remind_cnt, delivery_remind_finish_cnt, delivery_remind_timeout_cnt,
        delivery_remind_timeout_finish_cnt, delivery_remind_unfinished_cnt, game_id, is_deleted, update_time,
        update_user_id)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.businessType}, #{entity.createDate}, #{entity.createTime}, #{entity.createUserId},
            #{entity.deliveryCustomerCare}, #{entity.deliveryRemindCnt}, #{entity.deliveryRemindFinishCnt},
            #{entity.deliveryRemindTimeoutCnt}, #{entity.deliveryRemindTimeoutFinishCnt},
            #{entity.deliveryRemindUnfinishedCnt}, #{entity.gameId}, #{entity.isDeleted}, #{entity.updateTime},
            #{entity.updateUserId})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into ads_pxb7_delivery_forewarning(business_type, create_date, create_time, create_user_id,
        delivery_customer_care, delivery_remind_cnt, delivery_remind_finish_cnt, delivery_remind_timeout_cnt,
        delivery_remind_timeout_finish_cnt, delivery_remind_unfinished_cnt, game_id, is_deleted, update_time,
        update_user_id)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.businessType}, #{entity.createDate}, #{entity.createTime}, #{entity.createUserId},
            #{entity.deliveryCustomerCare}, #{entity.deliveryRemindCnt}, #{entity.deliveryRemindFinishCnt},
            #{entity.deliveryRemindTimeoutCnt}, #{entity.deliveryRemindTimeoutFinishCnt},
            #{entity.deliveryRemindUnfinishedCnt}, #{entity.gameId}, #{entity.isDeleted}, #{entity.updateTime},
            #{entity.updateUserId})
        </foreach>
        on duplicate key update
        business_type = values(business_type),
        create_date = values(create_date),
        create_time = values(create_time),
        create_user_id = values(create_user_id),
        delivery_customer_care = values(delivery_customer_care),
        delivery_remind_cnt = values(delivery_remind_cnt),
        delivery_remind_finish_cnt = values(delivery_remind_finish_cnt),
        delivery_remind_timeout_cnt = values(delivery_remind_timeout_cnt),
        delivery_remind_timeout_finish_cnt = values(delivery_remind_timeout_finish_cnt),
        delivery_remind_unfinished_cnt = values(delivery_remind_unfinished_cnt),
        game_id = values(game_id),
        is_deleted = values(is_deleted),
        update_time = values(update_time),
        update_user_id = values(update_user_id)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update ads_pxb7_delivery_forewarning
        <set>
            <if test="businessType != null">
                business_type = #{businessType},
            </if>
            <if test="createDate != null and createDate != ''">
                create_date = #{createDate},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="createUserId != null and createUserId != ''">
                create_user_id = #{createUserId},
            </if>
            <if test="deliveryCustomerCare != null and deliveryCustomerCare != ''">
                delivery_customer_care = #{deliveryCustomerCare},
            </if>
            <if test="deliveryRemindCnt != null">
                delivery_remind_cnt = #{deliveryRemindCnt},
            </if>
            <if test="deliveryRemindFinishCnt != null">
                delivery_remind_finish_cnt = #{deliveryRemindFinishCnt},
            </if>
            <if test="deliveryRemindTimeoutCnt != null">
                delivery_remind_timeout_cnt = #{deliveryRemindTimeoutCnt},
            </if>
            <if test="deliveryRemindTimeoutFinishCnt != null">
                delivery_remind_timeout_finish_cnt = #{deliveryRemindTimeoutFinishCnt},
            </if>
            <if test="deliveryRemindUnfinishedCnt != null">
                delivery_remind_unfinished_cnt = #{deliveryRemindUnfinishedCnt},
            </if>
            <if test="gameId != null and gameId != ''">
                game_id = #{gameId},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="updateUserId != null and updateUserId != ''">
                update_user_id = #{updateUserId},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from ads_pxb7_delivery_forewarning where id = #{id}
    </delete>

</mapper>

