package com.pxb7.mall.workorder.infra.model;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;


/**
 * 账号交付预警记录(RemindDeliveryProduct)实体类
 *
 * <AUTHOR>
 * @since 2025-03-27 20:07:10
 */
public class RemindDeliveryProductReqPO {

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class AddPO {


        private String remindId;


        private String productId;


        private String productCode;


        private String gameId;


        private String groupId;


        private String orderId;


        private String orderItemId;


        private Integer completeStatus;


        private Integer timeOutStatus;


        private String deliveryCustomerCare;


        private LocalDateTime expectCompleteTime;


        private LocalDateTime imCountDownTime;


        private Long gameConfigId;


        private Long remindPlanId;


        private Long remindSubPlanId;


        private String createUserId;


        private String updateUserId;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class UpdatePO {


        private Long id;


        private String remindId;


        private String productId;


        private String productCode;


        private String gameId;


        private String groupId;


        private String orderId;


        private String orderItemId;


        private Integer completeStatus;

        private Integer deliveryStatus;


        private Integer timeOutStatus;


        private String deliveryCustomerCare;


        private LocalDateTime expectCompleteTime;


        private LocalDateTime imCountDownTime;

        private LocalDateTime pauseTime;

        private LocalDateTime payTime;

        private LocalDateTime completeTime;


        private Long gameConfigId;


        private Long remindPlanId;


        private Long remindSubPlanId;


        private String createUserId;


        private String updateUserId;


    }


    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class DelPO {
        private Long id;
    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class SearchPO {

        private String remindId;


        private String productId;


        private String productCode;


        private String gameId;


        private String groupId;


        private String orderId;


        private String orderItemId;


        private Integer completeStatus;


        private Integer timeOutStatus;


        private String deliveryCustomerCare;


        private LocalDateTime expectCompleteTime;


        private LocalDateTime imCountDownTime;


        private Long gameConfigId;


        private Long remindPlanId;


        private Long remindSubPlanId;


        private String createUserId;


        private String updateUserId;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class PagePO {

        private Long id;

        private String remindId;


        private String productId;


        private String productCode;


        private String gameId;


        private String groupId;


        private String orderId;


        private String orderItemId;


        private Integer completeStatus;


        private Integer timeOutStatus;


        private String deliveryCustomerCare;


        private LocalDateTime expectCompleteTime;


        private LocalDateTime imCountDownTime;


        private Long gameConfigId;


        private Long remindPlanId;


        private Long remindSubPlanId;


        private String createUserId;


        private String updateUserId;


        /**
         * 页码，从1开始
         */
        private long pageIndex;
        /**
         * 每页数量
         */
        private long pageSize;

    }

}

