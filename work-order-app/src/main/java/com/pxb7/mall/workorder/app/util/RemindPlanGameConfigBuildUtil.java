package com.pxb7.mall.workorder.app.util;

import com.alibaba.cola.exception.Assert;
import com.pxb7.mall.auth.dto.AdminUserDTO;
import com.pxb7.mall.workorder.app.model.AssPlanGameConfigDTO;
import com.pxb7.mall.workorder.app.model.ComplaintPlanGameConfigDTO;
import com.pxb7.mall.workorder.domain.model.*;
import com.pxb7.mall.workorder.infra.enums.RemindPlanServiceTypeEnum;
import com.pxb7.mall.workorder.infra.enums.RemindPlanWorkOrderStatusEnum;
import com.pxb7.mall.workorder.app.mapping.RemindPlanGameConfigAppMapping;
import com.pxb7.mall.workorder.app.mapping.TimeConfigDomainMapping;
import com.pxb7.mall.workorder.app.model.PlanGameConfigDTO;
import com.pxb7.mall.workorder.app.model.TimeConfigDTO;
import groovy.util.logging.Slf4j;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.pxb7.mall.workorder.client.enums.BizErrorCodeEnum.PARAM_ERROR;

@lombok.extern.slf4j.Slf4j
@Slf4j
public class RemindPlanGameConfigBuildUtil {

    public static RemindPlanGameConfigRefreshBO buildRemindPlanGameConfigRefreshBO(List<PlanGameConfigDTO> planGameConfigDTOS, RemindPlanBO remindPlanBO,
                                                                                   AdminUserDTO adminUser) {


        Assert.notEmpty(planGameConfigDTOS, PARAM_ERROR.getErrCode(), "预警设置不能为空");
        List<String> inputGameIds =  planGameConfigDTOS.stream().map(PlanGameConfigDTO::getGameId).distinct().toList();

        List<RemindSubPlanBO> remindSubPlans = remindPlanBO.getRemindSubPlans();
        boolean specailWorkOrderStatus = RemindPlanServiceTypeEnum.WORK_ORDER.getValue().equals(remindPlanBO.getServiceType()) ;
        Function<PlanGameConfigDTO, String> groupFun = PlanGameConfigDTO::getGameId;
        if (specailWorkOrderStatus) {
            groupFun = a -> a.getGameId() + "_" + a.getOnShelfType();
            for (PlanGameConfigDTO planGameConfigDTO : planGameConfigDTOS) {
                Assert.notNull(planGameConfigDTO.getOnShelfType(), PARAM_ERROR.getErrCode(), "上架方式不能为空!");
            }
        }
        Map<String, PlanGameConfigDTO> inputGameConfigMap = planGameConfigDTOS.stream()
            .collect(Collectors.toMap(groupFun, Function.identity(), (l, r) -> l));

        // 预警-游戏配置：
        // 新增了哪些？删除了哪些？ 修改了哪些？
        List<RemindPlanGameConfigBO> allRemindPlanGameConfigBOS = Optional.of(remindPlanBO).map(RemindPlanBO::getAllRemindPlanGameConfigs).orElse(new ArrayList<>());
        List<String> existGameIds = allRemindPlanGameConfigBOS.stream().map(RemindPlanGameConfigBO::getGameId).distinct().toList();

        //游戏交付时间配置-新增
        List<PlanGameConfigDTO> needAddPlanGameConfigDTOS =
            planGameConfigDTOS.stream().filter(a -> !existGameIds.contains(a.getGameId())).toList();
        List<RemindPlanGameConfigBO> needAddPlanGameConfigBOS = new ArrayList<>();
        for (RemindSubPlanBO remindSubPlan : remindSubPlans) {
            for (PlanGameConfigDTO planGameConfigDTO : needAddPlanGameConfigDTOS) {
                if (specailWorkOrderStatus &&  !remindSubPlan.getOnShelfType().equals(planGameConfigDTO.getOnShelfType())){
                    continue;
                }
                RemindPlanGameConfigBO addPlanGameConfigBO =
                    RemindPlanGameConfigAppMapping.INSTANCE.remindPlanGameConfigDTO2BO(planGameConfigDTO);
                addPlanGameConfigBO.setRemindPlanId(remindSubPlan.getRemindPlanId());
                addPlanGameConfigBO.setRemindSubPlanId(remindSubPlan.getId());
                addPlanGameConfigBO.setCreateUserId(adminUser.getUserId());
                addPlanGameConfigBO.setUpdateUserId(adminUser.getUserId());
                needAddPlanGameConfigBOS.add(addPlanGameConfigBO);
            }
        }
        //游戏交付时间配置-删除
        List<RemindPlanGameConfigBO> needDeleteRemindPlanGameConfigBOS = allRemindPlanGameConfigBOS.stream().filter(a -> !inputGameIds.contains(a.getGameId()))
            .map(a->a.setUpdateUserId(adminUser.getUserId())).toList();

        //游戏交付时间配置-修改
        List<RemindPlanGameConfigBO> waitUpdateRemindPlanGameConfigBOS = allRemindPlanGameConfigBOS.stream().filter(a -> inputGameIds.contains(a.getGameId())).toList();
        List<RemindPlanGameConfigBO> needUpdateRemindPlanGameConfigBOS = new ArrayList<>();
        for (RemindPlanGameConfigBO waitUpdatePlanGameConfigBO : waitUpdateRemindPlanGameConfigBOS) {
            String key = waitUpdatePlanGameConfigBO.getGameId();
            if (specailWorkOrderStatus) {
                Long remindSubPlanId = waitUpdatePlanGameConfigBO.getRemindSubPlanId();
                RemindSubPlanBO remindSubPlanBO = remindPlanBO.tryGetRemindSubPlan(remindSubPlanId);
                if (null == remindSubPlanBO) {
                    continue;
                }
                key = waitUpdatePlanGameConfigBO.getGameId() + "_" + remindSubPlanBO.getOnShelfType();
            }
            PlanGameConfigDTO planGameConfigDTO = inputGameConfigMap.get(key);
            if (null==planGameConfigDTO){
                continue;
            }
            if (!hasPlanGameConfigChanged(waitUpdatePlanGameConfigBO, planGameConfigDTO)) {
                log.info("planGameConfig no change:{}", waitUpdatePlanGameConfigBO.getId());
                continue;
            }


            waitUpdatePlanGameConfigBO.setExpectCompleteTimeConfig(TimeConfigDomainMapping.INSTANCE.timeConfigDTO2BO(planGameConfigDTO.getExpectCompleteTimeConfig()));
            waitUpdatePlanGameConfigBO.setImCountDownTimeConfig(TimeConfigDomainMapping.INSTANCE.timeConfigDTO2BO(planGameConfigDTO.getImCountDownTimeConfig()));
            waitUpdatePlanGameConfigBO.setUpdateUserId(adminUser.getUserId());
            waitUpdatePlanGameConfigBO.setUpdateTime(LocalDateTime.now());
            needUpdateRemindPlanGameConfigBOS.add(waitUpdatePlanGameConfigBO);
        }

        return new RemindPlanGameConfigRefreshBO().setNeedAddPlanGameConfigs(needAddPlanGameConfigBOS).setNeedDeletePlanGameConfigs(needDeleteRemindPlanGameConfigBOS)
            .setNeedUpdatePlanGameConfigs(needUpdateRemindPlanGameConfigBOS);
    }

    public static RemindPlanGameConfigRefreshBO buildAssRemindPlanGameConfigRefreshBO(
        List<AssPlanGameConfigDTO> planGameConfigDTOS, RemindPlanBO remindPlanBO, AdminUserDTO adminUser) {

        Assert.notEmpty(planGameConfigDTOS, PARAM_ERROR.getErrCode(), "预警设置不能为空");
        List<String> inputGameIds =
            planGameConfigDTOS.stream().map(AssPlanGameConfigDTO::getGameId).distinct().toList();

        List<RemindSubPlanBO> remindSubPlans = remindPlanBO.getRemindSubPlans();
        Function<AssPlanGameConfigDTO, String> groupFun = AssPlanGameConfigDTO::getGameId;
        Map<String, AssPlanGameConfigDTO> inputGameConfigMap =
            planGameConfigDTOS.stream().collect(Collectors.toMap(groupFun, Function.identity(), (l, r) -> l));

        // 预警-游戏配置：
        // 新增了哪些？删除了哪些？ 修改了哪些？
        List<RemindPlanGameConfigBO> allRemindPlanGameConfigBOS =
            Optional.of(remindPlanBO).map(RemindPlanBO::getAllRemindPlanGameConfigs).orElse(new ArrayList<>());
        // 修改前
        List<String> existGameIds =
            allRemindPlanGameConfigBOS.stream().map(RemindPlanGameConfigBO::getGameId).distinct().toList();

        //游戏交付时间配置-新增
        List<AssPlanGameConfigDTO> needAddPlanGameConfigDTOS =
            planGameConfigDTOS.stream().filter(a -> !existGameIds.contains(a.getGameId())).toList();
        List<RemindPlanGameConfigBO> needAddPlanGameConfigBOS = new ArrayList<>();
        for (RemindSubPlanBO remindSubPlan : remindSubPlans) {
            for (AssPlanGameConfigDTO planGameConfigDTO : needAddPlanGameConfigDTOS) {

                RemindPlanGameConfigBO addPlanGameConfigBO =
                    RemindPlanGameConfigAppMapping.INSTANCE.remindPlanGameConfigDTO2BO(planGameConfigDTO);
                addPlanGameConfigBO.setRemindPlanId(remindSubPlan.getRemindPlanId());
                addPlanGameConfigBO.setRemindSubPlanId(remindSubPlan.getId());
                addPlanGameConfigBO.setCreateUserId(adminUser.getUserId());
                addPlanGameConfigBO.setUpdateUserId(adminUser.getUserId());
                needAddPlanGameConfigBOS.add(addPlanGameConfigBO);
            }
        }
        //游戏交付时间配置-删除
        List<RemindPlanGameConfigBO> needDeleteRemindPlanGameConfigBOS =
            allRemindPlanGameConfigBOS.stream().filter(a -> !inputGameIds.contains(a.getGameId()))
                .map(a -> a.setUpdateUserId(adminUser.getUserId())).toList();

        //游戏交付时间配置-修改
        List<RemindPlanGameConfigBO> waitUpdateRemindPlanGameConfigBOS =
            allRemindPlanGameConfigBOS.stream().filter(a -> inputGameIds.contains(a.getGameId())).toList();
        List<RemindPlanGameConfigBO> needUpdateRemindPlanGameConfigBOS = new ArrayList<>();
        for (RemindPlanGameConfigBO waitUpdatePlanGameConfigBO : waitUpdateRemindPlanGameConfigBOS) {
            String key = waitUpdatePlanGameConfigBO.getGameId();

            AssPlanGameConfigDTO planGameConfigDTO = inputGameConfigMap.get(key);
            if (null == planGameConfigDTO) {
                continue;
            }
            if (!hasPlanGameConfigChanged(waitUpdatePlanGameConfigBO, planGameConfigDTO)) {
                log.info("planGameConfig no change:{}", waitUpdatePlanGameConfigBO.getId());
                continue;
            }

            waitUpdatePlanGameConfigBO.setExpectCompleteTimeConfig(
                TimeConfigDomainMapping.INSTANCE.timeConfigDTO2BO(planGameConfigDTO.getExpectCompleteTimeConfig()));
            waitUpdatePlanGameConfigBO.setImCountDownTimeConfig(
                TimeConfigDomainMapping.INSTANCE.timeConfigDTO2BO(planGameConfigDTO.getImCountDownTimeConfig()));
            waitUpdatePlanGameConfigBO.setUpdateUserId(adminUser.getUserId());
            waitUpdatePlanGameConfigBO.setUpdateTime(LocalDateTime.now());
            needUpdateRemindPlanGameConfigBOS.add(waitUpdatePlanGameConfigBO);
        }

        return new RemindPlanGameConfigRefreshBO().setNeedAddPlanGameConfigs(needAddPlanGameConfigBOS)
            .setNeedDeletePlanGameConfigs(needDeleteRemindPlanGameConfigBOS)
            .setNeedUpdatePlanGameConfigs(needUpdateRemindPlanGameConfigBOS);
    }

    public static RemindPlanGameConfigRefreshBO buildComplaintRemindPlanGameConfigRefreshBO(
        List<ComplaintPlanGameConfigDTO> planGameConfigDTOS, RemindPlanBO remindPlanBO, AdminUserDTO adminUser) {

        Assert.notEmpty(planGameConfigDTOS, PARAM_ERROR.getErrCode(), "预警设置不能为空");
        List<Integer> inputComplaintChannels =
            planGameConfigDTOS.stream().map(ComplaintPlanGameConfigDTO::getChannel).distinct().toList();

        List<RemindSubPlanBO> remindSubPlans = remindPlanBO.getRemindSubPlans();
        Function<ComplaintPlanGameConfigDTO, Integer> groupFun = ComplaintPlanGameConfigDTO::getChannel;
        Map<Integer, ComplaintPlanGameConfigDTO> inputGameConfigMap =
            planGameConfigDTOS.stream().collect(Collectors.toMap(groupFun, Function.identity(), (l, r) -> l));

        // 预警-游戏配置：
        // 新增了哪些？删除了哪些？ 修改了哪些？
        List<RemindPlanGameConfigBO> allRemindPlanGameConfigBOS =
            Optional.of(remindPlanBO).map(RemindPlanBO::getAllRemindPlanGameConfigs).orElse(new ArrayList<>());
        // 修改前
        List<Integer> existComplaintChannels =
            allRemindPlanGameConfigBOS.stream().map(RemindPlanGameConfigBO::getChannel).distinct().toList();

        //游戏交付时间配置-新增
        List<ComplaintPlanGameConfigDTO> needAddPlanGameConfigDTOS =
            planGameConfigDTOS.stream().filter(a -> !existComplaintChannels.contains(a.getChannel())).toList();
        List<RemindPlanGameConfigBO> needAddPlanGameConfigBOS = new ArrayList<>();
        for (RemindSubPlanBO remindSubPlan : remindSubPlans) {
            for (ComplaintPlanGameConfigDTO planGameConfigDTO : needAddPlanGameConfigDTOS) {

                RemindPlanGameConfigBO addPlanGameConfigBO =
                    RemindPlanGameConfigAppMapping.INSTANCE.remindPlanGameConfigDTO2BO(planGameConfigDTO);
                addPlanGameConfigBO.setGameId("");
                addPlanGameConfigBO.setGameName("");
                addPlanGameConfigBO.setRemindPlanId(remindSubPlan.getRemindPlanId());
                addPlanGameConfigBO.setRemindSubPlanId(remindSubPlan.getId());
                addPlanGameConfigBO.setCreateUserId(adminUser.getUserId());
                addPlanGameConfigBO.setUpdateUserId(adminUser.getUserId());
                needAddPlanGameConfigBOS.add(addPlanGameConfigBO);
            }
        }
        //游戏交付时间配置-删除
        List<RemindPlanGameConfigBO> needDeleteRemindPlanGameConfigBOS =
            allRemindPlanGameConfigBOS.stream().filter(a -> !inputComplaintChannels.contains(a.getChannel()))
                .map(a -> a.setUpdateUserId(adminUser.getUserId())).toList();

        //游戏交付时间配置-修改
        List<RemindPlanGameConfigBO> waitUpdateRemindPlanGameConfigBOS =
            allRemindPlanGameConfigBOS.stream().filter(a -> inputComplaintChannels.contains(a.getChannel())).toList();
        List<RemindPlanGameConfigBO> needUpdateRemindPlanGameConfigBOS = new ArrayList<>();
        for (RemindPlanGameConfigBO waitUpdatePlanGameConfigBO : waitUpdateRemindPlanGameConfigBOS) {
            Integer key = waitUpdatePlanGameConfigBO.getChannel();

            ComplaintPlanGameConfigDTO planGameConfigDTO = inputGameConfigMap.get(key);
            if (null == planGameConfigDTO) {
                continue;
            }
            if (!hasPlanGameConfigChanged(waitUpdatePlanGameConfigBO, planGameConfigDTO)) {
                log.info("planGameConfig no change:{}", waitUpdatePlanGameConfigBO.getId());
                continue;
            }

            waitUpdatePlanGameConfigBO.setExpectCompleteTimeConfig(
                TimeConfigDomainMapping.INSTANCE.timeConfigDTO2BO(planGameConfigDTO.getExpectCompleteTimeConfig()));
            waitUpdatePlanGameConfigBO.setImCountDownTimeConfig(
                TimeConfigDomainMapping.INSTANCE.timeConfigDTO2BO(planGameConfigDTO.getImCountDownTimeConfig()));
            waitUpdatePlanGameConfigBO.setUpdateUserId(adminUser.getUserId());
            waitUpdatePlanGameConfigBO.setUpdateTime(LocalDateTime.now());
            needUpdateRemindPlanGameConfigBOS.add(waitUpdatePlanGameConfigBO);
        }

        return new RemindPlanGameConfigRefreshBO().setNeedAddPlanGameConfigs(needAddPlanGameConfigBOS)
            .setNeedDeletePlanGameConfigs(needDeleteRemindPlanGameConfigBOS)
            .setNeedUpdatePlanGameConfigs(needUpdateRemindPlanGameConfigBOS);
    }

    public static List<PlanGameConfigDTO> gainDistinctPlanGameConfigDTOList(List<RemindPlanGameConfigBO> allRemindPlanGameConfigBOS, RemindPlanBO remindPlanBO) {

        boolean noneMatch = remindPlanBO.getRemindSubPlans().stream()
            .noneMatch(a -> RemindPlanWorkOrderStatusEnum.WAIT_ACCEPT.getValue().equals(a.getWorkOrderStatus()));
        boolean specailWorkOrderStatus = RemindPlanServiceTypeEnum.WORK_ORDER.getValue().equals(remindPlanBO.getServiceType());

        List<PlanGameConfigDTO> planGameConfigDTOS = new ArrayList<>();
        for (RemindPlanGameConfigBO remindPlanGameConfigBO : allRemindPlanGameConfigBOS) {
            PlanGameConfigDTO planGameConfigDTO =
                RemindPlanGameConfigAppMapping.INSTANCE.remindPlanGameConfigBO2DTO(remindPlanGameConfigBO);
            if (specailWorkOrderStatus) {
                Integer onShelfType = remindPlanBO.tryGetRemindSubPlan(remindPlanGameConfigBO.getRemindSubPlanId()).getOnShelfType();
                planGameConfigDTO.setOnShelfType(onShelfType);
            }
            planGameConfigDTOS.add(planGameConfigDTO);
        }
        Map<String, PlanGameConfigDTO> gameIdToFirstConfigDTOMap =
            planGameConfigDTOS.stream().collect(Collectors.toMap(b -> {
                if (specailWorkOrderStatus) {
                    return b.getGameId() + "_" + b.getOnShelfType();
                } else {
                    return b.getGameId();
                }
            }, Function.identity(), (first, second) -> first));

        ArrayList<PlanGameConfigDTO> result = new ArrayList<>(gameIdToFirstConfigDTOMap.values());
        if (!CollectionUtils.isEmpty(result)) {
            result.sort(Comparator.comparing(PlanGameConfigDTO::getGameId));
        }
        return result;
    }

    public static List<ComplaintPlanGameConfigDTO>
        complaintPlanGameConfigDTOList(List<RemindPlanGameConfigBO> allRemindPlanGameConfigBOS) {

        List<ComplaintPlanGameConfigDTO> planGameConfigDTOS = new ArrayList<>();
        for (RemindPlanGameConfigBO remindPlanGameConfigBO : allRemindPlanGameConfigBOS) {
            ComplaintPlanGameConfigDTO planGameConfigDTO =
                RemindPlanGameConfigAppMapping.INSTANCE.complaintRemindPlanGameConfigBO2DTO(remindPlanGameConfigBO);
            planGameConfigDTOS.add(planGameConfigDTO);
        }
        return planGameConfigDTOS;
    }

    public static List<AssPlanGameConfigDTO>
        AssPlanGameConfigDTOList(List<RemindPlanGameConfigBO> allRemindPlanGameConfigBOS) {

        List<AssPlanGameConfigDTO> planGameConfigDTOS = new ArrayList<>();
        for (RemindPlanGameConfigBO remindPlanGameConfigBO : allRemindPlanGameConfigBOS) {
            AssPlanGameConfigDTO planGameConfigDTO =
                RemindPlanGameConfigAppMapping.INSTANCE.assRemindPlanGameConfigBO2DTO(remindPlanGameConfigBO);
            planGameConfigDTOS.add(planGameConfigDTO);
        }
        Map<String, AssPlanGameConfigDTO> gameIdToFirstConfigDTOMap = planGameConfigDTOS.stream()
            .collect(Collectors.toMap(AssPlanGameConfigDTO::getGameId, Function.identity(), (first, second) -> first));

        ArrayList<AssPlanGameConfigDTO> result = new ArrayList<>(gameIdToFirstConfigDTOMap.values());
        if (!CollectionUtils.isEmpty(result)) {
            result.sort(Comparator.comparing(AssPlanGameConfigDTO::getGameId));
        }
        return result;
    }

    private static boolean hasPlanGameConfigChanged(RemindPlanGameConfigBO original, PlanGameConfigDTO modified) {
        if (original == null && modified == null) {
            return false;
        }
        if (original == null || modified == null) {
            return true;
        }

        return !Objects.equals(original.getGameId(), modified.getGameId()) ||
            !Objects.equals(original.getGameName(), modified.getGameName()) ||
            !Objects.equals(original.getMaker(), modified.getMaker()) ||
            hasTimeConfigChanged(original.getExpectCompleteTimeConfig(), modified.getExpectCompleteTimeConfig()) ||
            hasTimeConfigChanged(original.getImCountDownTimeConfig(), modified.getImCountDownTimeConfig());
    }

    private static boolean hasPlanGameConfigChanged(RemindPlanGameConfigBO original, AssPlanGameConfigDTO modified) {
        if (original == null && modified == null) {
            return false;
        }
        if (original == null || modified == null) {
            return true;
        }

        return !Objects.equals(original.getGameId(), modified.getGameId()) ||
            !Objects.equals(original.getGameName(), modified.getGameName()) ||
            hasTimeConfigChanged(original.getExpectCompleteTimeConfig(), modified.getExpectCompleteTimeConfig()) ||
            hasTimeConfigChanged(original.getImCountDownTimeConfig(), modified.getImCountDownTimeConfig());
    }

    private static boolean hasPlanGameConfigChanged(RemindPlanGameConfigBO original, ComplaintPlanGameConfigDTO modified) {
        if (original == null && modified == null) {
            return false;
        }
        if (original == null || modified == null) {
            return true;
        }

        return !Objects.equals(original.getChannel(), modified.getChannel()) ||
            hasTimeConfigChanged(original.getExpectCompleteTimeConfig(), modified.getExpectCompleteTimeConfig()) ||
            hasTimeConfigChanged(original.getImCountDownTimeConfig(), modified.getImCountDownTimeConfig());
    }

    private static boolean hasTimeConfigChanged(TimeConfigBO original, TimeConfigDTO modified) {
        if (original == null && modified == null) {
            return false;
        }
        if (original == null || modified == null) {
            return true;
        }

        return !Objects.equals(original.getHours(), modified.getHours()) ||! Objects.equals(original.getMinutes(),
            modified.getMinutes());
    }
}
