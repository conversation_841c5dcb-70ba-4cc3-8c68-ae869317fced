package com.pxb7.mall.workorder.domain.model;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;


/**
 * 提醒服务预警子计划(RemindSubPlan)实体类
 *
 * <AUTHOR>
 * @since 2025-03-24 21:11:14
 */
public class RemindSubPlanRespBO {


    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class DetailBO {
        private Long id;
        private Long remindPlanId;
        private Integer businessType;
        private Integer workOrderStatus;
        private Integer onShelfType;
        private Integer membership;
        private Integer workOrderType;
        private Integer complaintLevel;
        private LocalDateTime createTime;
        private LocalDateTime updateTime;
        private String createUserId;
        private String updateUserId;
        private Boolean deleted;
    }
}

