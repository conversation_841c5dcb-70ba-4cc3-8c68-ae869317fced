package com.pxb7.mall.workorder.infra.model;

import java.util.Date;
import java.time.*;
import java.math.BigDecimal;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.ToString;


/**
 * 预警计划操作记录表(RemindPlanOperateRecord)实体类
 *
 * <AUTHOR>
 * @since 2025-03-31 20:36:21
 */
public class RemindPlanOperateRecordReqPO {

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class AddPO {


        private String remindPlanId;


        private Integer optType;


        private Integer dataType;


        private String originContent;


        private String newContent;


        private String traceId;


        private String optUserId;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class UpdatePO {


        private Long id;


        private String remindPlanId;


        private Integer optType;


        private Integer dataType;


        private String originContent;


        private String newContent;


        private String traceId;


        private String optUserId;


    }


    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class DelPO {
        private Long id;
    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class SearchPO {

        private String remindPlanId;


        private Integer optType;


        private Integer dataType;


        private String originContent;


        private String newContent;


        private String traceId;


        private String optUserId;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class PagePO {


        private String remindPlanId;


        private Integer optType;


        private Integer dataType;


        private String originContent;


        private String newContent;


        private String traceId;


        private String optUserId;


        /**
         * 页码，从1开始
         */
        private Integer pageIndex;
        /**
         * 每页数量
         */
        private Integer pageSize;

    }

}

