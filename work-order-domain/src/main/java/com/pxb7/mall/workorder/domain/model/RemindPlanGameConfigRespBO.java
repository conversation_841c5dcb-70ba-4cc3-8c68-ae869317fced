package com.pxb7.mall.workorder.domain.model;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;


/**
 * 预警计划游戏配置(RemindPlanGameConfig)实体类
 *
 * <AUTHOR>
 * @since 2025-03-24 21:11:14
 */
public class RemindPlanGameConfigRespBO {


    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class DetailBO {
        private Long id;
        private String gameId;
        private String gameName;
        private Integer maker;
        private Integer channel;
        private String expectCompleteTimeConfig;
        private String imCountDownTimeConfig;
        private Long remindPlanId;
        private Long remindSubPlanId;
        private LocalDateTime createTime;
        private LocalDateTime updateTime;
        private String createUserId;
        private String updateUserId;
        private Boolean deleted;
    }
}

