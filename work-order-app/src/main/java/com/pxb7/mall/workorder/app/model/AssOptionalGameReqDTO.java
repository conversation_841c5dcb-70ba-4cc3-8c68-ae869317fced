package com.pxb7.mall.workorder.app.model;

import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.List;

@Getter
@Setter
@Accessors(chain = true)
@ToString
public class AssOptionalGameReqDTO {

    /**
     * 服务类型: 1：账号交付 2:商品工单 3:售后工单 4:客诉工单
     */
    @NotNull
    private Integer serviceType;

    /**
     * 工单类型，1:找回，2:纠纷
     */
    @NotNull
    private Integer workOrderType;

    /**
     * 模糊查询-游戏名称
     */
    private String gameName;

    /**
     * 订单来源(会员类型)：1 散户工单 2 号商工单 3 3A工单，服务类型为商品工单有值
     */
    private Integer membership;

}
