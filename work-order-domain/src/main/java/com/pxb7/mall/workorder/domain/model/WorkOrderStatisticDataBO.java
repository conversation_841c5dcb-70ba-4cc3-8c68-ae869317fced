package com.pxb7.mall.workorder.domain.model;

import lombok.Data;

import java.math.BigDecimal;
import java.math.RoundingMode;

@Data
public class WorkOrderStatisticDataBO {

    /**
     * 日期
     */
    private String date;

    /**
     * 游戏id 列表
     */
    private String gameId;

    /**
     * 上架方式：1:官方截图，2:自主截图
     */
    private Integer onShelfType;

    /**
     * 客服id
     */
    private String customerCareId;

    /**
     * 接单美工Id
     */
    private String artDesignerId;

    /**
     * 跟进人Id
     */
    private String followerId;


    /**
     * 处理总数量（待接单总数/已跟进总数...）
     */
    private Long processTotalCount;

    /**
     * 已完成数量 （已接单数/已完结数...）
     */
    private Long completeCount;


    /**
     *  已失效数量
     */
    private Long invalidCount;


    /**
     * 即将超时 (待接单即将超时/已接单即将超时/待跟进即将超时...)
     */
    private Long willTimeoutCount;

    /**
     * 已超时数量  (待接单已超时/已接单已超时/待跟进已超时...)
     */
    private Long timeoutCount;




    /**
     * 超时率= 已超时数/交付中房间数
     *
     * timeoutCount/processTotalCount
     */
    public BigDecimal getTimeoutRate(){
        if (timeoutCount == null) {
            return BigDecimal.ZERO;
        }
        if (processTotalCount == null || processTotalCount <= 0) {
            return null; // 处理分母为零的情况
        }
        // 计算超时率
        try {
            BigDecimal timeout = BigDecimal.valueOf(timeoutCount);
            BigDecimal processTotalCount = BigDecimal.valueOf(this.processTotalCount);

            String timeoutRateStr = timeout.divide(processTotalCount, 4, RoundingMode.HALF_UP) // 设置精度和舍入模式
                .multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP).stripTrailingZeros()
                .toPlainString();

            return new BigDecimal(timeoutRateStr); // 确保最终结果的精度
        } catch (ArithmeticException | NumberFormatException e) {
            // 捕获可能的异常并返回 null
            return null;
        }
    }









}
