package com.pxb7.mall.workorder.infra.repository.db.mapper;

import com.pxb7.mall.workorder.infra.repository.db.entity.RemindDeliveryProduct;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 账号交付预警记录(RemindDeliveryProduct)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-03-27 20:07:07
 */
@Mapper
public interface RemindDeliveryProductMapper extends BaseMapper<RemindDeliveryProduct> {
    /**
     * 批量新增数据
     *
     * @param entities List<RemindDeliveryProduct> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<RemindDeliveryProduct> entities);

    /**
     * 批量修改超时状态
     * @param ids
     * @param status
     */
    int batchUpdateTimeoutStatusByIds(List<Long> ids, Integer status);

}

