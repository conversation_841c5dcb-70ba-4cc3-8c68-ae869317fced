package com.pxb7.mall.workorder.infra.repository.gateway.dubbo.user;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.fastjson2.JSON;
import com.pxb7.mall.user.api.UserServiceI;
import com.pxb7.mall.user.dto.response.user.UserShortInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Repository;

import java.util.*;

/**
 * <AUTHOR>
 * @version : UserGateway.java, v 0.1 2025年04月24日 10:45 yang.xuexi Exp $
 */
@Repository
@Slf4j
public class UserGateway {

    @DubboReference(providedBy = "user-c", timeout = 2000)
    private UserServiceI userServiceI;


    public List<UserShortInfoDTO> queryUserInfoList(Set<String> userIdSet) {
        if (CollUtil.isEmpty(userIdSet)) {
            log.warn("userIdSet is empty");
            return Collections.emptyList();
        }
        try {
            MultiResponse<UserShortInfoDTO> userShortInfo = userServiceI.getUserShortInfo(new ArrayList<>(userIdSet));
            if (Objects.isNull(userShortInfo) || !userShortInfo.isSuccess() || CollUtil.isEmpty(userShortInfo.getData())) {
                log.error("getUserShortInfo fail:{}", JSON.toJSONString(userIdSet));
            } else {
                return userShortInfo.getData();
            }
        } catch (Exception e) {
            log.error("getUserShortInfo fail:{}", JSON.toJSONString(userIdSet));
        }

        return Collections.emptyList();
    }
}
