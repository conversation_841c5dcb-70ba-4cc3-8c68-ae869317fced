package com.pxb7.mall.workorder.app.mapping;

import com.pxb7.mall.workorder.app.model.AfterSalePageDetailDTO;
import com.pxb7.mall.workorder.app.model.AfterSaleStatisticDataDTO;
import com.pxb7.mall.workorder.app.model.AfterSaleStatisticDataSearchDTO;
import com.pxb7.mall.workorder.app.model.RemindAfterSaleReqDTO;
import com.pxb7.mall.workorder.app.model.RemindAfterSaleRespDTO;
import com.pxb7.mall.workorder.domain.model.AfterSaleStatisticDataBO;
import com.pxb7.mall.workorder.domain.model.AfterSaleStatisticDataSearchBO;
import com.pxb7.mall.workorder.domain.model.RemindAfterSaleBO;
import com.pxb7.mall.workorder.domain.model.RemindAfterSaleReqBO;
import com.pxb7.mall.workorder.domain.model.RemindAfterSaleRespBO;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

@Mapper
public interface RemindAfterSaleAppMapping {

    RemindAfterSaleAppMapping INSTANCE = Mappers.getMapper(RemindAfterSaleAppMapping.class);

    RemindAfterSaleReqBO.AddBO remindAfterSaleDTO2AddBO(RemindAfterSaleReqDTO.AddDTO source);

    RemindAfterSaleReqBO.UpdateBO remindAfterSaleDTO2UpdateBO(RemindAfterSaleReqDTO.UpdateDTO source);

    RemindAfterSaleReqBO.DelBO remindAfterSaleDTO2DelBO(RemindAfterSaleReqDTO.DelDTO source);

    RemindAfterSaleReqBO.SearchBO remindAfterSaleDTO2SearchBO(RemindAfterSaleReqDTO.SearchDTO source);

    RemindAfterSaleReqBO.PageBO remindAfterSaleDTO2PageBO(RemindAfterSaleReqDTO.PageDTO source);

    RemindAfterSaleRespDTO.DetailDTO remindAfterSaleBO2DetailDTO(RemindAfterSaleRespBO.DetailBO source);

    List<RemindAfterSaleRespDTO.DetailDTO> remindAfterSaleBO2ListDTO(List<RemindAfterSaleRespBO.DetailBO> source);

    Page<AfterSalePageDetailDTO> remindAfterSaleBO2PageDTO(Page<RemindAfterSaleBO> page);

    AfterSaleStatisticDataSearchBO statisticParam2BO(AfterSaleStatisticDataSearchDTO param);

    List<AfterSaleStatisticDataDTO> statisticResul2DTOList(List<AfterSaleStatisticDataBO> list);
}
