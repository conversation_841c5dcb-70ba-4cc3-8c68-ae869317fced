package com.pxb7.mall.workorder.app.model;

import java.util.List;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.ToString;


/**
 * 提醒服务预警计划(RemindPlan)实体类
 *
 * <AUTHOR>
 * @since 2025-03-24 21:12:16
 */
public class RemindPlanRespDTO {


    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class DetailDTO {

        /**
         * 预警计划Id
         */
        private Long id;

        /**
         * 预警计划名称
         */
        private String planName;

        /**
         * 业务类型
         */
        private Integer serviceType;


        /**
         * 业务类型，1:代售，2:中介，服务类型为账号交付有值
         */
        private List<Integer> businessTypes;

        /**
         *  工单状态：1:待接单，2:已接单，3:待跟进，服务类型为商品工单有值
         */
        private List<Integer> workOrderStatuses;

        /**
         * 上架方式：1:官方截图，2:自主截图，服务类型为商品工单且 不为“待接单”时有值
         */
        private List<Integer> onShelfTypes;

        /**
         * 订单来源(会员类型)：1 散户工单 2 号商工单 3 3A工单，服务类型为商品工单有值
         */
        private List<Integer> memberships;

        /**
         * 订单来源(会员类型)：1 散户工单 2 号商工单 3 3A工单，服务类型为商品工单有值
         */
        private Integer membership;

        /**
         * 工单类型  1:找回 2:纠纷 服务类型为售后工单有值
         */
        private Integer workOrderType;

        /**
         * 投诉级别：1:一级，2:二级，3:三级，4:四级，5:五级，6:六级，服务类型为客诉工单有值
         */
        private Integer complaintLevel;

        /**
         * 预警游戏配置
         */
        private List<PlanGameConfigDTO> planGameConfigs;

        /**
         * 预警规则
         */
        private List<PlanRuleDTO> planRules;


        /**
         * 免打扰时间段 {"startTime":"22:00","endTime":"08:00"}
         */
        //@NotBlank(message = "免打扰时间段不能为空")
        private NotDisturbPeriodDTO notDisturbPeriod;
    }
}

