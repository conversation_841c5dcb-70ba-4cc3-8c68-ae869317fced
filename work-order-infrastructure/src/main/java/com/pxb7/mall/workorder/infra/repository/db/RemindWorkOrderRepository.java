package com.pxb7.mall.workorder.infra.repository.db;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.pxb7.mall.workorder.infra.model.RemindWorkOrderReqPO;
import com.pxb7.mall.workorder.infra.repository.db.entity.RemindWorkOrder;

import java.util.List;

/**
 * 商品工单预警记录(RemindWorkOrder)表服务接口
 *
 * <AUTHOR>
 * @since 2025-04-07 11:58:55
 */
public interface RemindWorkOrderRepository extends IService<RemindWorkOrder> {

    boolean insert(RemindWorkOrderReqPO.AddPO param);

    RemindWorkOrder getByRemindId(String remindId);

    boolean deleteByWorkOrderId(String workOrderId);

    boolean updateByWorkOrderId(RemindWorkOrderReqPO.UpdatePO param);

    RemindWorkOrder findByWorkOrderId(String workOrderId, String remindId);

    Page<RemindWorkOrder> page(RemindWorkOrderReqPO.PagePO pagePO);

    boolean batchUpdateTimeoutStatusByIds(List<Long> ids, Integer status);

    boolean existFollowUpStatusWorkOrder(String workOrderId);
}

